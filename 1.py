# pip install deepface opencv-python pillow
from deepface import DeepFace
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont

# 英文表情到中文的映射
emotion_mapping = {
    'angry': '愤怒',
    'disgust': '厌恶',
    'fear': '恐惧',
    'happy': '开心',
    'sad': '悲伤',
    'surprise': '惊讶',
    'neutral': '平静'
}

def put_chinese_text(img, text, position, font_size=30, color=(255, 255, 255)):
    """在图像上绘制中文文字"""
    # 将OpenCV图像转换为PIL图像
    img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(img_pil)

    # 尝试使用系统字体，如果失败则使用默认字体
    try:
        # macOS系统中文字体
        font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", font_size)
    except:
        try:
            # Windows系统中文字体
            font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", font_size)
        except:
            try:
                # Linux系统中文字体
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", font_size)
            except:
                # 使用默认字体
                font = ImageFont.load_default()

    # 绘制文字
    draw.text(position, text, font=font, fill=color)

    # 转换回OpenCV格式
    img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
    return img_cv

cap = cv2.VideoCapture(0)
while True:
    ret, frame = cap.read()
    if not ret:
        break
    # DeepFace 分析，actions 可以包含 'emotion','age','gender' 等
    result = DeepFace.analyze(frame, actions=['emotion'], enforce_detection=False)
    # result 是 list，取第一个元素（dict），然后取 emotion 概览
    emotion_en = result[0]['dominant_emotion']
    # 转换为中文
    emotion_cn = emotion_mapping.get(emotion_en, emotion_en)
    # 使用自定义函数绘制中文文字
    frame = put_chinese_text(frame, emotion_cn, (30, 30), font_size=40)
    cv2.imshow("emotion", frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break
cap.release()
cv2.destroyAllWindows()
