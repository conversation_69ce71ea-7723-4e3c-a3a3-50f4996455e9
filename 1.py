# pip install deepface opencv-python
from deepface import DeepFace
import cv2

# 英文表情到中文的映射
emotion_mapping_cn = {
    'angry': '愤怒',
    'disgust': '厌恶',
    'fear': '恐惧',
    'happy': '开心',
    'sad': '悲伤',
    'surprise': '惊讶',
    'neutral': '平静'
}

# 英文表情显示映射（用于视频窗口）
emotion_mapping_en = {
    'angry': 'ANGRY',
    'disgust': 'DISGUST',
    'fear': 'FEAR',
    'happy': 'HAPPY',
    'sad': 'SAD',
    'surprise': 'SURPRISE',
    'neutral': 'NEUTRAL'
}

cap = cv2.VideoCapture(0)
while True:
    ret, frame = cap.read()
    if not ret:
        break
    # DeepFace 分析，actions 可以包含 'emotion','age','gender' 等
    result = DeepFace.analyze(frame, actions=['emotion'], enforce_detection=False)
    # result 是 list，取第一个元素（dict），然后取 emotion 概览
    emotion_en = result[0]['dominant_emotion']

    # 获取中文和英文显示
    emotion_cn = emotion_mapping_cn.get(emotion_en, emotion_en)
    emotion_display = emotion_mapping_en.get(emotion_en, emotion_en.upper())

    # 在终端打印中文表情
    print(f"检测到表情: {emotion_cn}")

    # 在视频窗口显示大号英文
    cv2.putText(frame, emotion_display, (30, 80), cv2.FONT_HERSHEY_SIMPLEX, 2.5, (0, 255, 0), 4)
    cv2.imshow("emotion", frame)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break
cap.release()
cv2.destroyAllWindows()
