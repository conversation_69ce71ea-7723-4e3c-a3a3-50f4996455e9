"""AUTOGENERATED. DO NOT EDIT."""

from tf_keras.src.losses import BinaryCrossentropy
from tf_keras.src.losses import BinaryFocalCrossentropy
from tf_keras.src.losses import CategoricalCrossentropy
from tf_keras.src.losses import CategoricalFocalCrossentropy
from tf_keras.src.losses import CategoricalHinge
from tf_keras.src.losses import CosineSimilarity
from tf_keras.src.losses import Hinge
from tf_keras.src.losses import Huber
from tf_keras.src.losses import KLDivergence
from tf_keras.src.losses import LogCosh
from tf_keras.src.losses import Loss
from tf_keras.src.losses import MeanAbsoluteError
from tf_keras.src.losses import MeanAbsolutePercentageError
from tf_keras.src.losses import MeanSquaredError
from tf_keras.src.losses import MeanSquaredLogarithmicError
from tf_keras.src.losses import Poisson
from tf_keras.src.losses import SparseCategoricalCrossentropy
from tf_keras.src.losses import SquaredHinge
from tf_keras.src.losses import binary_crossentropy
from tf_keras.src.losses import binary_focal_crossentropy
from tf_keras.src.losses import categorical_crossentropy
from tf_keras.src.losses import categorical_focal_crossentropy
from tf_keras.src.losses import categorical_hinge
from tf_keras.src.losses import cosine_similarity
from tf_keras.src.losses import cosine_similarity as cosine
from tf_keras.src.losses import cosine_similarity as cosine_proximity
from tf_keras.src.losses import deserialize
from tf_keras.src.losses import get
from tf_keras.src.losses import hinge
from tf_keras.src.losses import kl_divergence
from tf_keras.src.losses import kl_divergence as KLD
from tf_keras.src.losses import kl_divergence as kld
from tf_keras.src.losses import kl_divergence as kullback_leibler_divergence
from tf_keras.src.losses import log_cosh
from tf_keras.src.losses import log_cosh as logcosh
from tf_keras.src.losses import mean_absolute_error
from tf_keras.src.losses import mean_absolute_error as MAE
from tf_keras.src.losses import mean_absolute_error as mae
from tf_keras.src.losses import mean_absolute_percentage_error
from tf_keras.src.losses import mean_absolute_percentage_error as MAPE
from tf_keras.src.losses import mean_absolute_percentage_error as mape
from tf_keras.src.losses import mean_squared_error
from tf_keras.src.losses import mean_squared_error as MSE
from tf_keras.src.losses import mean_squared_error as mse
from tf_keras.src.losses import mean_squared_logarithmic_error
from tf_keras.src.losses import mean_squared_logarithmic_error as MSLE
from tf_keras.src.losses import mean_squared_logarithmic_error as msle
from tf_keras.src.losses import poisson
from tf_keras.src.losses import serialize
from tf_keras.src.losses import sparse_categorical_crossentropy
from tf_keras.src.losses import squared_hinge
