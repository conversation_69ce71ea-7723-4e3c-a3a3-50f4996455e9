# Copyright 2019 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Constants for TF-Keras SavedModel serialization."""

# Namespace used to store all attributes added during serialization.
# e.g. the list of layers can be accessed using `loaded.keras_api.layers`, in an
# object loaded from `tf.saved_model.load()`.
KERAS_ATTR = "keras_api"

# Keys for the serialization cache.
# Maps to the keras serialization dict {Layer --> SerializedAttributes object}
KERAS_CACHE_KEY = "keras_serialized_attributes"


# Name of TF-Keras metadata file stored in the SavedModel.
SAVED_METADATA_PATH = "keras_metadata.pb"

# Names of SavedObject TF-Keras identifiers.
INPUT_LAYER_IDENTIFIER = "_tf_keras_input_layer"
LAYER_IDENTIFIER = "_tf_keras_layer"
METRIC_IDENTIFIER = "_tf_keras_metric"
MODEL_IDENTIFIER = "_tf_keras_model"
NETWORK_IDENTIFIER = "_tf_keras_network"
RNN_LAYER_IDENTIFIER = "_tf_keras_rnn_layer"
SEQUENTIAL_IDENTIFIER = "_tf_keras_sequential"

KERAS_OBJECT_IDENTIFIERS = (
    INPUT_LAYER_IDENTIFIER,
    LAYER_IDENTIFIER,
    METRIC_IDENTIFIER,
    MODEL_IDENTIFIER,
    NETWORK_IDENTIFIER,
    RNN_LAYER_IDENTIFIER,
    SEQUENTIAL_IDENTIFIER,
)

