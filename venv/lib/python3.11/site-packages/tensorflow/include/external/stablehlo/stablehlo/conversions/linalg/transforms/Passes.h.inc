/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_STABLEHLOLEGALIZETOLINALGPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// StablehloLegalizeToLinalgPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_STABLEHLOLEGALIZETOLINALGPASS
struct StablehloLegalizeToLinalgPassOptions {
  bool enablePrimitiveOps = false;
  bool enableSparseOps = false;
};
std::unique_ptr<::mlir::Pass> createStablehloLegalizeToLinalgPass();
std::unique_ptr<::mlir::Pass> createStablehloLegalizeToLinalgPass(StablehloLegalizeToLinalgPassOptions options);
#undef GEN_PASS_DECL_STABLEHLOLEGALIZETOLINALGPASS
#endif // GEN_PASS_DECL_STABLEHLOLEGALIZETOLINALGPASS
#ifdef GEN_PASS_DEF_STABLEHLOLEGALIZETOLINALGPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloLegalizeToLinalgPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createStablehloLegalizeToLinalgPass(StablehloLegalizeToLinalgPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class StablehloLegalizeToLinalgPassBase : public ::mlir::OperationPass<> {
public:
  using Base = StablehloLegalizeToLinalgPassBase;

  StablehloLegalizeToLinalgPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeToLinalgPassBase(const StablehloLegalizeToLinalgPassBase &other) : ::mlir::OperationPass<>(other) {}
  StablehloLegalizeToLinalgPassBase& operator=(const StablehloLegalizeToLinalgPassBase &) = delete;
  StablehloLegalizeToLinalgPassBase(StablehloLegalizeToLinalgPassBase &&) = delete;
  StablehloLegalizeToLinalgPassBase& operator=(StablehloLegalizeToLinalgPassBase &&) = delete;
  ~StablehloLegalizeToLinalgPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Legalize StableHLO to LinAlg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeToLinalgPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeToLinalgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::bufferization::BufferizationDialect>();
    registry.insert<mlir::complex::ComplexDialect>();
    registry.insert<mlir::linalg::LinalgDialect>();
    registry.insert<mlir::math::MathDialect>();
    registry.insert<mlir::memref::MemRefDialect>();
    registry.insert<mlir::scf::SCFDialect>();
    registry.insert<mlir::shape::ShapeDialect>();
    registry.insert<mlir::sparse_tensor::SparseTensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeToLinalgPassBase<DerivedT>)

  StablehloLegalizeToLinalgPassBase(StablehloLegalizeToLinalgPassOptions options) : StablehloLegalizeToLinalgPassBase() {
    enablePrimitiveOps = std::move(options.enablePrimitiveOps);
    enableSparseOps = std::move(options.enableSparseOps);
  }
protected:
  ::mlir::Pass::Option<bool> enablePrimitiveOps{*this, "enable-primitive-ops", ::llvm::cl::desc("Lower to primitive Linalg ops (map, reduce and transpose) when possible, instead of linalg.generic"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> enableSparseOps{*this, "enable-sparse-ops", ::llvm::cl::desc("Lower to Sparse Tensor ops (sparse_tensor.concatenate)when possible, instead of linalg.generic"), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createStablehloLegalizeToLinalgPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createStablehloLegalizeToLinalgPass(StablehloLegalizeToLinalgPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createStablehloLegalizeToLinalgPass() {
  return impl::createStablehloLegalizeToLinalgPass();
}

std::unique_ptr<::mlir::Pass> createStablehloLegalizeToLinalgPass(StablehloLegalizeToLinalgPassOptions options) {
  return impl::createStablehloLegalizeToLinalgPass(std::move(options));
}
#undef GEN_PASS_DEF_STABLEHLOLEGALIZETOLINALGPASS
#endif // GEN_PASS_DEF_STABLEHLOLEGALIZETOLINALGPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// StablehloLegalizeToLinalgPass Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloLegalizeToLinalgPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeToLinalgPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerStablehloLegalizeToLinalgPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createStablehloLegalizeToLinalgPass();
  });
}

//===----------------------------------------------------------------------===//
// StablehloLinalgTransforms Registration
//===----------------------------------------------------------------------===//

inline void registerStablehloLinalgTransformsPasses() {
  registerStablehloLegalizeToLinalgPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class StablehloLegalizeToLinalgPassBase : public ::mlir::OperationPass<> {
public:
  using Base = StablehloLegalizeToLinalgPassBase;

  StablehloLegalizeToLinalgPassBase() : ::mlir::OperationPass<>(::mlir::TypeID::get<DerivedT>()) {}
  StablehloLegalizeToLinalgPassBase(const StablehloLegalizeToLinalgPassBase &other) : ::mlir::OperationPass<>(other) {}
  StablehloLegalizeToLinalgPassBase& operator=(const StablehloLegalizeToLinalgPassBase &) = delete;
  StablehloLegalizeToLinalgPassBase(StablehloLegalizeToLinalgPassBase &&) = delete;
  StablehloLegalizeToLinalgPassBase& operator=(StablehloLegalizeToLinalgPassBase &&) = delete;
  ~StablehloLegalizeToLinalgPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("stablehlo-legalize-to-linalg");
  }
  ::llvm::StringRef getArgument() const override { return "stablehlo-legalize-to-linalg"; }

  ::llvm::StringRef getDescription() const override { return "Legalize StableHLO to LinAlg"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("StablehloLegalizeToLinalgPass");
  }
  ::llvm::StringRef getName() const override { return "StablehloLegalizeToLinalgPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::bufferization::BufferizationDialect>();
    registry.insert<mlir::complex::ComplexDialect>();
    registry.insert<mlir::linalg::LinalgDialect>();
    registry.insert<mlir::math::MathDialect>();
    registry.insert<mlir::memref::MemRefDialect>();
    registry.insert<mlir::scf::SCFDialect>();
    registry.insert<mlir::shape::ShapeDialect>();
    registry.insert<mlir::sparse_tensor::SparseTensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(StablehloLegalizeToLinalgPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> enablePrimitiveOps{*this, "enable-primitive-ops", ::llvm::cl::desc("Lower to primitive Linalg ops (map, reduce and transpose) when possible, instead of linalg.generic"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<bool> enableSparseOps{*this, "enable-sparse-ops", ::llvm::cl::desc("Lower to Sparse Tensor ops (sparse_tensor.concatenate)when possible, instead of linalg.generic"), ::llvm::cl::init(false)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
