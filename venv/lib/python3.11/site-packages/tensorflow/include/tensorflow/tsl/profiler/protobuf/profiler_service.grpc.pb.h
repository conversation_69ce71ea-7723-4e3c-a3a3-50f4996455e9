// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tsl/profiler/protobuf/profiler_service.proto
#ifndef GRPC_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto__INCLUDED
#define GRPC_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto__INCLUDED

#include "tsl/profiler/protobuf/profiler_service.pb.h"

#include <functional>
#include <grpc/impl/codegen/port_platform.h>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace tensorflow {


namespace grpc {

// The ProfilerService service retrieves performance information about
// the programs running on connected devices over a period of time.
class ProfilerService final {
 public:
  static constexpr char const* service_full_name() {
    return "tensorflow.ProfilerService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Starts a profiling session, blocks until it completes, and returns data.
    virtual ::grpc::Status Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::tensorflow::ProfileResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileResponse>> AsyncProfile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileResponse>>(AsyncProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileResponse>> PrepareAsyncProfile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileResponse>>(PrepareAsyncProfileRaw(context, request, cq));
    }
    // Signal to terminate the Profile rpc for a on-going profiling session,
    // The Profile rpc will return successfully and prematurely without timeout.
    // This is used by programmatic mode to end the session in workers.
    virtual ::grpc::Status Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::tensorflow::TerminateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TerminateResponse>> AsyncTerminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TerminateResponse>>(AsyncTerminateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TerminateResponse>> PrepareAsyncTerminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TerminateResponse>>(PrepareAsyncTerminateRaw(context, request, cq));
    }
    // Collects profiling data and returns user-friendly metrics.
    virtual ::grpc::Status Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::tensorflow::MonitorResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::MonitorResponse>> AsyncMonitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::MonitorResponse>>(AsyncMonitorRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::MonitorResponse>> PrepareAsyncMonitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::MonitorResponse>>(PrepareAsyncMonitorRaw(context, request, cq));
    }
    class experimental_async_interface {
     public:
      virtual ~experimental_async_interface() {}
      // Starts a profiling session, blocks until it completes, and returns data.
      virtual void Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Profile(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Profile(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Profile(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Signal to terminate the Profile rpc for a on-going profiling session,
      // The Profile rpc will return successfully and prematurely without timeout.
      // This is used by programmatic mode to end the session in workers.
      virtual void Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Terminate(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TerminateResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Terminate(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TerminateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Terminate(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TerminateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // Collects profiling data and returns user-friendly metrics.
      virtual void Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Monitor(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::MonitorResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Monitor(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::MonitorResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Monitor(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::MonitorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
    };
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    typedef class experimental_async_interface async_interface;
    #endif
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    async_interface* async() { return experimental_async(); }
    #endif
    virtual class experimental_async_interface* experimental_async() { return nullptr; }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileResponse>* AsyncProfileRaw(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileResponse>* PrepareAsyncProfileRaw(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TerminateResponse>* AsyncTerminateRaw(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TerminateResponse>* PrepareAsyncTerminateRaw(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::MonitorResponse>* AsyncMonitorRaw(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::MonitorResponse>* PrepareAsyncMonitorRaw(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::tensorflow::ProfileResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileResponse>> AsyncProfile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileResponse>>(AsyncProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileResponse>> PrepareAsyncProfile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileResponse>>(PrepareAsyncProfileRaw(context, request, cq));
    }
    ::grpc::Status Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::tensorflow::TerminateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::TerminateResponse>> AsyncTerminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::TerminateResponse>>(AsyncTerminateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::TerminateResponse>> PrepareAsyncTerminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::TerminateResponse>>(PrepareAsyncTerminateRaw(context, request, cq));
    }
    ::grpc::Status Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::tensorflow::MonitorResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::MonitorResponse>> AsyncMonitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::MonitorResponse>>(AsyncMonitorRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::MonitorResponse>> PrepareAsyncMonitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::MonitorResponse>>(PrepareAsyncMonitorRaw(context, request, cq));
    }
    class experimental_async final :
      public StubInterface::experimental_async_interface {
     public:
      void Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response, std::function<void(::grpc::Status)>) override;
      void Profile(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Profile(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Profile(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response, std::function<void(::grpc::Status)>) override;
      void Terminate(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TerminateResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Terminate(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TerminateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Terminate(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TerminateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response, std::function<void(::grpc::Status)>) override;
      void Monitor(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::MonitorResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Monitor(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::MonitorResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Monitor(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::MonitorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
     private:
      friend class Stub;
      explicit experimental_async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class experimental_async_interface* experimental_async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class experimental_async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileResponse>* AsyncProfileRaw(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileResponse>* PrepareAsyncProfileRaw(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::TerminateResponse>* AsyncTerminateRaw(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::TerminateResponse>* PrepareAsyncTerminateRaw(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::MonitorResponse>* AsyncMonitorRaw(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::MonitorResponse>* PrepareAsyncMonitorRaw(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Profile_;
    const ::grpc::internal::RpcMethod rpcmethod_Terminate_;
    const ::grpc::internal::RpcMethod rpcmethod_Monitor_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Starts a profiling session, blocks until it completes, and returns data.
    virtual ::grpc::Status Profile(::grpc::ServerContext* context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response);
    // Signal to terminate the Profile rpc for a on-going profiling session,
    // The Profile rpc will return successfully and prematurely without timeout.
    // This is used by programmatic mode to end the session in workers.
    virtual ::grpc::Status Terminate(::grpc::ServerContext* context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response);
    // Collects profiling data and returns user-friendly metrics.
    virtual ::grpc::Status Monitor(::grpc::ServerContext* context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Profile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Profile() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Profile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Profile(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileRequest* /*request*/, ::tensorflow::ProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestProfile(::grpc::ServerContext* context, ::tensorflow::ProfileRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::ProfileResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_Terminate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Terminate() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_Terminate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Terminate(::grpc::ServerContext* /*context*/, const ::tensorflow::TerminateRequest* /*request*/, ::tensorflow::TerminateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestTerminate(::grpc::ServerContext* context, ::tensorflow::TerminateRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::TerminateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_Monitor : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Monitor() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_Monitor() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Monitor(::grpc::ServerContext* /*context*/, const ::tensorflow::MonitorRequest* /*request*/, ::tensorflow::MonitorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMonitor(::grpc::ServerContext* context, ::tensorflow::MonitorRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::MonitorResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Profile<WithAsyncMethod_Terminate<WithAsyncMethod_Monitor<Service > > > AsyncService;
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_Profile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_Profile() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ProfileRequest, ::tensorflow::ProfileResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response) { return this->Profile(context, request, response); }));}
    void SetMessageAllocatorFor_Profile(
        ::grpc::experimental::MessageAllocator< ::tensorflow::ProfileRequest, ::tensorflow::ProfileResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(0);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::ProfileRequest, ::tensorflow::ProfileResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_Profile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Profile(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileRequest* /*request*/, ::tensorflow::ProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Profile(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::ProfileRequest* /*request*/, ::tensorflow::ProfileResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Profile(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::ProfileRequest* /*request*/, ::tensorflow::ProfileResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_Terminate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_Terminate() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(1,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::TerminateRequest, ::tensorflow::TerminateResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response) { return this->Terminate(context, request, response); }));}
    void SetMessageAllocatorFor_Terminate(
        ::grpc::experimental::MessageAllocator< ::tensorflow::TerminateRequest, ::tensorflow::TerminateResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(1);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::TerminateRequest, ::tensorflow::TerminateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_Terminate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Terminate(::grpc::ServerContext* /*context*/, const ::tensorflow::TerminateRequest* /*request*/, ::tensorflow::TerminateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Terminate(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::TerminateRequest* /*request*/, ::tensorflow::TerminateResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Terminate(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::TerminateRequest* /*request*/, ::tensorflow::TerminateResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_Monitor : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_Monitor() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(2,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::MonitorRequest, ::tensorflow::MonitorResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response) { return this->Monitor(context, request, response); }));}
    void SetMessageAllocatorFor_Monitor(
        ::grpc::experimental::MessageAllocator< ::tensorflow::MonitorRequest, ::tensorflow::MonitorResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(2);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::MonitorRequest, ::tensorflow::MonitorResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_Monitor() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Monitor(::grpc::ServerContext* /*context*/, const ::tensorflow::MonitorRequest* /*request*/, ::tensorflow::MonitorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Monitor(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::MonitorRequest* /*request*/, ::tensorflow::MonitorResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Monitor(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::MonitorRequest* /*request*/, ::tensorflow::MonitorResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
  typedef ExperimentalWithCallbackMethod_Profile<ExperimentalWithCallbackMethod_Terminate<ExperimentalWithCallbackMethod_Monitor<Service > > > CallbackService;
  #endif

  typedef ExperimentalWithCallbackMethod_Profile<ExperimentalWithCallbackMethod_Terminate<ExperimentalWithCallbackMethod_Monitor<Service > > > ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Profile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Profile() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Profile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Profile(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileRequest* /*request*/, ::tensorflow::ProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_Terminate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Terminate() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_Terminate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Terminate(::grpc::ServerContext* /*context*/, const ::tensorflow::TerminateRequest* /*request*/, ::tensorflow::TerminateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_Monitor : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Monitor() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_Monitor() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Monitor(::grpc::ServerContext* /*context*/, const ::tensorflow::MonitorRequest* /*request*/, ::tensorflow::MonitorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Profile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Profile() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Profile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Profile(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileRequest* /*request*/, ::tensorflow::ProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestProfile(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_Terminate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Terminate() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_Terminate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Terminate(::grpc::ServerContext* /*context*/, const ::tensorflow::TerminateRequest* /*request*/, ::tensorflow::TerminateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestTerminate(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_Monitor : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Monitor() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_Monitor() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Monitor(::grpc::ServerContext* /*context*/, const ::tensorflow::MonitorRequest* /*request*/, ::tensorflow::MonitorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMonitor(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_Profile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_Profile() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Profile(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_Profile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Profile(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileRequest* /*request*/, ::tensorflow::ProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Profile(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Profile(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_Terminate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_Terminate() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(1,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Terminate(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_Terminate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Terminate(::grpc::ServerContext* /*context*/, const ::tensorflow::TerminateRequest* /*request*/, ::tensorflow::TerminateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Terminate(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Terminate(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_Monitor : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_Monitor() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(2,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Monitor(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_Monitor() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Monitor(::grpc::ServerContext* /*context*/, const ::tensorflow::MonitorRequest* /*request*/, ::tensorflow::MonitorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Monitor(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Monitor(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Profile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Profile() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::ProfileRequest, ::tensorflow::ProfileResponse>(std::bind(&WithStreamedUnaryMethod_Profile<BaseClass>::StreamedProfile, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_Profile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Profile(::grpc::ServerContext* /*context*/, const ::tensorflow::ProfileRequest* /*request*/, ::tensorflow::ProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedProfile(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::ProfileRequest,::tensorflow::ProfileResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Terminate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Terminate() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::TerminateRequest, ::tensorflow::TerminateResponse>(std::bind(&WithStreamedUnaryMethod_Terminate<BaseClass>::StreamedTerminate, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_Terminate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Terminate(::grpc::ServerContext* /*context*/, const ::tensorflow::TerminateRequest* /*request*/, ::tensorflow::TerminateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedTerminate(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::TerminateRequest,::tensorflow::TerminateResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Monitor : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Monitor() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::MonitorRequest, ::tensorflow::MonitorResponse>(std::bind(&WithStreamedUnaryMethod_Monitor<BaseClass>::StreamedMonitor, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_Monitor() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Monitor(::grpc::ServerContext* /*context*/, const ::tensorflow::MonitorRequest* /*request*/, ::tensorflow::MonitorResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedMonitor(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::MonitorRequest,::tensorflow::MonitorResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Profile<WithStreamedUnaryMethod_Terminate<WithStreamedUnaryMethod_Monitor<Service > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Profile<WithStreamedUnaryMethod_Terminate<WithStreamedUnaryMethod_Monitor<Service > > > StreamedService;
};

}  // namespace grpc

}  // namespace tensorflow


#endif  // GRPC_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto__INCLUDED
