// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tsl/profiler/protobuf/xplane.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto;
namespace tensorflow {
namespace profiler {
class XEvent;
struct XEventDefaultTypeInternal;
extern XEventDefaultTypeInternal _XEvent_default_instance_;
class XEventMetadata;
struct XEventMetadataDefaultTypeInternal;
extern XEventMetadataDefaultTypeInternal _XEventMetadata_default_instance_;
class XLine;
struct XLineDefaultTypeInternal;
extern XLineDefaultTypeInternal _XLine_default_instance_;
class XPlane;
struct XPlaneDefaultTypeInternal;
extern XPlaneDefaultTypeInternal _XPlane_default_instance_;
class XPlane_EventMetadataEntry_DoNotUse;
struct XPlane_EventMetadataEntry_DoNotUseDefaultTypeInternal;
extern XPlane_EventMetadataEntry_DoNotUseDefaultTypeInternal _XPlane_EventMetadataEntry_DoNotUse_default_instance_;
class XPlane_StatMetadataEntry_DoNotUse;
struct XPlane_StatMetadataEntry_DoNotUseDefaultTypeInternal;
extern XPlane_StatMetadataEntry_DoNotUseDefaultTypeInternal _XPlane_StatMetadataEntry_DoNotUse_default_instance_;
class XSpace;
struct XSpaceDefaultTypeInternal;
extern XSpaceDefaultTypeInternal _XSpace_default_instance_;
class XStat;
struct XStatDefaultTypeInternal;
extern XStatDefaultTypeInternal _XStat_default_instance_;
class XStatMetadata;
struct XStatMetadataDefaultTypeInternal;
extern XStatMetadataDefaultTypeInternal _XStatMetadata_default_instance_;
}  // namespace profiler
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::profiler::XEvent* Arena::CreateMaybeMessage<::tensorflow::profiler::XEvent>(Arena*);
template<> ::tensorflow::profiler::XEventMetadata* Arena::CreateMaybeMessage<::tensorflow::profiler::XEventMetadata>(Arena*);
template<> ::tensorflow::profiler::XLine* Arena::CreateMaybeMessage<::tensorflow::profiler::XLine>(Arena*);
template<> ::tensorflow::profiler::XPlane* Arena::CreateMaybeMessage<::tensorflow::profiler::XPlane>(Arena*);
template<> ::tensorflow::profiler::XPlane_EventMetadataEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::profiler::XPlane_EventMetadataEntry_DoNotUse>(Arena*);
template<> ::tensorflow::profiler::XPlane_StatMetadataEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::profiler::XPlane_StatMetadataEntry_DoNotUse>(Arena*);
template<> ::tensorflow::profiler::XSpace* Arena::CreateMaybeMessage<::tensorflow::profiler::XSpace>(Arena*);
template<> ::tensorflow::profiler::XStat* Arena::CreateMaybeMessage<::tensorflow::profiler::XStat>(Arena*);
template<> ::tensorflow::profiler::XStatMetadata* Arena::CreateMaybeMessage<::tensorflow::profiler::XStatMetadata>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace profiler {

// ===================================================================

class XSpace final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XSpace) */ {
 public:
  inline XSpace() : XSpace(nullptr) {}
  ~XSpace() override;
  explicit PROTOBUF_CONSTEXPR XSpace(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XSpace(const XSpace& from);
  XSpace(XSpace&& from) noexcept
    : XSpace() {
    *this = ::std::move(from);
  }

  inline XSpace& operator=(const XSpace& from) {
    CopyFrom(from);
    return *this;
  }
  inline XSpace& operator=(XSpace&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XSpace& default_instance() {
    return *internal_default_instance();
  }
  static inline const XSpace* internal_default_instance() {
    return reinterpret_cast<const XSpace*>(
               &_XSpace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(XSpace& a, XSpace& b) {
    a.Swap(&b);
  }
  inline void Swap(XSpace* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XSpace* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XSpace* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XSpace>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XSpace& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XSpace& from) {
    XSpace::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XSpace* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XSpace";
  }
  protected:
  explicit XSpace(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlanesFieldNumber = 1,
    kErrorsFieldNumber = 2,
    kWarningsFieldNumber = 3,
    kHostnamesFieldNumber = 4,
  };
  // repeated .tensorflow.profiler.XPlane planes = 1;
  int planes_size() const;
  private:
  int _internal_planes_size() const;
  public:
  void clear_planes();
  ::tensorflow::profiler::XPlane* mutable_planes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XPlane >*
      mutable_planes();
  private:
  const ::tensorflow::profiler::XPlane& _internal_planes(int index) const;
  ::tensorflow::profiler::XPlane* _internal_add_planes();
  public:
  const ::tensorflow::profiler::XPlane& planes(int index) const;
  ::tensorflow::profiler::XPlane* add_planes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XPlane >&
      planes() const;

  // repeated string errors = 2;
  int errors_size() const;
  private:
  int _internal_errors_size() const;
  public:
  void clear_errors();
  const std::string& errors(int index) const;
  std::string* mutable_errors(int index);
  void set_errors(int index, const std::string& value);
  void set_errors(int index, std::string&& value);
  void set_errors(int index, const char* value);
  void set_errors(int index, const char* value, size_t size);
  std::string* add_errors();
  void add_errors(const std::string& value);
  void add_errors(std::string&& value);
  void add_errors(const char* value);
  void add_errors(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& errors() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_errors();
  private:
  const std::string& _internal_errors(int index) const;
  std::string* _internal_add_errors();
  public:

  // repeated string warnings = 3;
  int warnings_size() const;
  private:
  int _internal_warnings_size() const;
  public:
  void clear_warnings();
  const std::string& warnings(int index) const;
  std::string* mutable_warnings(int index);
  void set_warnings(int index, const std::string& value);
  void set_warnings(int index, std::string&& value);
  void set_warnings(int index, const char* value);
  void set_warnings(int index, const char* value, size_t size);
  std::string* add_warnings();
  void add_warnings(const std::string& value);
  void add_warnings(std::string&& value);
  void add_warnings(const char* value);
  void add_warnings(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& warnings() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_warnings();
  private:
  const std::string& _internal_warnings(int index) const;
  std::string* _internal_add_warnings();
  public:

  // repeated string hostnames = 4;
  int hostnames_size() const;
  private:
  int _internal_hostnames_size() const;
  public:
  void clear_hostnames();
  const std::string& hostnames(int index) const;
  std::string* mutable_hostnames(int index);
  void set_hostnames(int index, const std::string& value);
  void set_hostnames(int index, std::string&& value);
  void set_hostnames(int index, const char* value);
  void set_hostnames(int index, const char* value, size_t size);
  std::string* add_hostnames();
  void add_hostnames(const std::string& value);
  void add_hostnames(std::string&& value);
  void add_hostnames(const char* value);
  void add_hostnames(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& hostnames() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_hostnames();
  private:
  const std::string& _internal_hostnames(int index) const;
  std::string* _internal_add_hostnames();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XSpace)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XPlane > planes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> errors_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> warnings_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> hostnames_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XPlane_EventMetadataEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<XPlane_EventMetadataEntry_DoNotUse, 
    int64_t, ::tensorflow::profiler::XEventMetadata,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<XPlane_EventMetadataEntry_DoNotUse, 
    int64_t, ::tensorflow::profiler::XEventMetadata,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  XPlane_EventMetadataEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR XPlane_EventMetadataEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit XPlane_EventMetadataEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const XPlane_EventMetadataEntry_DoNotUse& other);
  static const XPlane_EventMetadataEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const XPlane_EventMetadataEntry_DoNotUse*>(&_XPlane_EventMetadataEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto;
};

// -------------------------------------------------------------------

class XPlane_StatMetadataEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<XPlane_StatMetadataEntry_DoNotUse, 
    int64_t, ::tensorflow::profiler::XStatMetadata,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<XPlane_StatMetadataEntry_DoNotUse, 
    int64_t, ::tensorflow::profiler::XStatMetadata,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  XPlane_StatMetadataEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR XPlane_StatMetadataEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit XPlane_StatMetadataEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const XPlane_StatMetadataEntry_DoNotUse& other);
  static const XPlane_StatMetadataEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const XPlane_StatMetadataEntry_DoNotUse*>(&_XPlane_StatMetadataEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto;
};

// -------------------------------------------------------------------

class XPlane final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XPlane) */ {
 public:
  inline XPlane() : XPlane(nullptr) {}
  ~XPlane() override;
  explicit PROTOBUF_CONSTEXPR XPlane(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XPlane(const XPlane& from);
  XPlane(XPlane&& from) noexcept
    : XPlane() {
    *this = ::std::move(from);
  }

  inline XPlane& operator=(const XPlane& from) {
    CopyFrom(from);
    return *this;
  }
  inline XPlane& operator=(XPlane&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XPlane& default_instance() {
    return *internal_default_instance();
  }
  static inline const XPlane* internal_default_instance() {
    return reinterpret_cast<const XPlane*>(
               &_XPlane_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(XPlane& a, XPlane& b) {
    a.Swap(&b);
  }
  inline void Swap(XPlane* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XPlane* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XPlane* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XPlane>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XPlane& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XPlane& from) {
    XPlane::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XPlane* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XPlane";
  }
  protected:
  explicit XPlane(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kLinesFieldNumber = 3,
    kEventMetadataFieldNumber = 4,
    kStatMetadataFieldNumber = 5,
    kStatsFieldNumber = 6,
    kNameFieldNumber = 2,
    kIdFieldNumber = 1,
  };
  // repeated .tensorflow.profiler.XLine lines = 3;
  int lines_size() const;
  private:
  int _internal_lines_size() const;
  public:
  void clear_lines();
  ::tensorflow::profiler::XLine* mutable_lines(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XLine >*
      mutable_lines();
  private:
  const ::tensorflow::profiler::XLine& _internal_lines(int index) const;
  ::tensorflow::profiler::XLine* _internal_add_lines();
  public:
  const ::tensorflow::profiler::XLine& lines(int index) const;
  ::tensorflow::profiler::XLine* add_lines();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XLine >&
      lines() const;

  // map<int64, .tensorflow.profiler.XEventMetadata> event_metadata = 4;
  int event_metadata_size() const;
  private:
  int _internal_event_metadata_size() const;
  public:
  void clear_event_metadata();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XEventMetadata >&
      _internal_event_metadata() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XEventMetadata >*
      _internal_mutable_event_metadata();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XEventMetadata >&
      event_metadata() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XEventMetadata >*
      mutable_event_metadata();

  // map<int64, .tensorflow.profiler.XStatMetadata> stat_metadata = 5;
  int stat_metadata_size() const;
  private:
  int _internal_stat_metadata_size() const;
  public:
  void clear_stat_metadata();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XStatMetadata >&
      _internal_stat_metadata() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XStatMetadata >*
      _internal_mutable_stat_metadata();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XStatMetadata >&
      stat_metadata() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XStatMetadata >*
      mutable_stat_metadata();

  // repeated .tensorflow.profiler.XStat stats = 6;
  int stats_size() const;
  private:
  int _internal_stats_size() const;
  public:
  void clear_stats();
  ::tensorflow::profiler::XStat* mutable_stats(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
      mutable_stats();
  private:
  const ::tensorflow::profiler::XStat& _internal_stats(int index) const;
  ::tensorflow::profiler::XStat* _internal_add_stats();
  public:
  const ::tensorflow::profiler::XStat& stats(int index) const;
  ::tensorflow::profiler::XStat* add_stats();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
      stats() const;

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XPlane)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XLine > lines_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        XPlane_EventMetadataEntry_DoNotUse,
        int64_t, ::tensorflow::profiler::XEventMetadata,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> event_metadata_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        XPlane_StatMetadataEntry_DoNotUse,
        int64_t, ::tensorflow::profiler::XStatMetadata,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> stat_metadata_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat > stats_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int64_t id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XLine final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XLine) */ {
 public:
  inline XLine() : XLine(nullptr) {}
  ~XLine() override;
  explicit PROTOBUF_CONSTEXPR XLine(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XLine(const XLine& from);
  XLine(XLine&& from) noexcept
    : XLine() {
    *this = ::std::move(from);
  }

  inline XLine& operator=(const XLine& from) {
    CopyFrom(from);
    return *this;
  }
  inline XLine& operator=(XLine&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XLine& default_instance() {
    return *internal_default_instance();
  }
  static inline const XLine* internal_default_instance() {
    return reinterpret_cast<const XLine*>(
               &_XLine_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(XLine& a, XLine& b) {
    a.Swap(&b);
  }
  inline void Swap(XLine* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XLine* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XLine* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XLine>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XLine& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XLine& from) {
    XLine::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XLine* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XLine";
  }
  protected:
  explicit XLine(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEventsFieldNumber = 4,
    kNameFieldNumber = 2,
    kDisplayNameFieldNumber = 11,
    kIdFieldNumber = 1,
    kTimestampNsFieldNumber = 3,
    kDurationPsFieldNumber = 9,
    kDisplayIdFieldNumber = 10,
  };
  // repeated .tensorflow.profiler.XEvent events = 4;
  int events_size() const;
  private:
  int _internal_events_size() const;
  public:
  void clear_events();
  ::tensorflow::profiler::XEvent* mutable_events(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XEvent >*
      mutable_events();
  private:
  const ::tensorflow::profiler::XEvent& _internal_events(int index) const;
  ::tensorflow::profiler::XEvent* _internal_add_events();
  public:
  const ::tensorflow::profiler::XEvent& events(int index) const;
  ::tensorflow::profiler::XEvent* add_events();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XEvent >&
      events() const;

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string display_name = 11;
  void clear_display_name();
  const std::string& display_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_display_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_display_name();
  PROTOBUF_NODISCARD std::string* release_display_name();
  void set_allocated_display_name(std::string* display_name);
  private:
  const std::string& _internal_display_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_display_name(const std::string& value);
  std::string* _internal_mutable_display_name();
  public:

  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // int64 timestamp_ns = 3;
  void clear_timestamp_ns();
  int64_t timestamp_ns() const;
  void set_timestamp_ns(int64_t value);
  private:
  int64_t _internal_timestamp_ns() const;
  void _internal_set_timestamp_ns(int64_t value);
  public:

  // int64 duration_ps = 9;
  void clear_duration_ps();
  int64_t duration_ps() const;
  void set_duration_ps(int64_t value);
  private:
  int64_t _internal_duration_ps() const;
  void _internal_set_duration_ps(int64_t value);
  public:

  // int64 display_id = 10;
  void clear_display_id();
  int64_t display_id() const;
  void set_display_id(int64_t value);
  private:
  int64_t _internal_display_id() const;
  void _internal_set_display_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XLine)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XEvent > events_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr display_name_;
    int64_t id_;
    int64_t timestamp_ns_;
    int64_t duration_ps_;
    int64_t display_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XEvent final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XEvent) */ {
 public:
  inline XEvent() : XEvent(nullptr) {}
  ~XEvent() override;
  explicit PROTOBUF_CONSTEXPR XEvent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XEvent(const XEvent& from);
  XEvent(XEvent&& from) noexcept
    : XEvent() {
    *this = ::std::move(from);
  }

  inline XEvent& operator=(const XEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline XEvent& operator=(XEvent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XEvent& default_instance() {
    return *internal_default_instance();
  }
  enum DataCase {
    kOffsetPs = 2,
    kNumOccurrences = 5,
    DATA_NOT_SET = 0,
  };

  static inline const XEvent* internal_default_instance() {
    return reinterpret_cast<const XEvent*>(
               &_XEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(XEvent& a, XEvent& b) {
    a.Swap(&b);
  }
  inline void Swap(XEvent* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XEvent>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XEvent& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XEvent& from) {
    XEvent::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XEvent* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XEvent";
  }
  protected:
  explicit XEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStatsFieldNumber = 4,
    kMetadataIdFieldNumber = 1,
    kDurationPsFieldNumber = 3,
    kOffsetPsFieldNumber = 2,
    kNumOccurrencesFieldNumber = 5,
  };
  // repeated .tensorflow.profiler.XStat stats = 4;
  int stats_size() const;
  private:
  int _internal_stats_size() const;
  public:
  void clear_stats();
  ::tensorflow::profiler::XStat* mutable_stats(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
      mutable_stats();
  private:
  const ::tensorflow::profiler::XStat& _internal_stats(int index) const;
  ::tensorflow::profiler::XStat* _internal_add_stats();
  public:
  const ::tensorflow::profiler::XStat& stats(int index) const;
  ::tensorflow::profiler::XStat* add_stats();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
      stats() const;

  // int64 metadata_id = 1;
  void clear_metadata_id();
  int64_t metadata_id() const;
  void set_metadata_id(int64_t value);
  private:
  int64_t _internal_metadata_id() const;
  void _internal_set_metadata_id(int64_t value);
  public:

  // int64 duration_ps = 3;
  void clear_duration_ps();
  int64_t duration_ps() const;
  void set_duration_ps(int64_t value);
  private:
  int64_t _internal_duration_ps() const;
  void _internal_set_duration_ps(int64_t value);
  public:

  // int64 offset_ps = 2;
  bool has_offset_ps() const;
  private:
  bool _internal_has_offset_ps() const;
  public:
  void clear_offset_ps();
  int64_t offset_ps() const;
  void set_offset_ps(int64_t value);
  private:
  int64_t _internal_offset_ps() const;
  void _internal_set_offset_ps(int64_t value);
  public:

  // int64 num_occurrences = 5;
  bool has_num_occurrences() const;
  private:
  bool _internal_has_num_occurrences() const;
  public:
  void clear_num_occurrences();
  int64_t num_occurrences() const;
  void set_num_occurrences(int64_t value);
  private:
  int64_t _internal_num_occurrences() const;
  void _internal_set_num_occurrences(int64_t value);
  public:

  void clear_data();
  DataCase data_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XEvent)
 private:
  class _Internal;
  void set_has_offset_ps();
  void set_has_num_occurrences();

  inline bool has_data() const;
  inline void clear_has_data();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat > stats_;
    int64_t metadata_id_;
    int64_t duration_ps_;
    union DataUnion {
      constexpr DataUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int64_t offset_ps_;
      int64_t num_occurrences_;
    } data_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XStat final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XStat) */ {
 public:
  inline XStat() : XStat(nullptr) {}
  ~XStat() override;
  explicit PROTOBUF_CONSTEXPR XStat(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XStat(const XStat& from);
  XStat(XStat&& from) noexcept
    : XStat() {
    *this = ::std::move(from);
  }

  inline XStat& operator=(const XStat& from) {
    CopyFrom(from);
    return *this;
  }
  inline XStat& operator=(XStat&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XStat& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kDoubleValue = 2,
    kUint64Value = 3,
    kInt64Value = 4,
    kStrValue = 5,
    kBytesValue = 6,
    kRefValue = 7,
    VALUE_NOT_SET = 0,
  };

  static inline const XStat* internal_default_instance() {
    return reinterpret_cast<const XStat*>(
               &_XStat_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(XStat& a, XStat& b) {
    a.Swap(&b);
  }
  inline void Swap(XStat* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XStat* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XStat* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XStat>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XStat& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XStat& from) {
    XStat::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XStat* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XStat";
  }
  protected:
  explicit XStat(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMetadataIdFieldNumber = 1,
    kDoubleValueFieldNumber = 2,
    kUint64ValueFieldNumber = 3,
    kInt64ValueFieldNumber = 4,
    kStrValueFieldNumber = 5,
    kBytesValueFieldNumber = 6,
    kRefValueFieldNumber = 7,
  };
  // int64 metadata_id = 1;
  void clear_metadata_id();
  int64_t metadata_id() const;
  void set_metadata_id(int64_t value);
  private:
  int64_t _internal_metadata_id() const;
  void _internal_set_metadata_id(int64_t value);
  public:

  // double double_value = 2;
  bool has_double_value() const;
  private:
  bool _internal_has_double_value() const;
  public:
  void clear_double_value();
  double double_value() const;
  void set_double_value(double value);
  private:
  double _internal_double_value() const;
  void _internal_set_double_value(double value);
  public:

  // uint64 uint64_value = 3;
  bool has_uint64_value() const;
  private:
  bool _internal_has_uint64_value() const;
  public:
  void clear_uint64_value();
  uint64_t uint64_value() const;
  void set_uint64_value(uint64_t value);
  private:
  uint64_t _internal_uint64_value() const;
  void _internal_set_uint64_value(uint64_t value);
  public:

  // int64 int64_value = 4;
  bool has_int64_value() const;
  private:
  bool _internal_has_int64_value() const;
  public:
  void clear_int64_value();
  int64_t int64_value() const;
  void set_int64_value(int64_t value);
  private:
  int64_t _internal_int64_value() const;
  void _internal_set_int64_value(int64_t value);
  public:

  // string str_value = 5;
  bool has_str_value() const;
  private:
  bool _internal_has_str_value() const;
  public:
  void clear_str_value();
  const std::string& str_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_str_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_str_value();
  PROTOBUF_NODISCARD std::string* release_str_value();
  void set_allocated_str_value(std::string* str_value);
  private:
  const std::string& _internal_str_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_str_value(const std::string& value);
  std::string* _internal_mutable_str_value();
  public:

  // bytes bytes_value = 6;
  bool has_bytes_value() const;
  private:
  bool _internal_has_bytes_value() const;
  public:
  void clear_bytes_value();
  const std::string& bytes_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bytes_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bytes_value();
  PROTOBUF_NODISCARD std::string* release_bytes_value();
  void set_allocated_bytes_value(std::string* bytes_value);
  private:
  const std::string& _internal_bytes_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bytes_value(const std::string& value);
  std::string* _internal_mutable_bytes_value();
  public:

  // uint64 ref_value = 7;
  bool has_ref_value() const;
  private:
  bool _internal_has_ref_value() const;
  public:
  void clear_ref_value();
  uint64_t ref_value() const;
  void set_ref_value(uint64_t value);
  private:
  uint64_t _internal_ref_value() const;
  void _internal_set_ref_value(uint64_t value);
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XStat)
 private:
  class _Internal;
  void set_has_double_value();
  void set_has_uint64_value();
  void set_has_int64_value();
  void set_has_str_value();
  void set_has_bytes_value();
  void set_has_ref_value();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t metadata_id_;
    union ValueUnion {
      constexpr ValueUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      double double_value_;
      uint64_t uint64_value_;
      int64_t int64_value_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr str_value_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bytes_value_;
      uint64_t ref_value_;
    } value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XEventMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XEventMetadata) */ {
 public:
  inline XEventMetadata() : XEventMetadata(nullptr) {}
  ~XEventMetadata() override;
  explicit PROTOBUF_CONSTEXPR XEventMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XEventMetadata(const XEventMetadata& from);
  XEventMetadata(XEventMetadata&& from) noexcept
    : XEventMetadata() {
    *this = ::std::move(from);
  }

  inline XEventMetadata& operator=(const XEventMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline XEventMetadata& operator=(XEventMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XEventMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const XEventMetadata* internal_default_instance() {
    return reinterpret_cast<const XEventMetadata*>(
               &_XEventMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(XEventMetadata& a, XEventMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(XEventMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XEventMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XEventMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XEventMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XEventMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XEventMetadata& from) {
    XEventMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XEventMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XEventMetadata";
  }
  protected:
  explicit XEventMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStatsFieldNumber = 5,
    kChildIdFieldNumber = 6,
    kNameFieldNumber = 2,
    kMetadataFieldNumber = 3,
    kDisplayNameFieldNumber = 4,
    kIdFieldNumber = 1,
  };
  // repeated .tensorflow.profiler.XStat stats = 5;
  int stats_size() const;
  private:
  int _internal_stats_size() const;
  public:
  void clear_stats();
  ::tensorflow::profiler::XStat* mutable_stats(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
      mutable_stats();
  private:
  const ::tensorflow::profiler::XStat& _internal_stats(int index) const;
  ::tensorflow::profiler::XStat* _internal_add_stats();
  public:
  const ::tensorflow::profiler::XStat& stats(int index) const;
  ::tensorflow::profiler::XStat* add_stats();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
      stats() const;

  // repeated int64 child_id = 6;
  int child_id_size() const;
  private:
  int _internal_child_id_size() const;
  public:
  void clear_child_id();
  private:
  int64_t _internal_child_id(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_child_id() const;
  void _internal_add_child_id(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_child_id();
  public:
  int64_t child_id(int index) const;
  void set_child_id(int index, int64_t value);
  void add_child_id(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      child_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_child_id();

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bytes metadata = 3;
  void clear_metadata();
  const std::string& metadata() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_metadata(ArgT0&& arg0, ArgT... args);
  std::string* mutable_metadata();
  PROTOBUF_NODISCARD std::string* release_metadata();
  void set_allocated_metadata(std::string* metadata);
  private:
  const std::string& _internal_metadata() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_metadata(const std::string& value);
  std::string* _internal_mutable_metadata();
  public:

  // string display_name = 4;
  void clear_display_name();
  const std::string& display_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_display_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_display_name();
  PROTOBUF_NODISCARD std::string* release_display_name();
  void set_allocated_display_name(std::string* display_name);
  private:
  const std::string& _internal_display_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_display_name(const std::string& value);
  std::string* _internal_mutable_display_name();
  public:

  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XEventMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat > stats_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > child_id_;
    mutable std::atomic<int> _child_id_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr metadata_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr display_name_;
    int64_t id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// -------------------------------------------------------------------

class XStatMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.profiler.XStatMetadata) */ {
 public:
  inline XStatMetadata() : XStatMetadata(nullptr) {}
  ~XStatMetadata() override;
  explicit PROTOBUF_CONSTEXPR XStatMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XStatMetadata(const XStatMetadata& from);
  XStatMetadata(XStatMetadata&& from) noexcept
    : XStatMetadata() {
    *this = ::std::move(from);
  }

  inline XStatMetadata& operator=(const XStatMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline XStatMetadata& operator=(XStatMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XStatMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const XStatMetadata* internal_default_instance() {
    return reinterpret_cast<const XStatMetadata*>(
               &_XStatMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(XStatMetadata& a, XStatMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(XStatMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XStatMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XStatMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XStatMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XStatMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XStatMetadata& from) {
    XStatMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XStatMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.profiler.XStatMetadata";
  }
  protected:
  explicit XStatMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 2,
    kDescriptionFieldNumber = 3,
    kIdFieldNumber = 1,
  };
  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string description = 3;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.profiler.XStatMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
    int64_t id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// XSpace

// repeated .tensorflow.profiler.XPlane planes = 1;
inline int XSpace::_internal_planes_size() const {
  return _impl_.planes_.size();
}
inline int XSpace::planes_size() const {
  return _internal_planes_size();
}
inline void XSpace::clear_planes() {
  _impl_.planes_.Clear();
}
inline ::tensorflow::profiler::XPlane* XSpace::mutable_planes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XSpace.planes)
  return _impl_.planes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XPlane >*
XSpace::mutable_planes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XSpace.planes)
  return &_impl_.planes_;
}
inline const ::tensorflow::profiler::XPlane& XSpace::_internal_planes(int index) const {
  return _impl_.planes_.Get(index);
}
inline const ::tensorflow::profiler::XPlane& XSpace::planes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XSpace.planes)
  return _internal_planes(index);
}
inline ::tensorflow::profiler::XPlane* XSpace::_internal_add_planes() {
  return _impl_.planes_.Add();
}
inline ::tensorflow::profiler::XPlane* XSpace::add_planes() {
  ::tensorflow::profiler::XPlane* _add = _internal_add_planes();
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.planes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XPlane >&
XSpace::planes() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XSpace.planes)
  return _impl_.planes_;
}

// repeated string errors = 2;
inline int XSpace::_internal_errors_size() const {
  return _impl_.errors_.size();
}
inline int XSpace::errors_size() const {
  return _internal_errors_size();
}
inline void XSpace::clear_errors() {
  _impl_.errors_.Clear();
}
inline std::string* XSpace::add_errors() {
  std::string* _s = _internal_add_errors();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.profiler.XSpace.errors)
  return _s;
}
inline const std::string& XSpace::_internal_errors(int index) const {
  return _impl_.errors_.Get(index);
}
inline const std::string& XSpace::errors(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XSpace.errors)
  return _internal_errors(index);
}
inline std::string* XSpace::mutable_errors(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XSpace.errors)
  return _impl_.errors_.Mutable(index);
}
inline void XSpace::set_errors(int index, const std::string& value) {
  _impl_.errors_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.errors)
}
inline void XSpace::set_errors(int index, std::string&& value) {
  _impl_.errors_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.errors)
}
inline void XSpace::set_errors(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.errors_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XSpace.errors)
}
inline void XSpace::set_errors(int index, const char* value, size_t size) {
  _impl_.errors_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XSpace.errors)
}
inline std::string* XSpace::_internal_add_errors() {
  return _impl_.errors_.Add();
}
inline void XSpace::add_errors(const std::string& value) {
  _impl_.errors_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.errors)
}
inline void XSpace::add_errors(std::string&& value) {
  _impl_.errors_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.errors)
}
inline void XSpace::add_errors(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.errors_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.profiler.XSpace.errors)
}
inline void XSpace::add_errors(const char* value, size_t size) {
  _impl_.errors_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.profiler.XSpace.errors)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
XSpace::errors() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XSpace.errors)
  return _impl_.errors_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
XSpace::mutable_errors() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XSpace.errors)
  return &_impl_.errors_;
}

// repeated string warnings = 3;
inline int XSpace::_internal_warnings_size() const {
  return _impl_.warnings_.size();
}
inline int XSpace::warnings_size() const {
  return _internal_warnings_size();
}
inline void XSpace::clear_warnings() {
  _impl_.warnings_.Clear();
}
inline std::string* XSpace::add_warnings() {
  std::string* _s = _internal_add_warnings();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.profiler.XSpace.warnings)
  return _s;
}
inline const std::string& XSpace::_internal_warnings(int index) const {
  return _impl_.warnings_.Get(index);
}
inline const std::string& XSpace::warnings(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XSpace.warnings)
  return _internal_warnings(index);
}
inline std::string* XSpace::mutable_warnings(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XSpace.warnings)
  return _impl_.warnings_.Mutable(index);
}
inline void XSpace::set_warnings(int index, const std::string& value) {
  _impl_.warnings_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.warnings)
}
inline void XSpace::set_warnings(int index, std::string&& value) {
  _impl_.warnings_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.warnings)
}
inline void XSpace::set_warnings(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.warnings_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XSpace.warnings)
}
inline void XSpace::set_warnings(int index, const char* value, size_t size) {
  _impl_.warnings_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XSpace.warnings)
}
inline std::string* XSpace::_internal_add_warnings() {
  return _impl_.warnings_.Add();
}
inline void XSpace::add_warnings(const std::string& value) {
  _impl_.warnings_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.warnings)
}
inline void XSpace::add_warnings(std::string&& value) {
  _impl_.warnings_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.warnings)
}
inline void XSpace::add_warnings(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.warnings_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.profiler.XSpace.warnings)
}
inline void XSpace::add_warnings(const char* value, size_t size) {
  _impl_.warnings_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.profiler.XSpace.warnings)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
XSpace::warnings() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XSpace.warnings)
  return _impl_.warnings_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
XSpace::mutable_warnings() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XSpace.warnings)
  return &_impl_.warnings_;
}

// repeated string hostnames = 4;
inline int XSpace::_internal_hostnames_size() const {
  return _impl_.hostnames_.size();
}
inline int XSpace::hostnames_size() const {
  return _internal_hostnames_size();
}
inline void XSpace::clear_hostnames() {
  _impl_.hostnames_.Clear();
}
inline std::string* XSpace::add_hostnames() {
  std::string* _s = _internal_add_hostnames();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.profiler.XSpace.hostnames)
  return _s;
}
inline const std::string& XSpace::_internal_hostnames(int index) const {
  return _impl_.hostnames_.Get(index);
}
inline const std::string& XSpace::hostnames(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XSpace.hostnames)
  return _internal_hostnames(index);
}
inline std::string* XSpace::mutable_hostnames(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XSpace.hostnames)
  return _impl_.hostnames_.Mutable(index);
}
inline void XSpace::set_hostnames(int index, const std::string& value) {
  _impl_.hostnames_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.hostnames)
}
inline void XSpace::set_hostnames(int index, std::string&& value) {
  _impl_.hostnames_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XSpace.hostnames)
}
inline void XSpace::set_hostnames(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.hostnames_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.profiler.XSpace.hostnames)
}
inline void XSpace::set_hostnames(int index, const char* value, size_t size) {
  _impl_.hostnames_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.profiler.XSpace.hostnames)
}
inline std::string* XSpace::_internal_add_hostnames() {
  return _impl_.hostnames_.Add();
}
inline void XSpace::add_hostnames(const std::string& value) {
  _impl_.hostnames_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.hostnames)
}
inline void XSpace::add_hostnames(std::string&& value) {
  _impl_.hostnames_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XSpace.hostnames)
}
inline void XSpace::add_hostnames(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.hostnames_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.profiler.XSpace.hostnames)
}
inline void XSpace::add_hostnames(const char* value, size_t size) {
  _impl_.hostnames_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.profiler.XSpace.hostnames)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
XSpace::hostnames() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XSpace.hostnames)
  return _impl_.hostnames_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
XSpace::mutable_hostnames() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XSpace.hostnames)
  return &_impl_.hostnames_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// XPlane

// int64 id = 1;
inline void XPlane::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t XPlane::_internal_id() const {
  return _impl_.id_;
}
inline int64_t XPlane::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XPlane.id)
  return _internal_id();
}
inline void XPlane::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void XPlane::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XPlane.id)
}

// string name = 2;
inline void XPlane::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& XPlane::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XPlane.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XPlane::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XPlane.name)
}
inline std::string* XPlane::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XPlane.name)
  return _s;
}
inline const std::string& XPlane::_internal_name() const {
  return _impl_.name_.Get();
}
inline void XPlane::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* XPlane::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* XPlane::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XPlane.name)
  return _impl_.name_.Release();
}
inline void XPlane::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XPlane.name)
}

// repeated .tensorflow.profiler.XLine lines = 3;
inline int XPlane::_internal_lines_size() const {
  return _impl_.lines_.size();
}
inline int XPlane::lines_size() const {
  return _internal_lines_size();
}
inline void XPlane::clear_lines() {
  _impl_.lines_.Clear();
}
inline ::tensorflow::profiler::XLine* XPlane::mutable_lines(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XPlane.lines)
  return _impl_.lines_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XLine >*
XPlane::mutable_lines() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XPlane.lines)
  return &_impl_.lines_;
}
inline const ::tensorflow::profiler::XLine& XPlane::_internal_lines(int index) const {
  return _impl_.lines_.Get(index);
}
inline const ::tensorflow::profiler::XLine& XPlane::lines(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XPlane.lines)
  return _internal_lines(index);
}
inline ::tensorflow::profiler::XLine* XPlane::_internal_add_lines() {
  return _impl_.lines_.Add();
}
inline ::tensorflow::profiler::XLine* XPlane::add_lines() {
  ::tensorflow::profiler::XLine* _add = _internal_add_lines();
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XPlane.lines)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XLine >&
XPlane::lines() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XPlane.lines)
  return _impl_.lines_;
}

// map<int64, .tensorflow.profiler.XEventMetadata> event_metadata = 4;
inline int XPlane::_internal_event_metadata_size() const {
  return _impl_.event_metadata_.size();
}
inline int XPlane::event_metadata_size() const {
  return _internal_event_metadata_size();
}
inline void XPlane::clear_event_metadata() {
  _impl_.event_metadata_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XEventMetadata >&
XPlane::_internal_event_metadata() const {
  return _impl_.event_metadata_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XEventMetadata >&
XPlane::event_metadata() const {
  // @@protoc_insertion_point(field_map:tensorflow.profiler.XPlane.event_metadata)
  return _internal_event_metadata();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XEventMetadata >*
XPlane::_internal_mutable_event_metadata() {
  return _impl_.event_metadata_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XEventMetadata >*
XPlane::mutable_event_metadata() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.profiler.XPlane.event_metadata)
  return _internal_mutable_event_metadata();
}

// map<int64, .tensorflow.profiler.XStatMetadata> stat_metadata = 5;
inline int XPlane::_internal_stat_metadata_size() const {
  return _impl_.stat_metadata_.size();
}
inline int XPlane::stat_metadata_size() const {
  return _internal_stat_metadata_size();
}
inline void XPlane::clear_stat_metadata() {
  _impl_.stat_metadata_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XStatMetadata >&
XPlane::_internal_stat_metadata() const {
  return _impl_.stat_metadata_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XStatMetadata >&
XPlane::stat_metadata() const {
  // @@protoc_insertion_point(field_map:tensorflow.profiler.XPlane.stat_metadata)
  return _internal_stat_metadata();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XStatMetadata >*
XPlane::_internal_mutable_stat_metadata() {
  return _impl_.stat_metadata_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::profiler::XStatMetadata >*
XPlane::mutable_stat_metadata() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.profiler.XPlane.stat_metadata)
  return _internal_mutable_stat_metadata();
}

// repeated .tensorflow.profiler.XStat stats = 6;
inline int XPlane::_internal_stats_size() const {
  return _impl_.stats_.size();
}
inline int XPlane::stats_size() const {
  return _internal_stats_size();
}
inline void XPlane::clear_stats() {
  _impl_.stats_.Clear();
}
inline ::tensorflow::profiler::XStat* XPlane::mutable_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XPlane.stats)
  return _impl_.stats_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
XPlane::mutable_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XPlane.stats)
  return &_impl_.stats_;
}
inline const ::tensorflow::profiler::XStat& XPlane::_internal_stats(int index) const {
  return _impl_.stats_.Get(index);
}
inline const ::tensorflow::profiler::XStat& XPlane::stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XPlane.stats)
  return _internal_stats(index);
}
inline ::tensorflow::profiler::XStat* XPlane::_internal_add_stats() {
  return _impl_.stats_.Add();
}
inline ::tensorflow::profiler::XStat* XPlane::add_stats() {
  ::tensorflow::profiler::XStat* _add = _internal_add_stats();
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XPlane.stats)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
XPlane::stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XPlane.stats)
  return _impl_.stats_;
}

// -------------------------------------------------------------------

// XLine

// int64 id = 1;
inline void XLine::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t XLine::_internal_id() const {
  return _impl_.id_;
}
inline int64_t XLine::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.id)
  return _internal_id();
}
inline void XLine::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void XLine::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.id)
}

// int64 display_id = 10;
inline void XLine::clear_display_id() {
  _impl_.display_id_ = int64_t{0};
}
inline int64_t XLine::_internal_display_id() const {
  return _impl_.display_id_;
}
inline int64_t XLine::display_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.display_id)
  return _internal_display_id();
}
inline void XLine::_internal_set_display_id(int64_t value) {
  
  _impl_.display_id_ = value;
}
inline void XLine::set_display_id(int64_t value) {
  _internal_set_display_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.display_id)
}

// string name = 2;
inline void XLine::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& XLine::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XLine::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.name)
}
inline std::string* XLine::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XLine.name)
  return _s;
}
inline const std::string& XLine::_internal_name() const {
  return _impl_.name_.Get();
}
inline void XLine::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* XLine::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* XLine::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XLine.name)
  return _impl_.name_.Release();
}
inline void XLine::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XLine.name)
}

// string display_name = 11;
inline void XLine::clear_display_name() {
  _impl_.display_name_.ClearToEmpty();
}
inline const std::string& XLine::display_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.display_name)
  return _internal_display_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XLine::set_display_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.display_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.display_name)
}
inline std::string* XLine::mutable_display_name() {
  std::string* _s = _internal_mutable_display_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XLine.display_name)
  return _s;
}
inline const std::string& XLine::_internal_display_name() const {
  return _impl_.display_name_.Get();
}
inline void XLine::_internal_set_display_name(const std::string& value) {
  
  _impl_.display_name_.Set(value, GetArenaForAllocation());
}
inline std::string* XLine::_internal_mutable_display_name() {
  
  return _impl_.display_name_.Mutable(GetArenaForAllocation());
}
inline std::string* XLine::release_display_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XLine.display_name)
  return _impl_.display_name_.Release();
}
inline void XLine::set_allocated_display_name(std::string* display_name) {
  if (display_name != nullptr) {
    
  } else {
    
  }
  _impl_.display_name_.SetAllocated(display_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.display_name_.IsDefault()) {
    _impl_.display_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XLine.display_name)
}

// int64 timestamp_ns = 3;
inline void XLine::clear_timestamp_ns() {
  _impl_.timestamp_ns_ = int64_t{0};
}
inline int64_t XLine::_internal_timestamp_ns() const {
  return _impl_.timestamp_ns_;
}
inline int64_t XLine::timestamp_ns() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.timestamp_ns)
  return _internal_timestamp_ns();
}
inline void XLine::_internal_set_timestamp_ns(int64_t value) {
  
  _impl_.timestamp_ns_ = value;
}
inline void XLine::set_timestamp_ns(int64_t value) {
  _internal_set_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.timestamp_ns)
}

// int64 duration_ps = 9;
inline void XLine::clear_duration_ps() {
  _impl_.duration_ps_ = int64_t{0};
}
inline int64_t XLine::_internal_duration_ps() const {
  return _impl_.duration_ps_;
}
inline int64_t XLine::duration_ps() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.duration_ps)
  return _internal_duration_ps();
}
inline void XLine::_internal_set_duration_ps(int64_t value) {
  
  _impl_.duration_ps_ = value;
}
inline void XLine::set_duration_ps(int64_t value) {
  _internal_set_duration_ps(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XLine.duration_ps)
}

// repeated .tensorflow.profiler.XEvent events = 4;
inline int XLine::_internal_events_size() const {
  return _impl_.events_.size();
}
inline int XLine::events_size() const {
  return _internal_events_size();
}
inline void XLine::clear_events() {
  _impl_.events_.Clear();
}
inline ::tensorflow::profiler::XEvent* XLine::mutable_events(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XLine.events)
  return _impl_.events_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XEvent >*
XLine::mutable_events() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XLine.events)
  return &_impl_.events_;
}
inline const ::tensorflow::profiler::XEvent& XLine::_internal_events(int index) const {
  return _impl_.events_.Get(index);
}
inline const ::tensorflow::profiler::XEvent& XLine::events(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XLine.events)
  return _internal_events(index);
}
inline ::tensorflow::profiler::XEvent* XLine::_internal_add_events() {
  return _impl_.events_.Add();
}
inline ::tensorflow::profiler::XEvent* XLine::add_events() {
  ::tensorflow::profiler::XEvent* _add = _internal_add_events();
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XLine.events)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XEvent >&
XLine::events() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XLine.events)
  return _impl_.events_;
}

// -------------------------------------------------------------------

// XEvent

// int64 metadata_id = 1;
inline void XEvent::clear_metadata_id() {
  _impl_.metadata_id_ = int64_t{0};
}
inline int64_t XEvent::_internal_metadata_id() const {
  return _impl_.metadata_id_;
}
inline int64_t XEvent::metadata_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEvent.metadata_id)
  return _internal_metadata_id();
}
inline void XEvent::_internal_set_metadata_id(int64_t value) {
  
  _impl_.metadata_id_ = value;
}
inline void XEvent::set_metadata_id(int64_t value) {
  _internal_set_metadata_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEvent.metadata_id)
}

// int64 offset_ps = 2;
inline bool XEvent::_internal_has_offset_ps() const {
  return data_case() == kOffsetPs;
}
inline bool XEvent::has_offset_ps() const {
  return _internal_has_offset_ps();
}
inline void XEvent::set_has_offset_ps() {
  _impl_._oneof_case_[0] = kOffsetPs;
}
inline void XEvent::clear_offset_ps() {
  if (_internal_has_offset_ps()) {
    _impl_.data_.offset_ps_ = int64_t{0};
    clear_has_data();
  }
}
inline int64_t XEvent::_internal_offset_ps() const {
  if (_internal_has_offset_ps()) {
    return _impl_.data_.offset_ps_;
  }
  return int64_t{0};
}
inline void XEvent::_internal_set_offset_ps(int64_t value) {
  if (!_internal_has_offset_ps()) {
    clear_data();
    set_has_offset_ps();
  }
  _impl_.data_.offset_ps_ = value;
}
inline int64_t XEvent::offset_ps() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEvent.offset_ps)
  return _internal_offset_ps();
}
inline void XEvent::set_offset_ps(int64_t value) {
  _internal_set_offset_ps(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEvent.offset_ps)
}

// int64 num_occurrences = 5;
inline bool XEvent::_internal_has_num_occurrences() const {
  return data_case() == kNumOccurrences;
}
inline bool XEvent::has_num_occurrences() const {
  return _internal_has_num_occurrences();
}
inline void XEvent::set_has_num_occurrences() {
  _impl_._oneof_case_[0] = kNumOccurrences;
}
inline void XEvent::clear_num_occurrences() {
  if (_internal_has_num_occurrences()) {
    _impl_.data_.num_occurrences_ = int64_t{0};
    clear_has_data();
  }
}
inline int64_t XEvent::_internal_num_occurrences() const {
  if (_internal_has_num_occurrences()) {
    return _impl_.data_.num_occurrences_;
  }
  return int64_t{0};
}
inline void XEvent::_internal_set_num_occurrences(int64_t value) {
  if (!_internal_has_num_occurrences()) {
    clear_data();
    set_has_num_occurrences();
  }
  _impl_.data_.num_occurrences_ = value;
}
inline int64_t XEvent::num_occurrences() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEvent.num_occurrences)
  return _internal_num_occurrences();
}
inline void XEvent::set_num_occurrences(int64_t value) {
  _internal_set_num_occurrences(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEvent.num_occurrences)
}

// int64 duration_ps = 3;
inline void XEvent::clear_duration_ps() {
  _impl_.duration_ps_ = int64_t{0};
}
inline int64_t XEvent::_internal_duration_ps() const {
  return _impl_.duration_ps_;
}
inline int64_t XEvent::duration_ps() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEvent.duration_ps)
  return _internal_duration_ps();
}
inline void XEvent::_internal_set_duration_ps(int64_t value) {
  
  _impl_.duration_ps_ = value;
}
inline void XEvent::set_duration_ps(int64_t value) {
  _internal_set_duration_ps(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEvent.duration_ps)
}

// repeated .tensorflow.profiler.XStat stats = 4;
inline int XEvent::_internal_stats_size() const {
  return _impl_.stats_.size();
}
inline int XEvent::stats_size() const {
  return _internal_stats_size();
}
inline void XEvent::clear_stats() {
  _impl_.stats_.Clear();
}
inline ::tensorflow::profiler::XStat* XEvent::mutable_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XEvent.stats)
  return _impl_.stats_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
XEvent::mutable_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XEvent.stats)
  return &_impl_.stats_;
}
inline const ::tensorflow::profiler::XStat& XEvent::_internal_stats(int index) const {
  return _impl_.stats_.Get(index);
}
inline const ::tensorflow::profiler::XStat& XEvent::stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEvent.stats)
  return _internal_stats(index);
}
inline ::tensorflow::profiler::XStat* XEvent::_internal_add_stats() {
  return _impl_.stats_.Add();
}
inline ::tensorflow::profiler::XStat* XEvent::add_stats() {
  ::tensorflow::profiler::XStat* _add = _internal_add_stats();
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XEvent.stats)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
XEvent::stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XEvent.stats)
  return _impl_.stats_;
}

inline bool XEvent::has_data() const {
  return data_case() != DATA_NOT_SET;
}
inline void XEvent::clear_has_data() {
  _impl_._oneof_case_[0] = DATA_NOT_SET;
}
inline XEvent::DataCase XEvent::data_case() const {
  return XEvent::DataCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// XStat

// int64 metadata_id = 1;
inline void XStat::clear_metadata_id() {
  _impl_.metadata_id_ = int64_t{0};
}
inline int64_t XStat::_internal_metadata_id() const {
  return _impl_.metadata_id_;
}
inline int64_t XStat::metadata_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.metadata_id)
  return _internal_metadata_id();
}
inline void XStat::_internal_set_metadata_id(int64_t value) {
  
  _impl_.metadata_id_ = value;
}
inline void XStat::set_metadata_id(int64_t value) {
  _internal_set_metadata_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.metadata_id)
}

// double double_value = 2;
inline bool XStat::_internal_has_double_value() const {
  return value_case() == kDoubleValue;
}
inline bool XStat::has_double_value() const {
  return _internal_has_double_value();
}
inline void XStat::set_has_double_value() {
  _impl_._oneof_case_[0] = kDoubleValue;
}
inline void XStat::clear_double_value() {
  if (_internal_has_double_value()) {
    _impl_.value_.double_value_ = 0;
    clear_has_value();
  }
}
inline double XStat::_internal_double_value() const {
  if (_internal_has_double_value()) {
    return _impl_.value_.double_value_;
  }
  return 0;
}
inline void XStat::_internal_set_double_value(double value) {
  if (!_internal_has_double_value()) {
    clear_value();
    set_has_double_value();
  }
  _impl_.value_.double_value_ = value;
}
inline double XStat::double_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.double_value)
  return _internal_double_value();
}
inline void XStat::set_double_value(double value) {
  _internal_set_double_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.double_value)
}

// uint64 uint64_value = 3;
inline bool XStat::_internal_has_uint64_value() const {
  return value_case() == kUint64Value;
}
inline bool XStat::has_uint64_value() const {
  return _internal_has_uint64_value();
}
inline void XStat::set_has_uint64_value() {
  _impl_._oneof_case_[0] = kUint64Value;
}
inline void XStat::clear_uint64_value() {
  if (_internal_has_uint64_value()) {
    _impl_.value_.uint64_value_ = uint64_t{0u};
    clear_has_value();
  }
}
inline uint64_t XStat::_internal_uint64_value() const {
  if (_internal_has_uint64_value()) {
    return _impl_.value_.uint64_value_;
  }
  return uint64_t{0u};
}
inline void XStat::_internal_set_uint64_value(uint64_t value) {
  if (!_internal_has_uint64_value()) {
    clear_value();
    set_has_uint64_value();
  }
  _impl_.value_.uint64_value_ = value;
}
inline uint64_t XStat::uint64_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.uint64_value)
  return _internal_uint64_value();
}
inline void XStat::set_uint64_value(uint64_t value) {
  _internal_set_uint64_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.uint64_value)
}

// int64 int64_value = 4;
inline bool XStat::_internal_has_int64_value() const {
  return value_case() == kInt64Value;
}
inline bool XStat::has_int64_value() const {
  return _internal_has_int64_value();
}
inline void XStat::set_has_int64_value() {
  _impl_._oneof_case_[0] = kInt64Value;
}
inline void XStat::clear_int64_value() {
  if (_internal_has_int64_value()) {
    _impl_.value_.int64_value_ = int64_t{0};
    clear_has_value();
  }
}
inline int64_t XStat::_internal_int64_value() const {
  if (_internal_has_int64_value()) {
    return _impl_.value_.int64_value_;
  }
  return int64_t{0};
}
inline void XStat::_internal_set_int64_value(int64_t value) {
  if (!_internal_has_int64_value()) {
    clear_value();
    set_has_int64_value();
  }
  _impl_.value_.int64_value_ = value;
}
inline int64_t XStat::int64_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.int64_value)
  return _internal_int64_value();
}
inline void XStat::set_int64_value(int64_t value) {
  _internal_set_int64_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.int64_value)
}

// string str_value = 5;
inline bool XStat::_internal_has_str_value() const {
  return value_case() == kStrValue;
}
inline bool XStat::has_str_value() const {
  return _internal_has_str_value();
}
inline void XStat::set_has_str_value() {
  _impl_._oneof_case_[0] = kStrValue;
}
inline void XStat::clear_str_value() {
  if (_internal_has_str_value()) {
    _impl_.value_.str_value_.Destroy();
    clear_has_value();
  }
}
inline const std::string& XStat::str_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.str_value)
  return _internal_str_value();
}
template <typename ArgT0, typename... ArgT>
inline void XStat::set_str_value(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_str_value()) {
    clear_value();
    set_has_str_value();
    _impl_.value_.str_value_.InitDefault();
  }
  _impl_.value_.str_value_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.str_value)
}
inline std::string* XStat::mutable_str_value() {
  std::string* _s = _internal_mutable_str_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XStat.str_value)
  return _s;
}
inline const std::string& XStat::_internal_str_value() const {
  if (_internal_has_str_value()) {
    return _impl_.value_.str_value_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void XStat::_internal_set_str_value(const std::string& value) {
  if (!_internal_has_str_value()) {
    clear_value();
    set_has_str_value();
    _impl_.value_.str_value_.InitDefault();
  }
  _impl_.value_.str_value_.Set(value, GetArenaForAllocation());
}
inline std::string* XStat::_internal_mutable_str_value() {
  if (!_internal_has_str_value()) {
    clear_value();
    set_has_str_value();
    _impl_.value_.str_value_.InitDefault();
  }
  return _impl_.value_.str_value_.Mutable(      GetArenaForAllocation());
}
inline std::string* XStat::release_str_value() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XStat.str_value)
  if (_internal_has_str_value()) {
    clear_has_value();
    return _impl_.value_.str_value_.Release();
  } else {
    return nullptr;
  }
}
inline void XStat::set_allocated_str_value(std::string* str_value) {
  if (has_value()) {
    clear_value();
  }
  if (str_value != nullptr) {
    set_has_str_value();
    _impl_.value_.str_value_.InitAllocated(str_value, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XStat.str_value)
}

// bytes bytes_value = 6;
inline bool XStat::_internal_has_bytes_value() const {
  return value_case() == kBytesValue;
}
inline bool XStat::has_bytes_value() const {
  return _internal_has_bytes_value();
}
inline void XStat::set_has_bytes_value() {
  _impl_._oneof_case_[0] = kBytesValue;
}
inline void XStat::clear_bytes_value() {
  if (_internal_has_bytes_value()) {
    _impl_.value_.bytes_value_.Destroy();
    clear_has_value();
  }
}
inline const std::string& XStat::bytes_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.bytes_value)
  return _internal_bytes_value();
}
template <typename ArgT0, typename... ArgT>
inline void XStat::set_bytes_value(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_bytes_value()) {
    clear_value();
    set_has_bytes_value();
    _impl_.value_.bytes_value_.InitDefault();
  }
  _impl_.value_.bytes_value_.SetBytes( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.bytes_value)
}
inline std::string* XStat::mutable_bytes_value() {
  std::string* _s = _internal_mutable_bytes_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XStat.bytes_value)
  return _s;
}
inline const std::string& XStat::_internal_bytes_value() const {
  if (_internal_has_bytes_value()) {
    return _impl_.value_.bytes_value_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void XStat::_internal_set_bytes_value(const std::string& value) {
  if (!_internal_has_bytes_value()) {
    clear_value();
    set_has_bytes_value();
    _impl_.value_.bytes_value_.InitDefault();
  }
  _impl_.value_.bytes_value_.Set(value, GetArenaForAllocation());
}
inline std::string* XStat::_internal_mutable_bytes_value() {
  if (!_internal_has_bytes_value()) {
    clear_value();
    set_has_bytes_value();
    _impl_.value_.bytes_value_.InitDefault();
  }
  return _impl_.value_.bytes_value_.Mutable(      GetArenaForAllocation());
}
inline std::string* XStat::release_bytes_value() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XStat.bytes_value)
  if (_internal_has_bytes_value()) {
    clear_has_value();
    return _impl_.value_.bytes_value_.Release();
  } else {
    return nullptr;
  }
}
inline void XStat::set_allocated_bytes_value(std::string* bytes_value) {
  if (has_value()) {
    clear_value();
  }
  if (bytes_value != nullptr) {
    set_has_bytes_value();
    _impl_.value_.bytes_value_.InitAllocated(bytes_value, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XStat.bytes_value)
}

// uint64 ref_value = 7;
inline bool XStat::_internal_has_ref_value() const {
  return value_case() == kRefValue;
}
inline bool XStat::has_ref_value() const {
  return _internal_has_ref_value();
}
inline void XStat::set_has_ref_value() {
  _impl_._oneof_case_[0] = kRefValue;
}
inline void XStat::clear_ref_value() {
  if (_internal_has_ref_value()) {
    _impl_.value_.ref_value_ = uint64_t{0u};
    clear_has_value();
  }
}
inline uint64_t XStat::_internal_ref_value() const {
  if (_internal_has_ref_value()) {
    return _impl_.value_.ref_value_;
  }
  return uint64_t{0u};
}
inline void XStat::_internal_set_ref_value(uint64_t value) {
  if (!_internal_has_ref_value()) {
    clear_value();
    set_has_ref_value();
  }
  _impl_.value_.ref_value_ = value;
}
inline uint64_t XStat::ref_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStat.ref_value)
  return _internal_ref_value();
}
inline void XStat::set_ref_value(uint64_t value) {
  _internal_set_ref_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStat.ref_value)
}

inline bool XStat::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void XStat::clear_has_value() {
  _impl_._oneof_case_[0] = VALUE_NOT_SET;
}
inline XStat::ValueCase XStat::value_case() const {
  return XStat::ValueCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// XEventMetadata

// int64 id = 1;
inline void XEventMetadata::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t XEventMetadata::_internal_id() const {
  return _impl_.id_;
}
inline int64_t XEventMetadata::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.id)
  return _internal_id();
}
inline void XEventMetadata::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void XEventMetadata::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEventMetadata.id)
}

// string name = 2;
inline void XEventMetadata::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& XEventMetadata::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XEventMetadata::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEventMetadata.name)
}
inline std::string* XEventMetadata::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XEventMetadata.name)
  return _s;
}
inline const std::string& XEventMetadata::_internal_name() const {
  return _impl_.name_.Get();
}
inline void XEventMetadata::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* XEventMetadata::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* XEventMetadata::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XEventMetadata.name)
  return _impl_.name_.Release();
}
inline void XEventMetadata::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XEventMetadata.name)
}

// string display_name = 4;
inline void XEventMetadata::clear_display_name() {
  _impl_.display_name_.ClearToEmpty();
}
inline const std::string& XEventMetadata::display_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.display_name)
  return _internal_display_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XEventMetadata::set_display_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.display_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEventMetadata.display_name)
}
inline std::string* XEventMetadata::mutable_display_name() {
  std::string* _s = _internal_mutable_display_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XEventMetadata.display_name)
  return _s;
}
inline const std::string& XEventMetadata::_internal_display_name() const {
  return _impl_.display_name_.Get();
}
inline void XEventMetadata::_internal_set_display_name(const std::string& value) {
  
  _impl_.display_name_.Set(value, GetArenaForAllocation());
}
inline std::string* XEventMetadata::_internal_mutable_display_name() {
  
  return _impl_.display_name_.Mutable(GetArenaForAllocation());
}
inline std::string* XEventMetadata::release_display_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XEventMetadata.display_name)
  return _impl_.display_name_.Release();
}
inline void XEventMetadata::set_allocated_display_name(std::string* display_name) {
  if (display_name != nullptr) {
    
  } else {
    
  }
  _impl_.display_name_.SetAllocated(display_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.display_name_.IsDefault()) {
    _impl_.display_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XEventMetadata.display_name)
}

// bytes metadata = 3;
inline void XEventMetadata::clear_metadata() {
  _impl_.metadata_.ClearToEmpty();
}
inline const std::string& XEventMetadata::metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.metadata)
  return _internal_metadata();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XEventMetadata::set_metadata(ArgT0&& arg0, ArgT... args) {
 
 _impl_.metadata_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEventMetadata.metadata)
}
inline std::string* XEventMetadata::mutable_metadata() {
  std::string* _s = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XEventMetadata.metadata)
  return _s;
}
inline const std::string& XEventMetadata::_internal_metadata() const {
  return _impl_.metadata_.Get();
}
inline void XEventMetadata::_internal_set_metadata(const std::string& value) {
  
  _impl_.metadata_.Set(value, GetArenaForAllocation());
}
inline std::string* XEventMetadata::_internal_mutable_metadata() {
  
  return _impl_.metadata_.Mutable(GetArenaForAllocation());
}
inline std::string* XEventMetadata::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XEventMetadata.metadata)
  return _impl_.metadata_.Release();
}
inline void XEventMetadata::set_allocated_metadata(std::string* metadata) {
  if (metadata != nullptr) {
    
  } else {
    
  }
  _impl_.metadata_.SetAllocated(metadata, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.metadata_.IsDefault()) {
    _impl_.metadata_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XEventMetadata.metadata)
}

// repeated .tensorflow.profiler.XStat stats = 5;
inline int XEventMetadata::_internal_stats_size() const {
  return _impl_.stats_.size();
}
inline int XEventMetadata::stats_size() const {
  return _internal_stats_size();
}
inline void XEventMetadata::clear_stats() {
  _impl_.stats_.Clear();
}
inline ::tensorflow::profiler::XStat* XEventMetadata::mutable_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XEventMetadata.stats)
  return _impl_.stats_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >*
XEventMetadata::mutable_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XEventMetadata.stats)
  return &_impl_.stats_;
}
inline const ::tensorflow::profiler::XStat& XEventMetadata::_internal_stats(int index) const {
  return _impl_.stats_.Get(index);
}
inline const ::tensorflow::profiler::XStat& XEventMetadata::stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.stats)
  return _internal_stats(index);
}
inline ::tensorflow::profiler::XStat* XEventMetadata::_internal_add_stats() {
  return _impl_.stats_.Add();
}
inline ::tensorflow::profiler::XStat* XEventMetadata::add_stats() {
  ::tensorflow::profiler::XStat* _add = _internal_add_stats();
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XEventMetadata.stats)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::profiler::XStat >&
XEventMetadata::stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XEventMetadata.stats)
  return _impl_.stats_;
}

// repeated int64 child_id = 6;
inline int XEventMetadata::_internal_child_id_size() const {
  return _impl_.child_id_.size();
}
inline int XEventMetadata::child_id_size() const {
  return _internal_child_id_size();
}
inline void XEventMetadata::clear_child_id() {
  _impl_.child_id_.Clear();
}
inline int64_t XEventMetadata::_internal_child_id(int index) const {
  return _impl_.child_id_.Get(index);
}
inline int64_t XEventMetadata::child_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XEventMetadata.child_id)
  return _internal_child_id(index);
}
inline void XEventMetadata::set_child_id(int index, int64_t value) {
  _impl_.child_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XEventMetadata.child_id)
}
inline void XEventMetadata::_internal_add_child_id(int64_t value) {
  _impl_.child_id_.Add(value);
}
inline void XEventMetadata::add_child_id(int64_t value) {
  _internal_add_child_id(value);
  // @@protoc_insertion_point(field_add:tensorflow.profiler.XEventMetadata.child_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
XEventMetadata::_internal_child_id() const {
  return _impl_.child_id_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
XEventMetadata::child_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.profiler.XEventMetadata.child_id)
  return _internal_child_id();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
XEventMetadata::_internal_mutable_child_id() {
  return &_impl_.child_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
XEventMetadata::mutable_child_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.profiler.XEventMetadata.child_id)
  return _internal_mutable_child_id();
}

// -------------------------------------------------------------------

// XStatMetadata

// int64 id = 1;
inline void XStatMetadata::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t XStatMetadata::_internal_id() const {
  return _impl_.id_;
}
inline int64_t XStatMetadata::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStatMetadata.id)
  return _internal_id();
}
inline void XStatMetadata::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void XStatMetadata::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStatMetadata.id)
}

// string name = 2;
inline void XStatMetadata::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& XStatMetadata::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStatMetadata.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XStatMetadata::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStatMetadata.name)
}
inline std::string* XStatMetadata::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XStatMetadata.name)
  return _s;
}
inline const std::string& XStatMetadata::_internal_name() const {
  return _impl_.name_.Get();
}
inline void XStatMetadata::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* XStatMetadata::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* XStatMetadata::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XStatMetadata.name)
  return _impl_.name_.Release();
}
inline void XStatMetadata::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XStatMetadata.name)
}

// string description = 3;
inline void XStatMetadata::clear_description() {
  _impl_.description_.ClearToEmpty();
}
inline const std::string& XStatMetadata::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.profiler.XStatMetadata.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XStatMetadata::set_description(ArgT0&& arg0, ArgT... args) {
 
 _impl_.description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.profiler.XStatMetadata.description)
}
inline std::string* XStatMetadata::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:tensorflow.profiler.XStatMetadata.description)
  return _s;
}
inline const std::string& XStatMetadata::_internal_description() const {
  return _impl_.description_.Get();
}
inline void XStatMetadata::_internal_set_description(const std::string& value) {
  
  _impl_.description_.Set(value, GetArenaForAllocation());
}
inline std::string* XStatMetadata::_internal_mutable_description() {
  
  return _impl_.description_.Mutable(GetArenaForAllocation());
}
inline std::string* XStatMetadata::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.profiler.XStatMetadata.description)
  return _impl_.description_.Release();
}
inline void XStatMetadata::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  _impl_.description_.SetAllocated(description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.description_.IsDefault()) {
    _impl_.description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.profiler.XStatMetadata.description)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace profiler
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fxplane_2eproto
