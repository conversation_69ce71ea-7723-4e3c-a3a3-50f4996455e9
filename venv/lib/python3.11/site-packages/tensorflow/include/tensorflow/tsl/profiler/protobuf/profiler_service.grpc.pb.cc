// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tsl/profiler/protobuf/profiler_service.proto

#include "tsl/profiler/protobuf/profiler_service.pb.h"
#include "tsl/profiler/protobuf/profiler_service.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace tensorflow {

static const char* grpcProfilerService_method_names[] = {
  "/tensorflow.ProfilerService/Profile",
  "/tensorflow.ProfilerService/Terminate",
  "/tensorflow.ProfilerService/Monitor",
};

std::unique_ptr< grpc::ProfilerService::Stub> grpc::ProfilerService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< grpc::ProfilerService::Stub> stub(new grpc::ProfilerService::Stub(channel));
  return stub;
}

grpc::ProfilerService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_Profile_(grpcProfilerService_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Terminate_(grpcProfilerService_method_names[1], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Monitor_(grpcProfilerService_method_names[2], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status grpc::ProfilerService::Stub::Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::tensorflow::ProfileResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_Profile_, context, request, response);
}

void grpc::ProfilerService::Stub::experimental_async::Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Profile_, context, request, response, std::move(f));
}

void grpc::ProfilerService::Stub::experimental_async::Profile(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Profile_, context, request, response, std::move(f));
}

void grpc::ProfilerService::Stub::experimental_async::Profile(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Profile_, context, request, response, reactor);
}

void grpc::ProfilerService::Stub::experimental_async::Profile(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ProfileResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Profile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileResponse>* grpc::ProfilerService::Stub::AsyncProfileRaw(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::ProfileResponse>::Create(channel_.get(), cq, rpcmethod_Profile_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::ProfileResponse>* grpc::ProfilerService::Stub::PrepareAsyncProfileRaw(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::ProfileResponse>::Create(channel_.get(), cq, rpcmethod_Profile_, context, request, false);
}

::grpc::Status grpc::ProfilerService::Stub::Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::tensorflow::TerminateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_Terminate_, context, request, response);
}

void grpc::ProfilerService::Stub::experimental_async::Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Terminate_, context, request, response, std::move(f));
}

void grpc::ProfilerService::Stub::experimental_async::Terminate(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TerminateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Terminate_, context, request, response, std::move(f));
}

void grpc::ProfilerService::Stub::experimental_async::Terminate(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Terminate_, context, request, response, reactor);
}

void grpc::ProfilerService::Stub::experimental_async::Terminate(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TerminateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Terminate_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::TerminateResponse>* grpc::ProfilerService::Stub::AsyncTerminateRaw(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::TerminateResponse>::Create(channel_.get(), cq, rpcmethod_Terminate_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::TerminateResponse>* grpc::ProfilerService::Stub::PrepareAsyncTerminateRaw(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::TerminateResponse>::Create(channel_.get(), cq, rpcmethod_Terminate_, context, request, false);
}

::grpc::Status grpc::ProfilerService::Stub::Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::tensorflow::MonitorResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_Monitor_, context, request, response);
}

void grpc::ProfilerService::Stub::experimental_async::Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Monitor_, context, request, response, std::move(f));
}

void grpc::ProfilerService::Stub::experimental_async::Monitor(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::MonitorResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Monitor_, context, request, response, std::move(f));
}

void grpc::ProfilerService::Stub::experimental_async::Monitor(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Monitor_, context, request, response, reactor);
}

void grpc::ProfilerService::Stub::experimental_async::Monitor(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::MonitorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Monitor_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::MonitorResponse>* grpc::ProfilerService::Stub::AsyncMonitorRaw(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::MonitorResponse>::Create(channel_.get(), cq, rpcmethod_Monitor_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::MonitorResponse>* grpc::ProfilerService::Stub::PrepareAsyncMonitorRaw(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::MonitorResponse>::Create(channel_.get(), cq, rpcmethod_Monitor_, context, request, false);
}

grpc::ProfilerService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcProfilerService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::ProfilerService::Service, ::tensorflow::ProfileRequest, ::tensorflow::ProfileResponse>(
          std::mem_fn(&grpc::ProfilerService::Service::Profile), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcProfilerService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::ProfilerService::Service, ::tensorflow::TerminateRequest, ::tensorflow::TerminateResponse>(
          std::mem_fn(&grpc::ProfilerService::Service::Terminate), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcProfilerService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::ProfilerService::Service, ::tensorflow::MonitorRequest, ::tensorflow::MonitorResponse>(
          std::mem_fn(&grpc::ProfilerService::Service::Monitor), this)));
}

grpc::ProfilerService::Service::~Service() {
}

::grpc::Status grpc::ProfilerService::Service::Profile(::grpc::ServerContext* context, const ::tensorflow::ProfileRequest* request, ::tensorflow::ProfileResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::ProfilerService::Service::Terminate(::grpc::ServerContext* context, const ::tensorflow::TerminateRequest* request, ::tensorflow::TerminateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::ProfilerService::Service::Monitor(::grpc::ServerContext* context, const ::tensorflow::MonitorRequest* request, ::tensorflow::MonitorResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace tensorflow

