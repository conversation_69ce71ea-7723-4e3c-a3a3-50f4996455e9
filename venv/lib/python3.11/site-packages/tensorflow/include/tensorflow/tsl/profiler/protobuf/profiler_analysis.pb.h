// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tsl/profiler/protobuf/profiler_analysis.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tsl/profiler/protobuf/profiler_service.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto;
namespace tensorflow {
class EnumProfileSessionsAndToolsRequest;
struct EnumProfileSessionsAndToolsRequestDefaultTypeInternal;
extern EnumProfileSessionsAndToolsRequestDefaultTypeInternal _EnumProfileSessionsAndToolsRequest_default_instance_;
class EnumProfileSessionsAndToolsResponse;
struct EnumProfileSessionsAndToolsResponseDefaultTypeInternal;
extern EnumProfileSessionsAndToolsResponseDefaultTypeInternal _EnumProfileSessionsAndToolsResponse_default_instance_;
class NewProfileSessionRequest;
struct NewProfileSessionRequestDefaultTypeInternal;
extern NewProfileSessionRequestDefaultTypeInternal _NewProfileSessionRequest_default_instance_;
class NewProfileSessionResponse;
struct NewProfileSessionResponseDefaultTypeInternal;
extern NewProfileSessionResponseDefaultTypeInternal _NewProfileSessionResponse_default_instance_;
class ProfileSessionDataRequest;
struct ProfileSessionDataRequestDefaultTypeInternal;
extern ProfileSessionDataRequestDefaultTypeInternal _ProfileSessionDataRequest_default_instance_;
class ProfileSessionDataRequest_ParametersEntry_DoNotUse;
struct ProfileSessionDataRequest_ParametersEntry_DoNotUseDefaultTypeInternal;
extern ProfileSessionDataRequest_ParametersEntry_DoNotUseDefaultTypeInternal _ProfileSessionDataRequest_ParametersEntry_DoNotUse_default_instance_;
class ProfileSessionDataResponse;
struct ProfileSessionDataResponseDefaultTypeInternal;
extern ProfileSessionDataResponseDefaultTypeInternal _ProfileSessionDataResponse_default_instance_;
class ProfileSessionInfo;
struct ProfileSessionInfoDefaultTypeInternal;
extern ProfileSessionInfoDefaultTypeInternal _ProfileSessionInfo_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::EnumProfileSessionsAndToolsRequest* Arena::CreateMaybeMessage<::tensorflow::EnumProfileSessionsAndToolsRequest>(Arena*);
template<> ::tensorflow::EnumProfileSessionsAndToolsResponse* Arena::CreateMaybeMessage<::tensorflow::EnumProfileSessionsAndToolsResponse>(Arena*);
template<> ::tensorflow::NewProfileSessionRequest* Arena::CreateMaybeMessage<::tensorflow::NewProfileSessionRequest>(Arena*);
template<> ::tensorflow::NewProfileSessionResponse* Arena::CreateMaybeMessage<::tensorflow::NewProfileSessionResponse>(Arena*);
template<> ::tensorflow::ProfileSessionDataRequest* Arena::CreateMaybeMessage<::tensorflow::ProfileSessionDataRequest>(Arena*);
template<> ::tensorflow::ProfileSessionDataRequest_ParametersEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::ProfileSessionDataRequest_ParametersEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ProfileSessionDataResponse* Arena::CreateMaybeMessage<::tensorflow::ProfileSessionDataResponse>(Arena*);
template<> ::tensorflow::ProfileSessionInfo* Arena::CreateMaybeMessage<::tensorflow::ProfileSessionInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class NewProfileSessionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NewProfileSessionRequest) */ {
 public:
  inline NewProfileSessionRequest() : NewProfileSessionRequest(nullptr) {}
  ~NewProfileSessionRequest() override;
  explicit PROTOBUF_CONSTEXPR NewProfileSessionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NewProfileSessionRequest(const NewProfileSessionRequest& from);
  NewProfileSessionRequest(NewProfileSessionRequest&& from) noexcept
    : NewProfileSessionRequest() {
    *this = ::std::move(from);
  }

  inline NewProfileSessionRequest& operator=(const NewProfileSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline NewProfileSessionRequest& operator=(NewProfileSessionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NewProfileSessionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const NewProfileSessionRequest* internal_default_instance() {
    return reinterpret_cast<const NewProfileSessionRequest*>(
               &_NewProfileSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(NewProfileSessionRequest& a, NewProfileSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(NewProfileSessionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NewProfileSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NewProfileSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NewProfileSessionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NewProfileSessionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NewProfileSessionRequest& from) {
    NewProfileSessionRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NewProfileSessionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NewProfileSessionRequest";
  }
  protected:
  explicit NewProfileSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHostsFieldNumber = 3,
    kRepositoryRootFieldNumber = 2,
    kSessionIdFieldNumber = 4,
    kRequestFieldNumber = 1,
  };
  // repeated string hosts = 3;
  int hosts_size() const;
  private:
  int _internal_hosts_size() const;
  public:
  void clear_hosts();
  const std::string& hosts(int index) const;
  std::string* mutable_hosts(int index);
  void set_hosts(int index, const std::string& value);
  void set_hosts(int index, std::string&& value);
  void set_hosts(int index, const char* value);
  void set_hosts(int index, const char* value, size_t size);
  std::string* add_hosts();
  void add_hosts(const std::string& value);
  void add_hosts(std::string&& value);
  void add_hosts(const char* value);
  void add_hosts(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& hosts() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_hosts();
  private:
  const std::string& _internal_hosts(int index) const;
  std::string* _internal_add_hosts();
  public:

  // string repository_root = 2;
  void clear_repository_root();
  const std::string& repository_root() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_repository_root(ArgT0&& arg0, ArgT... args);
  std::string* mutable_repository_root();
  PROTOBUF_NODISCARD std::string* release_repository_root();
  void set_allocated_repository_root(std::string* repository_root);
  private:
  const std::string& _internal_repository_root() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_repository_root(const std::string& value);
  std::string* _internal_mutable_repository_root();
  public:

  // string session_id = 4;
  void clear_session_id();
  const std::string& session_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_id();
  PROTOBUF_NODISCARD std::string* release_session_id();
  void set_allocated_session_id(std::string* session_id);
  private:
  const std::string& _internal_session_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_id(const std::string& value);
  std::string* _internal_mutable_session_id();
  public:

  // .tensorflow.ProfileRequest request = 1;
  bool has_request() const;
  private:
  bool _internal_has_request() const;
  public:
  void clear_request();
  const ::tensorflow::ProfileRequest& request() const;
  PROTOBUF_NODISCARD ::tensorflow::ProfileRequest* release_request();
  ::tensorflow::ProfileRequest* mutable_request();
  void set_allocated_request(::tensorflow::ProfileRequest* request);
  private:
  const ::tensorflow::ProfileRequest& _internal_request() const;
  ::tensorflow::ProfileRequest* _internal_mutable_request();
  public:
  void unsafe_arena_set_allocated_request(
      ::tensorflow::ProfileRequest* request);
  ::tensorflow::ProfileRequest* unsafe_arena_release_request();

  // @@protoc_insertion_point(class_scope:tensorflow.NewProfileSessionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> hosts_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr repository_root_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_id_;
    ::tensorflow::ProfileRequest* request_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class NewProfileSessionResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NewProfileSessionResponse) */ {
 public:
  inline NewProfileSessionResponse() : NewProfileSessionResponse(nullptr) {}
  ~NewProfileSessionResponse() override;
  explicit PROTOBUF_CONSTEXPR NewProfileSessionResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NewProfileSessionResponse(const NewProfileSessionResponse& from);
  NewProfileSessionResponse(NewProfileSessionResponse&& from) noexcept
    : NewProfileSessionResponse() {
    *this = ::std::move(from);
  }

  inline NewProfileSessionResponse& operator=(const NewProfileSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline NewProfileSessionResponse& operator=(NewProfileSessionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NewProfileSessionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const NewProfileSessionResponse* internal_default_instance() {
    return reinterpret_cast<const NewProfileSessionResponse*>(
               &_NewProfileSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(NewProfileSessionResponse& a, NewProfileSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(NewProfileSessionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NewProfileSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NewProfileSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NewProfileSessionResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NewProfileSessionResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NewProfileSessionResponse& from) {
    NewProfileSessionResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NewProfileSessionResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NewProfileSessionResponse";
  }
  protected:
  explicit NewProfileSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrorMessageFieldNumber = 1,
    kEmptyTraceFieldNumber = 2,
  };
  // string error_message = 1;
  void clear_error_message();
  const std::string& error_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_error_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_error_message();
  PROTOBUF_NODISCARD std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);
  private:
  const std::string& _internal_error_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_error_message(const std::string& value);
  std::string* _internal_mutable_error_message();
  public:

  // bool empty_trace = 2;
  void clear_empty_trace();
  bool empty_trace() const;
  void set_empty_trace(bool value);
  private:
  bool _internal_empty_trace() const;
  void _internal_set_empty_trace(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.NewProfileSessionResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
    bool empty_trace_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class EnumProfileSessionsAndToolsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.EnumProfileSessionsAndToolsRequest) */ {
 public:
  inline EnumProfileSessionsAndToolsRequest() : EnumProfileSessionsAndToolsRequest(nullptr) {}
  ~EnumProfileSessionsAndToolsRequest() override;
  explicit PROTOBUF_CONSTEXPR EnumProfileSessionsAndToolsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EnumProfileSessionsAndToolsRequest(const EnumProfileSessionsAndToolsRequest& from);
  EnumProfileSessionsAndToolsRequest(EnumProfileSessionsAndToolsRequest&& from) noexcept
    : EnumProfileSessionsAndToolsRequest() {
    *this = ::std::move(from);
  }

  inline EnumProfileSessionsAndToolsRequest& operator=(const EnumProfileSessionsAndToolsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnumProfileSessionsAndToolsRequest& operator=(EnumProfileSessionsAndToolsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EnumProfileSessionsAndToolsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const EnumProfileSessionsAndToolsRequest* internal_default_instance() {
    return reinterpret_cast<const EnumProfileSessionsAndToolsRequest*>(
               &_EnumProfileSessionsAndToolsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(EnumProfileSessionsAndToolsRequest& a, EnumProfileSessionsAndToolsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(EnumProfileSessionsAndToolsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EnumProfileSessionsAndToolsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EnumProfileSessionsAndToolsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EnumProfileSessionsAndToolsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EnumProfileSessionsAndToolsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const EnumProfileSessionsAndToolsRequest& from) {
    EnumProfileSessionsAndToolsRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnumProfileSessionsAndToolsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.EnumProfileSessionsAndToolsRequest";
  }
  protected:
  explicit EnumProfileSessionsAndToolsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepositoryRootFieldNumber = 1,
  };
  // string repository_root = 1;
  void clear_repository_root();
  const std::string& repository_root() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_repository_root(ArgT0&& arg0, ArgT... args);
  std::string* mutable_repository_root();
  PROTOBUF_NODISCARD std::string* release_repository_root();
  void set_allocated_repository_root(std::string* repository_root);
  private:
  const std::string& _internal_repository_root() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_repository_root(const std::string& value);
  std::string* _internal_mutable_repository_root();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.EnumProfileSessionsAndToolsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr repository_root_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class ProfileSessionInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileSessionInfo) */ {
 public:
  inline ProfileSessionInfo() : ProfileSessionInfo(nullptr) {}
  ~ProfileSessionInfo() override;
  explicit PROTOBUF_CONSTEXPR ProfileSessionInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileSessionInfo(const ProfileSessionInfo& from);
  ProfileSessionInfo(ProfileSessionInfo&& from) noexcept
    : ProfileSessionInfo() {
    *this = ::std::move(from);
  }

  inline ProfileSessionInfo& operator=(const ProfileSessionInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileSessionInfo& operator=(ProfileSessionInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileSessionInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileSessionInfo* internal_default_instance() {
    return reinterpret_cast<const ProfileSessionInfo*>(
               &_ProfileSessionInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ProfileSessionInfo& a, ProfileSessionInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileSessionInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileSessionInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileSessionInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileSessionInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileSessionInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProfileSessionInfo& from) {
    ProfileSessionInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileSessionInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileSessionInfo";
  }
  protected:
  explicit ProfileSessionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAvailableToolsFieldNumber = 2,
    kSessionIdFieldNumber = 1,
  };
  // repeated string available_tools = 2;
  int available_tools_size() const;
  private:
  int _internal_available_tools_size() const;
  public:
  void clear_available_tools();
  const std::string& available_tools(int index) const;
  std::string* mutable_available_tools(int index);
  void set_available_tools(int index, const std::string& value);
  void set_available_tools(int index, std::string&& value);
  void set_available_tools(int index, const char* value);
  void set_available_tools(int index, const char* value, size_t size);
  std::string* add_available_tools();
  void add_available_tools(const std::string& value);
  void add_available_tools(std::string&& value);
  void add_available_tools(const char* value);
  void add_available_tools(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& available_tools() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_available_tools();
  private:
  const std::string& _internal_available_tools(int index) const;
  std::string* _internal_add_available_tools();
  public:

  // string session_id = 1;
  void clear_session_id();
  const std::string& session_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_id();
  PROTOBUF_NODISCARD std::string* release_session_id();
  void set_allocated_session_id(std::string* session_id);
  private:
  const std::string& _internal_session_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_id(const std::string& value);
  std::string* _internal_mutable_session_id();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileSessionInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> available_tools_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class EnumProfileSessionsAndToolsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.EnumProfileSessionsAndToolsResponse) */ {
 public:
  inline EnumProfileSessionsAndToolsResponse() : EnumProfileSessionsAndToolsResponse(nullptr) {}
  ~EnumProfileSessionsAndToolsResponse() override;
  explicit PROTOBUF_CONSTEXPR EnumProfileSessionsAndToolsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EnumProfileSessionsAndToolsResponse(const EnumProfileSessionsAndToolsResponse& from);
  EnumProfileSessionsAndToolsResponse(EnumProfileSessionsAndToolsResponse&& from) noexcept
    : EnumProfileSessionsAndToolsResponse() {
    *this = ::std::move(from);
  }

  inline EnumProfileSessionsAndToolsResponse& operator=(const EnumProfileSessionsAndToolsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnumProfileSessionsAndToolsResponse& operator=(EnumProfileSessionsAndToolsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EnumProfileSessionsAndToolsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const EnumProfileSessionsAndToolsResponse* internal_default_instance() {
    return reinterpret_cast<const EnumProfileSessionsAndToolsResponse*>(
               &_EnumProfileSessionsAndToolsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(EnumProfileSessionsAndToolsResponse& a, EnumProfileSessionsAndToolsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(EnumProfileSessionsAndToolsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EnumProfileSessionsAndToolsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EnumProfileSessionsAndToolsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EnumProfileSessionsAndToolsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EnumProfileSessionsAndToolsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const EnumProfileSessionsAndToolsResponse& from) {
    EnumProfileSessionsAndToolsResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnumProfileSessionsAndToolsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.EnumProfileSessionsAndToolsResponse";
  }
  protected:
  explicit EnumProfileSessionsAndToolsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionsFieldNumber = 2,
    kErrorMessageFieldNumber = 1,
  };
  // repeated .tensorflow.ProfileSessionInfo sessions = 2;
  int sessions_size() const;
  private:
  int _internal_sessions_size() const;
  public:
  void clear_sessions();
  ::tensorflow::ProfileSessionInfo* mutable_sessions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileSessionInfo >*
      mutable_sessions();
  private:
  const ::tensorflow::ProfileSessionInfo& _internal_sessions(int index) const;
  ::tensorflow::ProfileSessionInfo* _internal_add_sessions();
  public:
  const ::tensorflow::ProfileSessionInfo& sessions(int index) const;
  ::tensorflow::ProfileSessionInfo* add_sessions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileSessionInfo >&
      sessions() const;

  // string error_message = 1;
  void clear_error_message();
  const std::string& error_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_error_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_error_message();
  PROTOBUF_NODISCARD std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);
  private:
  const std::string& _internal_error_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_error_message(const std::string& value);
  std::string* _internal_mutable_error_message();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.EnumProfileSessionsAndToolsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileSessionInfo > sessions_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class ProfileSessionDataRequest_ParametersEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileSessionDataRequest_ParametersEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileSessionDataRequest_ParametersEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  ProfileSessionDataRequest_ParametersEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileSessionDataRequest_ParametersEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileSessionDataRequest_ParametersEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileSessionDataRequest_ParametersEntry_DoNotUse& other);
  static const ProfileSessionDataRequest_ParametersEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileSessionDataRequest_ParametersEntry_DoNotUse*>(&_ProfileSessionDataRequest_ParametersEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ProfileSessionDataRequest.ParametersEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ProfileSessionDataRequest.ParametersEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto;
};

// -------------------------------------------------------------------

class ProfileSessionDataRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileSessionDataRequest) */ {
 public:
  inline ProfileSessionDataRequest() : ProfileSessionDataRequest(nullptr) {}
  ~ProfileSessionDataRequest() override;
  explicit PROTOBUF_CONSTEXPR ProfileSessionDataRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileSessionDataRequest(const ProfileSessionDataRequest& from);
  ProfileSessionDataRequest(ProfileSessionDataRequest&& from) noexcept
    : ProfileSessionDataRequest() {
    *this = ::std::move(from);
  }

  inline ProfileSessionDataRequest& operator=(const ProfileSessionDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileSessionDataRequest& operator=(ProfileSessionDataRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileSessionDataRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileSessionDataRequest* internal_default_instance() {
    return reinterpret_cast<const ProfileSessionDataRequest*>(
               &_ProfileSessionDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(ProfileSessionDataRequest& a, ProfileSessionDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileSessionDataRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileSessionDataRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileSessionDataRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileSessionDataRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileSessionDataRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProfileSessionDataRequest& from) {
    ProfileSessionDataRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileSessionDataRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileSessionDataRequest";
  }
  protected:
  explicit ProfileSessionDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kParametersFieldNumber = 4,
    kRepositoryRootFieldNumber = 1,
    kSessionIdFieldNumber = 2,
    kToolNameFieldNumber = 3,
    kHostNameFieldNumber = 5,
  };
  // map<string, string> parameters = 4;
  int parameters_size() const;
  private:
  int _internal_parameters_size() const;
  public:
  void clear_parameters();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_parameters() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_parameters();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      parameters() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_parameters();

  // string repository_root = 1;
  void clear_repository_root();
  const std::string& repository_root() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_repository_root(ArgT0&& arg0, ArgT... args);
  std::string* mutable_repository_root();
  PROTOBUF_NODISCARD std::string* release_repository_root();
  void set_allocated_repository_root(std::string* repository_root);
  private:
  const std::string& _internal_repository_root() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_repository_root(const std::string& value);
  std::string* _internal_mutable_repository_root();
  public:

  // string session_id = 2;
  void clear_session_id();
  const std::string& session_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_id();
  PROTOBUF_NODISCARD std::string* release_session_id();
  void set_allocated_session_id(std::string* session_id);
  private:
  const std::string& _internal_session_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_id(const std::string& value);
  std::string* _internal_mutable_session_id();
  public:

  // string tool_name = 3;
  void clear_tool_name();
  const std::string& tool_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tool_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tool_name();
  PROTOBUF_NODISCARD std::string* release_tool_name();
  void set_allocated_tool_name(std::string* tool_name);
  private:
  const std::string& _internal_tool_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tool_name(const std::string& value);
  std::string* _internal_mutable_tool_name();
  public:

  // string host_name = 5;
  void clear_host_name();
  const std::string& host_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_host_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_host_name();
  PROTOBUF_NODISCARD std::string* release_host_name();
  void set_allocated_host_name(std::string* host_name);
  private:
  const std::string& _internal_host_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_host_name(const std::string& value);
  std::string* _internal_mutable_host_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileSessionDataRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileSessionDataRequest_ParametersEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> parameters_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr repository_root_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tool_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto;
};
// -------------------------------------------------------------------

class ProfileSessionDataResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileSessionDataResponse) */ {
 public:
  inline ProfileSessionDataResponse() : ProfileSessionDataResponse(nullptr) {}
  ~ProfileSessionDataResponse() override;
  explicit PROTOBUF_CONSTEXPR ProfileSessionDataResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileSessionDataResponse(const ProfileSessionDataResponse& from);
  ProfileSessionDataResponse(ProfileSessionDataResponse&& from) noexcept
    : ProfileSessionDataResponse() {
    *this = ::std::move(from);
  }

  inline ProfileSessionDataResponse& operator=(const ProfileSessionDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileSessionDataResponse& operator=(ProfileSessionDataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileSessionDataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileSessionDataResponse* internal_default_instance() {
    return reinterpret_cast<const ProfileSessionDataResponse*>(
               &_ProfileSessionDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(ProfileSessionDataResponse& a, ProfileSessionDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileSessionDataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileSessionDataResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileSessionDataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileSessionDataResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileSessionDataResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProfileSessionDataResponse& from) {
    ProfileSessionDataResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileSessionDataResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileSessionDataResponse";
  }
  protected:
  explicit ProfileSessionDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrorMessageFieldNumber = 1,
    kOutputFormatFieldNumber = 2,
    kOutputFieldNumber = 3,
  };
  // string error_message = 1;
  void clear_error_message();
  const std::string& error_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_error_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_error_message();
  PROTOBUF_NODISCARD std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);
  private:
  const std::string& _internal_error_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_error_message(const std::string& value);
  std::string* _internal_mutable_error_message();
  public:

  // string output_format = 2;
  void clear_output_format();
  const std::string& output_format() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_output_format(ArgT0&& arg0, ArgT... args);
  std::string* mutable_output_format();
  PROTOBUF_NODISCARD std::string* release_output_format();
  void set_allocated_output_format(std::string* output_format);
  private:
  const std::string& _internal_output_format() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_output_format(const std::string& value);
  std::string* _internal_mutable_output_format();
  public:

  // bytes output = 3;
  void clear_output();
  const std::string& output() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_output(ArgT0&& arg0, ArgT... args);
  std::string* mutable_output();
  PROTOBUF_NODISCARD std::string* release_output();
  void set_allocated_output(std::string* output);
  private:
  const std::string& _internal_output() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_output(const std::string& value);
  std::string* _internal_mutable_output();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileSessionDataResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr output_format_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr output_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// NewProfileSessionRequest

// .tensorflow.ProfileRequest request = 1;
inline bool NewProfileSessionRequest::_internal_has_request() const {
  return this != internal_default_instance() && _impl_.request_ != nullptr;
}
inline bool NewProfileSessionRequest::has_request() const {
  return _internal_has_request();
}
inline const ::tensorflow::ProfileRequest& NewProfileSessionRequest::_internal_request() const {
  const ::tensorflow::ProfileRequest* p = _impl_.request_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ProfileRequest&>(
      ::tensorflow::_ProfileRequest_default_instance_);
}
inline const ::tensorflow::ProfileRequest& NewProfileSessionRequest::request() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionRequest.request)
  return _internal_request();
}
inline void NewProfileSessionRequest::unsafe_arena_set_allocated_request(
    ::tensorflow::ProfileRequest* request) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.request_);
  }
  _impl_.request_ = request;
  if (request) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NewProfileSessionRequest.request)
}
inline ::tensorflow::ProfileRequest* NewProfileSessionRequest::release_request() {
  
  ::tensorflow::ProfileRequest* temp = _impl_.request_;
  _impl_.request_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ProfileRequest* NewProfileSessionRequest::unsafe_arena_release_request() {
  // @@protoc_insertion_point(field_release:tensorflow.NewProfileSessionRequest.request)
  
  ::tensorflow::ProfileRequest* temp = _impl_.request_;
  _impl_.request_ = nullptr;
  return temp;
}
inline ::tensorflow::ProfileRequest* NewProfileSessionRequest::_internal_mutable_request() {
  
  if (_impl_.request_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ProfileRequest>(GetArenaForAllocation());
    _impl_.request_ = p;
  }
  return _impl_.request_;
}
inline ::tensorflow::ProfileRequest* NewProfileSessionRequest::mutable_request() {
  ::tensorflow::ProfileRequest* _msg = _internal_mutable_request();
  // @@protoc_insertion_point(field_mutable:tensorflow.NewProfileSessionRequest.request)
  return _msg;
}
inline void NewProfileSessionRequest::set_allocated_request(::tensorflow::ProfileRequest* request) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.request_);
  }
  if (request) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(request));
    if (message_arena != submessage_arena) {
      request = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, request, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.request_ = request;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewProfileSessionRequest.request)
}

// string repository_root = 2;
inline void NewProfileSessionRequest::clear_repository_root() {
  _impl_.repository_root_.ClearToEmpty();
}
inline const std::string& NewProfileSessionRequest::repository_root() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionRequest.repository_root)
  return _internal_repository_root();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NewProfileSessionRequest::set_repository_root(ArgT0&& arg0, ArgT... args) {
 
 _impl_.repository_root_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionRequest.repository_root)
}
inline std::string* NewProfileSessionRequest::mutable_repository_root() {
  std::string* _s = _internal_mutable_repository_root();
  // @@protoc_insertion_point(field_mutable:tensorflow.NewProfileSessionRequest.repository_root)
  return _s;
}
inline const std::string& NewProfileSessionRequest::_internal_repository_root() const {
  return _impl_.repository_root_.Get();
}
inline void NewProfileSessionRequest::_internal_set_repository_root(const std::string& value) {
  
  _impl_.repository_root_.Set(value, GetArenaForAllocation());
}
inline std::string* NewProfileSessionRequest::_internal_mutable_repository_root() {
  
  return _impl_.repository_root_.Mutable(GetArenaForAllocation());
}
inline std::string* NewProfileSessionRequest::release_repository_root() {
  // @@protoc_insertion_point(field_release:tensorflow.NewProfileSessionRequest.repository_root)
  return _impl_.repository_root_.Release();
}
inline void NewProfileSessionRequest::set_allocated_repository_root(std::string* repository_root) {
  if (repository_root != nullptr) {
    
  } else {
    
  }
  _impl_.repository_root_.SetAllocated(repository_root, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.repository_root_.IsDefault()) {
    _impl_.repository_root_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewProfileSessionRequest.repository_root)
}

// repeated string hosts = 3;
inline int NewProfileSessionRequest::_internal_hosts_size() const {
  return _impl_.hosts_.size();
}
inline int NewProfileSessionRequest::hosts_size() const {
  return _internal_hosts_size();
}
inline void NewProfileSessionRequest::clear_hosts() {
  _impl_.hosts_.Clear();
}
inline std::string* NewProfileSessionRequest::add_hosts() {
  std::string* _s = _internal_add_hosts();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.NewProfileSessionRequest.hosts)
  return _s;
}
inline const std::string& NewProfileSessionRequest::_internal_hosts(int index) const {
  return _impl_.hosts_.Get(index);
}
inline const std::string& NewProfileSessionRequest::hosts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionRequest.hosts)
  return _internal_hosts(index);
}
inline std::string* NewProfileSessionRequest::mutable_hosts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NewProfileSessionRequest.hosts)
  return _impl_.hosts_.Mutable(index);
}
inline void NewProfileSessionRequest::set_hosts(int index, const std::string& value) {
  _impl_.hosts_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionRequest.hosts)
}
inline void NewProfileSessionRequest::set_hosts(int index, std::string&& value) {
  _impl_.hosts_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionRequest.hosts)
}
inline void NewProfileSessionRequest::set_hosts(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.hosts_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.NewProfileSessionRequest.hosts)
}
inline void NewProfileSessionRequest::set_hosts(int index, const char* value, size_t size) {
  _impl_.hosts_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NewProfileSessionRequest.hosts)
}
inline std::string* NewProfileSessionRequest::_internal_add_hosts() {
  return _impl_.hosts_.Add();
}
inline void NewProfileSessionRequest::add_hosts(const std::string& value) {
  _impl_.hosts_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.NewProfileSessionRequest.hosts)
}
inline void NewProfileSessionRequest::add_hosts(std::string&& value) {
  _impl_.hosts_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.NewProfileSessionRequest.hosts)
}
inline void NewProfileSessionRequest::add_hosts(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.hosts_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.NewProfileSessionRequest.hosts)
}
inline void NewProfileSessionRequest::add_hosts(const char* value, size_t size) {
  _impl_.hosts_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.NewProfileSessionRequest.hosts)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
NewProfileSessionRequest::hosts() const {
  // @@protoc_insertion_point(field_list:tensorflow.NewProfileSessionRequest.hosts)
  return _impl_.hosts_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
NewProfileSessionRequest::mutable_hosts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NewProfileSessionRequest.hosts)
  return &_impl_.hosts_;
}

// string session_id = 4;
inline void NewProfileSessionRequest::clear_session_id() {
  _impl_.session_id_.ClearToEmpty();
}
inline const std::string& NewProfileSessionRequest::session_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionRequest.session_id)
  return _internal_session_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NewProfileSessionRequest::set_session_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionRequest.session_id)
}
inline std::string* NewProfileSessionRequest::mutable_session_id() {
  std::string* _s = _internal_mutable_session_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.NewProfileSessionRequest.session_id)
  return _s;
}
inline const std::string& NewProfileSessionRequest::_internal_session_id() const {
  return _impl_.session_id_.Get();
}
inline void NewProfileSessionRequest::_internal_set_session_id(const std::string& value) {
  
  _impl_.session_id_.Set(value, GetArenaForAllocation());
}
inline std::string* NewProfileSessionRequest::_internal_mutable_session_id() {
  
  return _impl_.session_id_.Mutable(GetArenaForAllocation());
}
inline std::string* NewProfileSessionRequest::release_session_id() {
  // @@protoc_insertion_point(field_release:tensorflow.NewProfileSessionRequest.session_id)
  return _impl_.session_id_.Release();
}
inline void NewProfileSessionRequest::set_allocated_session_id(std::string* session_id) {
  if (session_id != nullptr) {
    
  } else {
    
  }
  _impl_.session_id_.SetAllocated(session_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_id_.IsDefault()) {
    _impl_.session_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewProfileSessionRequest.session_id)
}

// -------------------------------------------------------------------

// NewProfileSessionResponse

// string error_message = 1;
inline void NewProfileSessionResponse::clear_error_message() {
  _impl_.error_message_.ClearToEmpty();
}
inline const std::string& NewProfileSessionResponse::error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionResponse.error_message)
  return _internal_error_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NewProfileSessionResponse::set_error_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.error_message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionResponse.error_message)
}
inline std::string* NewProfileSessionResponse::mutable_error_message() {
  std::string* _s = _internal_mutable_error_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.NewProfileSessionResponse.error_message)
  return _s;
}
inline const std::string& NewProfileSessionResponse::_internal_error_message() const {
  return _impl_.error_message_.Get();
}
inline void NewProfileSessionResponse::_internal_set_error_message(const std::string& value) {
  
  _impl_.error_message_.Set(value, GetArenaForAllocation());
}
inline std::string* NewProfileSessionResponse::_internal_mutable_error_message() {
  
  return _impl_.error_message_.Mutable(GetArenaForAllocation());
}
inline std::string* NewProfileSessionResponse::release_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.NewProfileSessionResponse.error_message)
  return _impl_.error_message_.Release();
}
inline void NewProfileSessionResponse::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  _impl_.error_message_.SetAllocated(error_message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.error_message_.IsDefault()) {
    _impl_.error_message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewProfileSessionResponse.error_message)
}

// bool empty_trace = 2;
inline void NewProfileSessionResponse::clear_empty_trace() {
  _impl_.empty_trace_ = false;
}
inline bool NewProfileSessionResponse::_internal_empty_trace() const {
  return _impl_.empty_trace_;
}
inline bool NewProfileSessionResponse::empty_trace() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewProfileSessionResponse.empty_trace)
  return _internal_empty_trace();
}
inline void NewProfileSessionResponse::_internal_set_empty_trace(bool value) {
  
  _impl_.empty_trace_ = value;
}
inline void NewProfileSessionResponse::set_empty_trace(bool value) {
  _internal_set_empty_trace(value);
  // @@protoc_insertion_point(field_set:tensorflow.NewProfileSessionResponse.empty_trace)
}

// -------------------------------------------------------------------

// EnumProfileSessionsAndToolsRequest

// string repository_root = 1;
inline void EnumProfileSessionsAndToolsRequest::clear_repository_root() {
  _impl_.repository_root_.ClearToEmpty();
}
inline const std::string& EnumProfileSessionsAndToolsRequest::repository_root() const {
  // @@protoc_insertion_point(field_get:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
  return _internal_repository_root();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumProfileSessionsAndToolsRequest::set_repository_root(ArgT0&& arg0, ArgT... args) {
 
 _impl_.repository_root_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
}
inline std::string* EnumProfileSessionsAndToolsRequest::mutable_repository_root() {
  std::string* _s = _internal_mutable_repository_root();
  // @@protoc_insertion_point(field_mutable:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
  return _s;
}
inline const std::string& EnumProfileSessionsAndToolsRequest::_internal_repository_root() const {
  return _impl_.repository_root_.Get();
}
inline void EnumProfileSessionsAndToolsRequest::_internal_set_repository_root(const std::string& value) {
  
  _impl_.repository_root_.Set(value, GetArenaForAllocation());
}
inline std::string* EnumProfileSessionsAndToolsRequest::_internal_mutable_repository_root() {
  
  return _impl_.repository_root_.Mutable(GetArenaForAllocation());
}
inline std::string* EnumProfileSessionsAndToolsRequest::release_repository_root() {
  // @@protoc_insertion_point(field_release:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
  return _impl_.repository_root_.Release();
}
inline void EnumProfileSessionsAndToolsRequest::set_allocated_repository_root(std::string* repository_root) {
  if (repository_root != nullptr) {
    
  } else {
    
  }
  _impl_.repository_root_.SetAllocated(repository_root, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.repository_root_.IsDefault()) {
    _impl_.repository_root_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EnumProfileSessionsAndToolsRequest.repository_root)
}

// -------------------------------------------------------------------

// ProfileSessionInfo

// string session_id = 1;
inline void ProfileSessionInfo::clear_session_id() {
  _impl_.session_id_.ClearToEmpty();
}
inline const std::string& ProfileSessionInfo::session_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionInfo.session_id)
  return _internal_session_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileSessionInfo::set_session_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionInfo.session_id)
}
inline std::string* ProfileSessionInfo::mutable_session_id() {
  std::string* _s = _internal_mutable_session_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionInfo.session_id)
  return _s;
}
inline const std::string& ProfileSessionInfo::_internal_session_id() const {
  return _impl_.session_id_.Get();
}
inline void ProfileSessionInfo::_internal_set_session_id(const std::string& value) {
  
  _impl_.session_id_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileSessionInfo::_internal_mutable_session_id() {
  
  return _impl_.session_id_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileSessionInfo::release_session_id() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionInfo.session_id)
  return _impl_.session_id_.Release();
}
inline void ProfileSessionInfo::set_allocated_session_id(std::string* session_id) {
  if (session_id != nullptr) {
    
  } else {
    
  }
  _impl_.session_id_.SetAllocated(session_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_id_.IsDefault()) {
    _impl_.session_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionInfo.session_id)
}

// repeated string available_tools = 2;
inline int ProfileSessionInfo::_internal_available_tools_size() const {
  return _impl_.available_tools_.size();
}
inline int ProfileSessionInfo::available_tools_size() const {
  return _internal_available_tools_size();
}
inline void ProfileSessionInfo::clear_available_tools() {
  _impl_.available_tools_.Clear();
}
inline std::string* ProfileSessionInfo::add_available_tools() {
  std::string* _s = _internal_add_available_tools();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ProfileSessionInfo.available_tools)
  return _s;
}
inline const std::string& ProfileSessionInfo::_internal_available_tools(int index) const {
  return _impl_.available_tools_.Get(index);
}
inline const std::string& ProfileSessionInfo::available_tools(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionInfo.available_tools)
  return _internal_available_tools(index);
}
inline std::string* ProfileSessionInfo::mutable_available_tools(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionInfo.available_tools)
  return _impl_.available_tools_.Mutable(index);
}
inline void ProfileSessionInfo::set_available_tools(int index, const std::string& value) {
  _impl_.available_tools_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionInfo.available_tools)
}
inline void ProfileSessionInfo::set_available_tools(int index, std::string&& value) {
  _impl_.available_tools_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionInfo.available_tools)
}
inline void ProfileSessionInfo::set_available_tools(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.available_tools_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileSessionInfo.available_tools)
}
inline void ProfileSessionInfo::set_available_tools(int index, const char* value, size_t size) {
  _impl_.available_tools_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileSessionInfo.available_tools)
}
inline std::string* ProfileSessionInfo::_internal_add_available_tools() {
  return _impl_.available_tools_.Add();
}
inline void ProfileSessionInfo::add_available_tools(const std::string& value) {
  _impl_.available_tools_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ProfileSessionInfo.available_tools)
}
inline void ProfileSessionInfo::add_available_tools(std::string&& value) {
  _impl_.available_tools_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ProfileSessionInfo.available_tools)
}
inline void ProfileSessionInfo::add_available_tools(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.available_tools_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ProfileSessionInfo.available_tools)
}
inline void ProfileSessionInfo::add_available_tools(const char* value, size_t size) {
  _impl_.available_tools_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ProfileSessionInfo.available_tools)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ProfileSessionInfo::available_tools() const {
  // @@protoc_insertion_point(field_list:tensorflow.ProfileSessionInfo.available_tools)
  return _impl_.available_tools_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ProfileSessionInfo::mutable_available_tools() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ProfileSessionInfo.available_tools)
  return &_impl_.available_tools_;
}

// -------------------------------------------------------------------

// EnumProfileSessionsAndToolsResponse

// string error_message = 1;
inline void EnumProfileSessionsAndToolsResponse::clear_error_message() {
  _impl_.error_message_.ClearToEmpty();
}
inline const std::string& EnumProfileSessionsAndToolsResponse::error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
  return _internal_error_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EnumProfileSessionsAndToolsResponse::set_error_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.error_message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
}
inline std::string* EnumProfileSessionsAndToolsResponse::mutable_error_message() {
  std::string* _s = _internal_mutable_error_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
  return _s;
}
inline const std::string& EnumProfileSessionsAndToolsResponse::_internal_error_message() const {
  return _impl_.error_message_.Get();
}
inline void EnumProfileSessionsAndToolsResponse::_internal_set_error_message(const std::string& value) {
  
  _impl_.error_message_.Set(value, GetArenaForAllocation());
}
inline std::string* EnumProfileSessionsAndToolsResponse::_internal_mutable_error_message() {
  
  return _impl_.error_message_.Mutable(GetArenaForAllocation());
}
inline std::string* EnumProfileSessionsAndToolsResponse::release_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
  return _impl_.error_message_.Release();
}
inline void EnumProfileSessionsAndToolsResponse::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  _impl_.error_message_.SetAllocated(error_message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.error_message_.IsDefault()) {
    _impl_.error_message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EnumProfileSessionsAndToolsResponse.error_message)
}

// repeated .tensorflow.ProfileSessionInfo sessions = 2;
inline int EnumProfileSessionsAndToolsResponse::_internal_sessions_size() const {
  return _impl_.sessions_.size();
}
inline int EnumProfileSessionsAndToolsResponse::sessions_size() const {
  return _internal_sessions_size();
}
inline void EnumProfileSessionsAndToolsResponse::clear_sessions() {
  _impl_.sessions_.Clear();
}
inline ::tensorflow::ProfileSessionInfo* EnumProfileSessionsAndToolsResponse::mutable_sessions(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.EnumProfileSessionsAndToolsResponse.sessions)
  return _impl_.sessions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileSessionInfo >*
EnumProfileSessionsAndToolsResponse::mutable_sessions() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.EnumProfileSessionsAndToolsResponse.sessions)
  return &_impl_.sessions_;
}
inline const ::tensorflow::ProfileSessionInfo& EnumProfileSessionsAndToolsResponse::_internal_sessions(int index) const {
  return _impl_.sessions_.Get(index);
}
inline const ::tensorflow::ProfileSessionInfo& EnumProfileSessionsAndToolsResponse::sessions(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.EnumProfileSessionsAndToolsResponse.sessions)
  return _internal_sessions(index);
}
inline ::tensorflow::ProfileSessionInfo* EnumProfileSessionsAndToolsResponse::_internal_add_sessions() {
  return _impl_.sessions_.Add();
}
inline ::tensorflow::ProfileSessionInfo* EnumProfileSessionsAndToolsResponse::add_sessions() {
  ::tensorflow::ProfileSessionInfo* _add = _internal_add_sessions();
  // @@protoc_insertion_point(field_add:tensorflow.EnumProfileSessionsAndToolsResponse.sessions)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileSessionInfo >&
EnumProfileSessionsAndToolsResponse::sessions() const {
  // @@protoc_insertion_point(field_list:tensorflow.EnumProfileSessionsAndToolsResponse.sessions)
  return _impl_.sessions_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProfileSessionDataRequest

// string repository_root = 1;
inline void ProfileSessionDataRequest::clear_repository_root() {
  _impl_.repository_root_.ClearToEmpty();
}
inline const std::string& ProfileSessionDataRequest::repository_root() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataRequest.repository_root)
  return _internal_repository_root();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileSessionDataRequest::set_repository_root(ArgT0&& arg0, ArgT... args) {
 
 _impl_.repository_root_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataRequest.repository_root)
}
inline std::string* ProfileSessionDataRequest::mutable_repository_root() {
  std::string* _s = _internal_mutable_repository_root();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataRequest.repository_root)
  return _s;
}
inline const std::string& ProfileSessionDataRequest::_internal_repository_root() const {
  return _impl_.repository_root_.Get();
}
inline void ProfileSessionDataRequest::_internal_set_repository_root(const std::string& value) {
  
  _impl_.repository_root_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileSessionDataRequest::_internal_mutable_repository_root() {
  
  return _impl_.repository_root_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileSessionDataRequest::release_repository_root() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataRequest.repository_root)
  return _impl_.repository_root_.Release();
}
inline void ProfileSessionDataRequest::set_allocated_repository_root(std::string* repository_root) {
  if (repository_root != nullptr) {
    
  } else {
    
  }
  _impl_.repository_root_.SetAllocated(repository_root, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.repository_root_.IsDefault()) {
    _impl_.repository_root_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataRequest.repository_root)
}

// string session_id = 2;
inline void ProfileSessionDataRequest::clear_session_id() {
  _impl_.session_id_.ClearToEmpty();
}
inline const std::string& ProfileSessionDataRequest::session_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataRequest.session_id)
  return _internal_session_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileSessionDataRequest::set_session_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataRequest.session_id)
}
inline std::string* ProfileSessionDataRequest::mutable_session_id() {
  std::string* _s = _internal_mutable_session_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataRequest.session_id)
  return _s;
}
inline const std::string& ProfileSessionDataRequest::_internal_session_id() const {
  return _impl_.session_id_.Get();
}
inline void ProfileSessionDataRequest::_internal_set_session_id(const std::string& value) {
  
  _impl_.session_id_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileSessionDataRequest::_internal_mutable_session_id() {
  
  return _impl_.session_id_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileSessionDataRequest::release_session_id() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataRequest.session_id)
  return _impl_.session_id_.Release();
}
inline void ProfileSessionDataRequest::set_allocated_session_id(std::string* session_id) {
  if (session_id != nullptr) {
    
  } else {
    
  }
  _impl_.session_id_.SetAllocated(session_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_id_.IsDefault()) {
    _impl_.session_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataRequest.session_id)
}

// string host_name = 5;
inline void ProfileSessionDataRequest::clear_host_name() {
  _impl_.host_name_.ClearToEmpty();
}
inline const std::string& ProfileSessionDataRequest::host_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataRequest.host_name)
  return _internal_host_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileSessionDataRequest::set_host_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.host_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataRequest.host_name)
}
inline std::string* ProfileSessionDataRequest::mutable_host_name() {
  std::string* _s = _internal_mutable_host_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataRequest.host_name)
  return _s;
}
inline const std::string& ProfileSessionDataRequest::_internal_host_name() const {
  return _impl_.host_name_.Get();
}
inline void ProfileSessionDataRequest::_internal_set_host_name(const std::string& value) {
  
  _impl_.host_name_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileSessionDataRequest::_internal_mutable_host_name() {
  
  return _impl_.host_name_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileSessionDataRequest::release_host_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataRequest.host_name)
  return _impl_.host_name_.Release();
}
inline void ProfileSessionDataRequest::set_allocated_host_name(std::string* host_name) {
  if (host_name != nullptr) {
    
  } else {
    
  }
  _impl_.host_name_.SetAllocated(host_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.host_name_.IsDefault()) {
    _impl_.host_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataRequest.host_name)
}

// string tool_name = 3;
inline void ProfileSessionDataRequest::clear_tool_name() {
  _impl_.tool_name_.ClearToEmpty();
}
inline const std::string& ProfileSessionDataRequest::tool_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataRequest.tool_name)
  return _internal_tool_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileSessionDataRequest::set_tool_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.tool_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataRequest.tool_name)
}
inline std::string* ProfileSessionDataRequest::mutable_tool_name() {
  std::string* _s = _internal_mutable_tool_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataRequest.tool_name)
  return _s;
}
inline const std::string& ProfileSessionDataRequest::_internal_tool_name() const {
  return _impl_.tool_name_.Get();
}
inline void ProfileSessionDataRequest::_internal_set_tool_name(const std::string& value) {
  
  _impl_.tool_name_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileSessionDataRequest::_internal_mutable_tool_name() {
  
  return _impl_.tool_name_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileSessionDataRequest::release_tool_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataRequest.tool_name)
  return _impl_.tool_name_.Release();
}
inline void ProfileSessionDataRequest::set_allocated_tool_name(std::string* tool_name) {
  if (tool_name != nullptr) {
    
  } else {
    
  }
  _impl_.tool_name_.SetAllocated(tool_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tool_name_.IsDefault()) {
    _impl_.tool_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataRequest.tool_name)
}

// map<string, string> parameters = 4;
inline int ProfileSessionDataRequest::_internal_parameters_size() const {
  return _impl_.parameters_.size();
}
inline int ProfileSessionDataRequest::parameters_size() const {
  return _internal_parameters_size();
}
inline void ProfileSessionDataRequest::clear_parameters() {
  _impl_.parameters_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
ProfileSessionDataRequest::_internal_parameters() const {
  return _impl_.parameters_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
ProfileSessionDataRequest::parameters() const {
  // @@protoc_insertion_point(field_map:tensorflow.ProfileSessionDataRequest.parameters)
  return _internal_parameters();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
ProfileSessionDataRequest::_internal_mutable_parameters() {
  return _impl_.parameters_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
ProfileSessionDataRequest::mutable_parameters() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.ProfileSessionDataRequest.parameters)
  return _internal_mutable_parameters();
}

// -------------------------------------------------------------------

// ProfileSessionDataResponse

// string error_message = 1;
inline void ProfileSessionDataResponse::clear_error_message() {
  _impl_.error_message_.ClearToEmpty();
}
inline const std::string& ProfileSessionDataResponse::error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataResponse.error_message)
  return _internal_error_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileSessionDataResponse::set_error_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.error_message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataResponse.error_message)
}
inline std::string* ProfileSessionDataResponse::mutable_error_message() {
  std::string* _s = _internal_mutable_error_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataResponse.error_message)
  return _s;
}
inline const std::string& ProfileSessionDataResponse::_internal_error_message() const {
  return _impl_.error_message_.Get();
}
inline void ProfileSessionDataResponse::_internal_set_error_message(const std::string& value) {
  
  _impl_.error_message_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileSessionDataResponse::_internal_mutable_error_message() {
  
  return _impl_.error_message_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileSessionDataResponse::release_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataResponse.error_message)
  return _impl_.error_message_.Release();
}
inline void ProfileSessionDataResponse::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  _impl_.error_message_.SetAllocated(error_message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.error_message_.IsDefault()) {
    _impl_.error_message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataResponse.error_message)
}

// string output_format = 2;
inline void ProfileSessionDataResponse::clear_output_format() {
  _impl_.output_format_.ClearToEmpty();
}
inline const std::string& ProfileSessionDataResponse::output_format() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataResponse.output_format)
  return _internal_output_format();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileSessionDataResponse::set_output_format(ArgT0&& arg0, ArgT... args) {
 
 _impl_.output_format_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataResponse.output_format)
}
inline std::string* ProfileSessionDataResponse::mutable_output_format() {
  std::string* _s = _internal_mutable_output_format();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataResponse.output_format)
  return _s;
}
inline const std::string& ProfileSessionDataResponse::_internal_output_format() const {
  return _impl_.output_format_.Get();
}
inline void ProfileSessionDataResponse::_internal_set_output_format(const std::string& value) {
  
  _impl_.output_format_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileSessionDataResponse::_internal_mutable_output_format() {
  
  return _impl_.output_format_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileSessionDataResponse::release_output_format() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataResponse.output_format)
  return _impl_.output_format_.Release();
}
inline void ProfileSessionDataResponse::set_allocated_output_format(std::string* output_format) {
  if (output_format != nullptr) {
    
  } else {
    
  }
  _impl_.output_format_.SetAllocated(output_format, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.output_format_.IsDefault()) {
    _impl_.output_format_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataResponse.output_format)
}

// bytes output = 3;
inline void ProfileSessionDataResponse::clear_output() {
  _impl_.output_.ClearToEmpty();
}
inline const std::string& ProfileSessionDataResponse::output() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileSessionDataResponse.output)
  return _internal_output();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileSessionDataResponse::set_output(ArgT0&& arg0, ArgT... args) {
 
 _impl_.output_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileSessionDataResponse.output)
}
inline std::string* ProfileSessionDataResponse::mutable_output() {
  std::string* _s = _internal_mutable_output();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileSessionDataResponse.output)
  return _s;
}
inline const std::string& ProfileSessionDataResponse::_internal_output() const {
  return _impl_.output_.Get();
}
inline void ProfileSessionDataResponse::_internal_set_output(const std::string& value) {
  
  _impl_.output_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileSessionDataResponse::_internal_mutable_output() {
  
  return _impl_.output_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileSessionDataResponse::release_output() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileSessionDataResponse.output)
  return _impl_.output_.Release();
}
inline void ProfileSessionDataResponse::set_allocated_output(std::string* output) {
  if (output != nullptr) {
    
  } else {
    
  }
  _impl_.output_.SetAllocated(output, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.output_.IsDefault()) {
    _impl_.output_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileSessionDataResponse.output)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5fanalysis_2eproto
