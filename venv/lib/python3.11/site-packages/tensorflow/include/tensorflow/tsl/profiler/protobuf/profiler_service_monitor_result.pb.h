// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tsl/profiler/protobuf/profiler_service_monitor_result.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_5fmonitor_5fresult_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_5fmonitor_5fresult_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_5fmonitor_5fresult_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_5fmonitor_5fresult_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_5fmonitor_5fresult_2eproto;
namespace tensorflow {
class ProfilerServiceMonitorResult;
struct ProfilerServiceMonitorResultDefaultTypeInternal;
extern ProfilerServiceMonitorResultDefaultTypeInternal _ProfilerServiceMonitorResult_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ProfilerServiceMonitorResult* Arena::CreateMaybeMessage<::tensorflow::ProfilerServiceMonitorResult>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum ProfilerServiceMonitorResult_ResponseType : int {
  ProfilerServiceMonitorResult_ResponseType_EMPTY_RESULT = 0,
  ProfilerServiceMonitorResult_ResponseType_UTIL_ONLY = 1,
  ProfilerServiceMonitorResult_ResponseType_UTIL_IDLE = 2,
  ProfilerServiceMonitorResult_ResponseType_UTIL_IDLE_STEP = 3,
  ProfilerServiceMonitorResult_ResponseType_ProfilerServiceMonitorResult_ResponseType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ProfilerServiceMonitorResult_ResponseType_ProfilerServiceMonitorResult_ResponseType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ProfilerServiceMonitorResult_ResponseType_IsValid(int value);
constexpr ProfilerServiceMonitorResult_ResponseType ProfilerServiceMonitorResult_ResponseType_ResponseType_MIN = ProfilerServiceMonitorResult_ResponseType_EMPTY_RESULT;
constexpr ProfilerServiceMonitorResult_ResponseType ProfilerServiceMonitorResult_ResponseType_ResponseType_MAX = ProfilerServiceMonitorResult_ResponseType_UTIL_IDLE_STEP;
constexpr int ProfilerServiceMonitorResult_ResponseType_ResponseType_ARRAYSIZE = ProfilerServiceMonitorResult_ResponseType_ResponseType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ProfilerServiceMonitorResult_ResponseType_descriptor();
template<typename T>
inline const std::string& ProfilerServiceMonitorResult_ResponseType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ProfilerServiceMonitorResult_ResponseType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ProfilerServiceMonitorResult_ResponseType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ProfilerServiceMonitorResult_ResponseType_descriptor(), enum_t_value);
}
inline bool ProfilerServiceMonitorResult_ResponseType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ProfilerServiceMonitorResult_ResponseType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ProfilerServiceMonitorResult_ResponseType>(
    ProfilerServiceMonitorResult_ResponseType_descriptor(), name, value);
}
// ===================================================================

class ProfilerServiceMonitorResult final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfilerServiceMonitorResult) */ {
 public:
  inline ProfilerServiceMonitorResult() : ProfilerServiceMonitorResult(nullptr) {}
  ~ProfilerServiceMonitorResult() override;
  explicit PROTOBUF_CONSTEXPR ProfilerServiceMonitorResult(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfilerServiceMonitorResult(const ProfilerServiceMonitorResult& from);
  ProfilerServiceMonitorResult(ProfilerServiceMonitorResult&& from) noexcept
    : ProfilerServiceMonitorResult() {
    *this = ::std::move(from);
  }

  inline ProfilerServiceMonitorResult& operator=(const ProfilerServiceMonitorResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfilerServiceMonitorResult& operator=(ProfilerServiceMonitorResult&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfilerServiceMonitorResult& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfilerServiceMonitorResult* internal_default_instance() {
    return reinterpret_cast<const ProfilerServiceMonitorResult*>(
               &_ProfilerServiceMonitorResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ProfilerServiceMonitorResult& a, ProfilerServiceMonitorResult& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfilerServiceMonitorResult* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfilerServiceMonitorResult* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfilerServiceMonitorResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfilerServiceMonitorResult>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfilerServiceMonitorResult& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProfilerServiceMonitorResult& from) {
    ProfilerServiceMonitorResult::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfilerServiceMonitorResult* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfilerServiceMonitorResult";
  }
  protected:
  explicit ProfilerServiceMonitorResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ProfilerServiceMonitorResult_ResponseType ResponseType;
  static constexpr ResponseType EMPTY_RESULT =
    ProfilerServiceMonitorResult_ResponseType_EMPTY_RESULT;
  static constexpr ResponseType UTIL_ONLY =
    ProfilerServiceMonitorResult_ResponseType_UTIL_ONLY;
  static constexpr ResponseType UTIL_IDLE =
    ProfilerServiceMonitorResult_ResponseType_UTIL_IDLE;
  static constexpr ResponseType UTIL_IDLE_STEP =
    ProfilerServiceMonitorResult_ResponseType_UTIL_IDLE_STEP;
  static inline bool ResponseType_IsValid(int value) {
    return ProfilerServiceMonitorResult_ResponseType_IsValid(value);
  }
  static constexpr ResponseType ResponseType_MIN =
    ProfilerServiceMonitorResult_ResponseType_ResponseType_MIN;
  static constexpr ResponseType ResponseType_MAX =
    ProfilerServiceMonitorResult_ResponseType_ResponseType_MAX;
  static constexpr int ResponseType_ARRAYSIZE =
    ProfilerServiceMonitorResult_ResponseType_ResponseType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ResponseType_descriptor() {
    return ProfilerServiceMonitorResult_ResponseType_descriptor();
  }
  template<typename T>
  static inline const std::string& ResponseType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ResponseType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ResponseType_Name.");
    return ProfilerServiceMonitorResult_ResponseType_Name(enum_t_value);
  }
  static inline bool ResponseType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      ResponseType* value) {
    return ProfilerServiceMonitorResult_ResponseType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceIdleTimePercentFieldNumber = 2,
    kMatrixUnitUtilizationPercentFieldNumber = 3,
    kStepTimeMsAvgFieldNumber = 4,
    kStepTimeMsMinFieldNumber = 5,
    kStepTimeMsMaxFieldNumber = 6,
    kInfeedPercentAvgFieldNumber = 7,
    kInfeedPercentMinFieldNumber = 8,
    kInfeedPercentMaxFieldNumber = 9,
    kResponseTypeFieldNumber = 1,
  };
  // double device_idle_time_percent = 2;
  void clear_device_idle_time_percent();
  double device_idle_time_percent() const;
  void set_device_idle_time_percent(double value);
  private:
  double _internal_device_idle_time_percent() const;
  void _internal_set_device_idle_time_percent(double value);
  public:

  // double matrix_unit_utilization_percent = 3;
  void clear_matrix_unit_utilization_percent();
  double matrix_unit_utilization_percent() const;
  void set_matrix_unit_utilization_percent(double value);
  private:
  double _internal_matrix_unit_utilization_percent() const;
  void _internal_set_matrix_unit_utilization_percent(double value);
  public:

  // double step_time_ms_avg = 4;
  void clear_step_time_ms_avg();
  double step_time_ms_avg() const;
  void set_step_time_ms_avg(double value);
  private:
  double _internal_step_time_ms_avg() const;
  void _internal_set_step_time_ms_avg(double value);
  public:

  // double step_time_ms_min = 5;
  void clear_step_time_ms_min();
  double step_time_ms_min() const;
  void set_step_time_ms_min(double value);
  private:
  double _internal_step_time_ms_min() const;
  void _internal_set_step_time_ms_min(double value);
  public:

  // double step_time_ms_max = 6;
  void clear_step_time_ms_max();
  double step_time_ms_max() const;
  void set_step_time_ms_max(double value);
  private:
  double _internal_step_time_ms_max() const;
  void _internal_set_step_time_ms_max(double value);
  public:

  // double infeed_percent_avg = 7;
  void clear_infeed_percent_avg();
  double infeed_percent_avg() const;
  void set_infeed_percent_avg(double value);
  private:
  double _internal_infeed_percent_avg() const;
  void _internal_set_infeed_percent_avg(double value);
  public:

  // double infeed_percent_min = 8;
  void clear_infeed_percent_min();
  double infeed_percent_min() const;
  void set_infeed_percent_min(double value);
  private:
  double _internal_infeed_percent_min() const;
  void _internal_set_infeed_percent_min(double value);
  public:

  // double infeed_percent_max = 9;
  void clear_infeed_percent_max();
  double infeed_percent_max() const;
  void set_infeed_percent_max(double value);
  private:
  double _internal_infeed_percent_max() const;
  void _internal_set_infeed_percent_max(double value);
  public:

  // .tensorflow.ProfilerServiceMonitorResult.ResponseType response_type = 1;
  void clear_response_type();
  ::tensorflow::ProfilerServiceMonitorResult_ResponseType response_type() const;
  void set_response_type(::tensorflow::ProfilerServiceMonitorResult_ResponseType value);
  private:
  ::tensorflow::ProfilerServiceMonitorResult_ResponseType _internal_response_type() const;
  void _internal_set_response_type(::tensorflow::ProfilerServiceMonitorResult_ResponseType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ProfilerServiceMonitorResult)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double device_idle_time_percent_;
    double matrix_unit_utilization_percent_;
    double step_time_ms_avg_;
    double step_time_ms_min_;
    double step_time_ms_max_;
    double infeed_percent_avg_;
    double infeed_percent_min_;
    double infeed_percent_max_;
    int response_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_5fmonitor_5fresult_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ProfilerServiceMonitorResult

// .tensorflow.ProfilerServiceMonitorResult.ResponseType response_type = 1;
inline void ProfilerServiceMonitorResult::clear_response_type() {
  _impl_.response_type_ = 0;
}
inline ::tensorflow::ProfilerServiceMonitorResult_ResponseType ProfilerServiceMonitorResult::_internal_response_type() const {
  return static_cast< ::tensorflow::ProfilerServiceMonitorResult_ResponseType >(_impl_.response_type_);
}
inline ::tensorflow::ProfilerServiceMonitorResult_ResponseType ProfilerServiceMonitorResult::response_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfilerServiceMonitorResult.response_type)
  return _internal_response_type();
}
inline void ProfilerServiceMonitorResult::_internal_set_response_type(::tensorflow::ProfilerServiceMonitorResult_ResponseType value) {
  
  _impl_.response_type_ = value;
}
inline void ProfilerServiceMonitorResult::set_response_type(::tensorflow::ProfilerServiceMonitorResult_ResponseType value) {
  _internal_set_response_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfilerServiceMonitorResult.response_type)
}

// double device_idle_time_percent = 2;
inline void ProfilerServiceMonitorResult::clear_device_idle_time_percent() {
  _impl_.device_idle_time_percent_ = 0;
}
inline double ProfilerServiceMonitorResult::_internal_device_idle_time_percent() const {
  return _impl_.device_idle_time_percent_;
}
inline double ProfilerServiceMonitorResult::device_idle_time_percent() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfilerServiceMonitorResult.device_idle_time_percent)
  return _internal_device_idle_time_percent();
}
inline void ProfilerServiceMonitorResult::_internal_set_device_idle_time_percent(double value) {
  
  _impl_.device_idle_time_percent_ = value;
}
inline void ProfilerServiceMonitorResult::set_device_idle_time_percent(double value) {
  _internal_set_device_idle_time_percent(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfilerServiceMonitorResult.device_idle_time_percent)
}

// double matrix_unit_utilization_percent = 3;
inline void ProfilerServiceMonitorResult::clear_matrix_unit_utilization_percent() {
  _impl_.matrix_unit_utilization_percent_ = 0;
}
inline double ProfilerServiceMonitorResult::_internal_matrix_unit_utilization_percent() const {
  return _impl_.matrix_unit_utilization_percent_;
}
inline double ProfilerServiceMonitorResult::matrix_unit_utilization_percent() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfilerServiceMonitorResult.matrix_unit_utilization_percent)
  return _internal_matrix_unit_utilization_percent();
}
inline void ProfilerServiceMonitorResult::_internal_set_matrix_unit_utilization_percent(double value) {
  
  _impl_.matrix_unit_utilization_percent_ = value;
}
inline void ProfilerServiceMonitorResult::set_matrix_unit_utilization_percent(double value) {
  _internal_set_matrix_unit_utilization_percent(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfilerServiceMonitorResult.matrix_unit_utilization_percent)
}

// double step_time_ms_avg = 4;
inline void ProfilerServiceMonitorResult::clear_step_time_ms_avg() {
  _impl_.step_time_ms_avg_ = 0;
}
inline double ProfilerServiceMonitorResult::_internal_step_time_ms_avg() const {
  return _impl_.step_time_ms_avg_;
}
inline double ProfilerServiceMonitorResult::step_time_ms_avg() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfilerServiceMonitorResult.step_time_ms_avg)
  return _internal_step_time_ms_avg();
}
inline void ProfilerServiceMonitorResult::_internal_set_step_time_ms_avg(double value) {
  
  _impl_.step_time_ms_avg_ = value;
}
inline void ProfilerServiceMonitorResult::set_step_time_ms_avg(double value) {
  _internal_set_step_time_ms_avg(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfilerServiceMonitorResult.step_time_ms_avg)
}

// double step_time_ms_min = 5;
inline void ProfilerServiceMonitorResult::clear_step_time_ms_min() {
  _impl_.step_time_ms_min_ = 0;
}
inline double ProfilerServiceMonitorResult::_internal_step_time_ms_min() const {
  return _impl_.step_time_ms_min_;
}
inline double ProfilerServiceMonitorResult::step_time_ms_min() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfilerServiceMonitorResult.step_time_ms_min)
  return _internal_step_time_ms_min();
}
inline void ProfilerServiceMonitorResult::_internal_set_step_time_ms_min(double value) {
  
  _impl_.step_time_ms_min_ = value;
}
inline void ProfilerServiceMonitorResult::set_step_time_ms_min(double value) {
  _internal_set_step_time_ms_min(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfilerServiceMonitorResult.step_time_ms_min)
}

// double step_time_ms_max = 6;
inline void ProfilerServiceMonitorResult::clear_step_time_ms_max() {
  _impl_.step_time_ms_max_ = 0;
}
inline double ProfilerServiceMonitorResult::_internal_step_time_ms_max() const {
  return _impl_.step_time_ms_max_;
}
inline double ProfilerServiceMonitorResult::step_time_ms_max() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfilerServiceMonitorResult.step_time_ms_max)
  return _internal_step_time_ms_max();
}
inline void ProfilerServiceMonitorResult::_internal_set_step_time_ms_max(double value) {
  
  _impl_.step_time_ms_max_ = value;
}
inline void ProfilerServiceMonitorResult::set_step_time_ms_max(double value) {
  _internal_set_step_time_ms_max(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfilerServiceMonitorResult.step_time_ms_max)
}

// double infeed_percent_avg = 7;
inline void ProfilerServiceMonitorResult::clear_infeed_percent_avg() {
  _impl_.infeed_percent_avg_ = 0;
}
inline double ProfilerServiceMonitorResult::_internal_infeed_percent_avg() const {
  return _impl_.infeed_percent_avg_;
}
inline double ProfilerServiceMonitorResult::infeed_percent_avg() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfilerServiceMonitorResult.infeed_percent_avg)
  return _internal_infeed_percent_avg();
}
inline void ProfilerServiceMonitorResult::_internal_set_infeed_percent_avg(double value) {
  
  _impl_.infeed_percent_avg_ = value;
}
inline void ProfilerServiceMonitorResult::set_infeed_percent_avg(double value) {
  _internal_set_infeed_percent_avg(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfilerServiceMonitorResult.infeed_percent_avg)
}

// double infeed_percent_min = 8;
inline void ProfilerServiceMonitorResult::clear_infeed_percent_min() {
  _impl_.infeed_percent_min_ = 0;
}
inline double ProfilerServiceMonitorResult::_internal_infeed_percent_min() const {
  return _impl_.infeed_percent_min_;
}
inline double ProfilerServiceMonitorResult::infeed_percent_min() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfilerServiceMonitorResult.infeed_percent_min)
  return _internal_infeed_percent_min();
}
inline void ProfilerServiceMonitorResult::_internal_set_infeed_percent_min(double value) {
  
  _impl_.infeed_percent_min_ = value;
}
inline void ProfilerServiceMonitorResult::set_infeed_percent_min(double value) {
  _internal_set_infeed_percent_min(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfilerServiceMonitorResult.infeed_percent_min)
}

// double infeed_percent_max = 9;
inline void ProfilerServiceMonitorResult::clear_infeed_percent_max() {
  _impl_.infeed_percent_max_ = 0;
}
inline double ProfilerServiceMonitorResult::_internal_infeed_percent_max() const {
  return _impl_.infeed_percent_max_;
}
inline double ProfilerServiceMonitorResult::infeed_percent_max() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfilerServiceMonitorResult.infeed_percent_max)
  return _internal_infeed_percent_max();
}
inline void ProfilerServiceMonitorResult::_internal_set_infeed_percent_max(double value) {
  
  _impl_.infeed_percent_max_ = value;
}
inline void ProfilerServiceMonitorResult::set_infeed_percent_max(double value) {
  _internal_set_infeed_percent_max(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfilerServiceMonitorResult.infeed_percent_max)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::ProfilerServiceMonitorResult_ResponseType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::ProfilerServiceMonitorResult_ResponseType>() {
  return ::tensorflow::ProfilerServiceMonitorResult_ResponseType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_5fmonitor_5fresult_2eproto
