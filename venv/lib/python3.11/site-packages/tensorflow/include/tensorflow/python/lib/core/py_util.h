/* Copyright 2015 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_PYTHON_LIB_CORE_PY_UTIL_H_
#define TENSORFLOW_PYTHON_LIB_CORE_PY_UTIL_H_

#include <Python.h>

#include "tensorflow/core/platform/logging.h"
#include "tensorflow/core/platform/types.h"

namespace tensorflow {

// Fetch the exception message as a string. An exception must be set
// (PyErr_Occurred() must be true).
string PyExceptionFetch();

// Assert that Python GIL is held.
inline void DCheckPyGilState() {
#if PY_MAJOR_VERSION >= 3 && PY_MINOR_VERSION >= 4
  DCHECK(PyGILState_Check());
#endif
}

}  // namespace tensorflow

#endif  // TENSORFLOW_PYTHON_LIB_CORE_PY_UTIL_H_
