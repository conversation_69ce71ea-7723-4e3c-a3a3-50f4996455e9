/* Copyright 2022 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

syntax = "proto3";

package tensorflow;

option go_package = "github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto";

// Describes the dimension numbers for Convolution op. Corresponds to
// ::mlir::mhlo::ConvDimensionNumbersAttr.
message UniformQuantizedConvolutionDimensionNumbersAttr {
  // The dimension that represents batch in the input.
  int64 input_batch_dimension = 1;
  // The dimension that represents features in the input.
  int64 input_feature_dimension = 2;
  // The dimensions that represents spatial dimensions in the input. Length must
  // be rank-2 for the tensor rank for Convolution op.
  repeated int64 input_spatial_dimensions = 3;

  // The dimension that represents input features in the kernel (rhs).
  int64 kernel_input_feature_dimension = 4;
  // The dimension that represents output features in the kernel (rhs).
  int64 kernel_output_feature_dimension = 5;
  // The dimensions that represents spatial dimensions in the kernel (rhs).
  // Length must be rank-2 for the tensor rank for Convolution op.
  repeated int64 kernel_spatial_dimensions = 6;

  // The dimension that represents batch in the output.
  int64 output_batch_dimension = 7;
  // The dimension that represents features in the output.
  int64 output_feature_dimension = 8;
  // The dimensions that represents spatial dimensions in the output. Length
  // must be rank-2 for the tensor rank for Convolution op.
  repeated int64 output_spatial_dimensions = 9;
}
