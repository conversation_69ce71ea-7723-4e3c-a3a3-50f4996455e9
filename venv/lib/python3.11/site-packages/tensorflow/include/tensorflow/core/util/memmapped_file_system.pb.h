// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/memmapped_file_system.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fmemmapped_5ffile_5fsystem_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fmemmapped_5ffile_5fsystem_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2futil_2fmemmapped_5ffile_5fsystem_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2futil_2fmemmapped_5ffile_5fsystem_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2futil_2fmemmapped_5ffile_5fsystem_2eproto;
namespace tensorflow {
class MemmappedFileSystemDirectory;
struct MemmappedFileSystemDirectoryDefaultTypeInternal;
extern MemmappedFileSystemDirectoryDefaultTypeInternal _MemmappedFileSystemDirectory_default_instance_;
class MemmappedFileSystemDirectoryElement;
struct MemmappedFileSystemDirectoryElementDefaultTypeInternal;
extern MemmappedFileSystemDirectoryElementDefaultTypeInternal _MemmappedFileSystemDirectoryElement_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::MemmappedFileSystemDirectory* Arena::CreateMaybeMessage<::tensorflow::MemmappedFileSystemDirectory>(Arena*);
template<> ::tensorflow::MemmappedFileSystemDirectoryElement* Arena::CreateMaybeMessage<::tensorflow::MemmappedFileSystemDirectoryElement>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class MemmappedFileSystemDirectoryElement final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemmappedFileSystemDirectoryElement) */ {
 public:
  inline MemmappedFileSystemDirectoryElement() : MemmappedFileSystemDirectoryElement(nullptr) {}
  ~MemmappedFileSystemDirectoryElement() override;
  explicit PROTOBUF_CONSTEXPR MemmappedFileSystemDirectoryElement(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemmappedFileSystemDirectoryElement(const MemmappedFileSystemDirectoryElement& from);
  MemmappedFileSystemDirectoryElement(MemmappedFileSystemDirectoryElement&& from) noexcept
    : MemmappedFileSystemDirectoryElement() {
    *this = ::std::move(from);
  }

  inline MemmappedFileSystemDirectoryElement& operator=(const MemmappedFileSystemDirectoryElement& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemmappedFileSystemDirectoryElement& operator=(MemmappedFileSystemDirectoryElement&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemmappedFileSystemDirectoryElement& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemmappedFileSystemDirectoryElement* internal_default_instance() {
    return reinterpret_cast<const MemmappedFileSystemDirectoryElement*>(
               &_MemmappedFileSystemDirectoryElement_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MemmappedFileSystemDirectoryElement& a, MemmappedFileSystemDirectoryElement& b) {
    a.Swap(&b);
  }
  inline void Swap(MemmappedFileSystemDirectoryElement* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemmappedFileSystemDirectoryElement* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemmappedFileSystemDirectoryElement* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemmappedFileSystemDirectoryElement>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemmappedFileSystemDirectoryElement& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemmappedFileSystemDirectoryElement& from) {
    MemmappedFileSystemDirectoryElement::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemmappedFileSystemDirectoryElement* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemmappedFileSystemDirectoryElement";
  }
  protected:
  explicit MemmappedFileSystemDirectoryElement(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 2,
    kOffsetFieldNumber = 1,
    kLengthFieldNumber = 3,
  };
  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // uint64 offset = 1;
  void clear_offset();
  uint64_t offset() const;
  void set_offset(uint64_t value);
  private:
  uint64_t _internal_offset() const;
  void _internal_set_offset(uint64_t value);
  public:

  // uint64 length = 3;
  void clear_length();
  uint64_t length() const;
  void set_length(uint64_t value);
  private:
  uint64_t _internal_length() const;
  void _internal_set_length(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemmappedFileSystemDirectoryElement)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    uint64_t offset_;
    uint64_t length_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fmemmapped_5ffile_5fsystem_2eproto;
};
// -------------------------------------------------------------------

class MemmappedFileSystemDirectory final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemmappedFileSystemDirectory) */ {
 public:
  inline MemmappedFileSystemDirectory() : MemmappedFileSystemDirectory(nullptr) {}
  ~MemmappedFileSystemDirectory() override;
  explicit PROTOBUF_CONSTEXPR MemmappedFileSystemDirectory(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemmappedFileSystemDirectory(const MemmappedFileSystemDirectory& from);
  MemmappedFileSystemDirectory(MemmappedFileSystemDirectory&& from) noexcept
    : MemmappedFileSystemDirectory() {
    *this = ::std::move(from);
  }

  inline MemmappedFileSystemDirectory& operator=(const MemmappedFileSystemDirectory& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemmappedFileSystemDirectory& operator=(MemmappedFileSystemDirectory&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemmappedFileSystemDirectory& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemmappedFileSystemDirectory* internal_default_instance() {
    return reinterpret_cast<const MemmappedFileSystemDirectory*>(
               &_MemmappedFileSystemDirectory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MemmappedFileSystemDirectory& a, MemmappedFileSystemDirectory& b) {
    a.Swap(&b);
  }
  inline void Swap(MemmappedFileSystemDirectory* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemmappedFileSystemDirectory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemmappedFileSystemDirectory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemmappedFileSystemDirectory>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemmappedFileSystemDirectory& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemmappedFileSystemDirectory& from) {
    MemmappedFileSystemDirectory::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemmappedFileSystemDirectory* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemmappedFileSystemDirectory";
  }
  protected:
  explicit MemmappedFileSystemDirectory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kElementFieldNumber = 1,
  };
  // repeated .tensorflow.MemmappedFileSystemDirectoryElement element = 1;
  int element_size() const;
  private:
  int _internal_element_size() const;
  public:
  void clear_element();
  ::tensorflow::MemmappedFileSystemDirectoryElement* mutable_element(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemmappedFileSystemDirectoryElement >*
      mutable_element();
  private:
  const ::tensorflow::MemmappedFileSystemDirectoryElement& _internal_element(int index) const;
  ::tensorflow::MemmappedFileSystemDirectoryElement* _internal_add_element();
  public:
  const ::tensorflow::MemmappedFileSystemDirectoryElement& element(int index) const;
  ::tensorflow::MemmappedFileSystemDirectoryElement* add_element();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemmappedFileSystemDirectoryElement >&
      element() const;

  // @@protoc_insertion_point(class_scope:tensorflow.MemmappedFileSystemDirectory)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemmappedFileSystemDirectoryElement > element_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fmemmapped_5ffile_5fsystem_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MemmappedFileSystemDirectoryElement

// uint64 offset = 1;
inline void MemmappedFileSystemDirectoryElement::clear_offset() {
  _impl_.offset_ = uint64_t{0u};
}
inline uint64_t MemmappedFileSystemDirectoryElement::_internal_offset() const {
  return _impl_.offset_;
}
inline uint64_t MemmappedFileSystemDirectoryElement::offset() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemmappedFileSystemDirectoryElement.offset)
  return _internal_offset();
}
inline void MemmappedFileSystemDirectoryElement::_internal_set_offset(uint64_t value) {
  
  _impl_.offset_ = value;
}
inline void MemmappedFileSystemDirectoryElement::set_offset(uint64_t value) {
  _internal_set_offset(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemmappedFileSystemDirectoryElement.offset)
}

// string name = 2;
inline void MemmappedFileSystemDirectoryElement::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& MemmappedFileSystemDirectoryElement::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemmappedFileSystemDirectoryElement.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemmappedFileSystemDirectoryElement::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemmappedFileSystemDirectoryElement.name)
}
inline std::string* MemmappedFileSystemDirectoryElement::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemmappedFileSystemDirectoryElement.name)
  return _s;
}
inline const std::string& MemmappedFileSystemDirectoryElement::_internal_name() const {
  return _impl_.name_.Get();
}
inline void MemmappedFileSystemDirectoryElement::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* MemmappedFileSystemDirectoryElement::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* MemmappedFileSystemDirectoryElement::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemmappedFileSystemDirectoryElement.name)
  return _impl_.name_.Release();
}
inline void MemmappedFileSystemDirectoryElement::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemmappedFileSystemDirectoryElement.name)
}

// uint64 length = 3;
inline void MemmappedFileSystemDirectoryElement::clear_length() {
  _impl_.length_ = uint64_t{0u};
}
inline uint64_t MemmappedFileSystemDirectoryElement::_internal_length() const {
  return _impl_.length_;
}
inline uint64_t MemmappedFileSystemDirectoryElement::length() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemmappedFileSystemDirectoryElement.length)
  return _internal_length();
}
inline void MemmappedFileSystemDirectoryElement::_internal_set_length(uint64_t value) {
  
  _impl_.length_ = value;
}
inline void MemmappedFileSystemDirectoryElement::set_length(uint64_t value) {
  _internal_set_length(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemmappedFileSystemDirectoryElement.length)
}

// -------------------------------------------------------------------

// MemmappedFileSystemDirectory

// repeated .tensorflow.MemmappedFileSystemDirectoryElement element = 1;
inline int MemmappedFileSystemDirectory::_internal_element_size() const {
  return _impl_.element_.size();
}
inline int MemmappedFileSystemDirectory::element_size() const {
  return _internal_element_size();
}
inline void MemmappedFileSystemDirectory::clear_element() {
  _impl_.element_.Clear();
}
inline ::tensorflow::MemmappedFileSystemDirectoryElement* MemmappedFileSystemDirectory::mutable_element(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MemmappedFileSystemDirectory.element)
  return _impl_.element_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemmappedFileSystemDirectoryElement >*
MemmappedFileSystemDirectory::mutable_element() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemmappedFileSystemDirectory.element)
  return &_impl_.element_;
}
inline const ::tensorflow::MemmappedFileSystemDirectoryElement& MemmappedFileSystemDirectory::_internal_element(int index) const {
  return _impl_.element_.Get(index);
}
inline const ::tensorflow::MemmappedFileSystemDirectoryElement& MemmappedFileSystemDirectory::element(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemmappedFileSystemDirectory.element)
  return _internal_element(index);
}
inline ::tensorflow::MemmappedFileSystemDirectoryElement* MemmappedFileSystemDirectory::_internal_add_element() {
  return _impl_.element_.Add();
}
inline ::tensorflow::MemmappedFileSystemDirectoryElement* MemmappedFileSystemDirectory::add_element() {
  ::tensorflow::MemmappedFileSystemDirectoryElement* _add = _internal_add_element();
  // @@protoc_insertion_point(field_add:tensorflow.MemmappedFileSystemDirectory.element)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemmappedFileSystemDirectoryElement >&
MemmappedFileSystemDirectory::element() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemmappedFileSystemDirectory.element)
  return _impl_.element_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fmemmapped_5ffile_5fsystem_2eproto
