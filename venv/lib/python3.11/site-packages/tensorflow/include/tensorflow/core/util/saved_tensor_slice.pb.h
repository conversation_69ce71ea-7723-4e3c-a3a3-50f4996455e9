// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/saved_tensor_slice.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/tensor_slice.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/framework/versions.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto;
namespace tensorflow {
class SavedSlice;
struct SavedSliceDefaultTypeInternal;
extern SavedSliceDefaultTypeInternal _SavedSlice_default_instance_;
class SavedSliceMeta;
struct SavedSliceMetaDefaultTypeInternal;
extern SavedSliceMetaDefaultTypeInternal _SavedSliceMeta_default_instance_;
class SavedTensorSliceMeta;
struct SavedTensorSliceMetaDefaultTypeInternal;
extern SavedTensorSliceMetaDefaultTypeInternal _SavedTensorSliceMeta_default_instance_;
class SavedTensorSlices;
struct SavedTensorSlicesDefaultTypeInternal;
extern SavedTensorSlicesDefaultTypeInternal _SavedTensorSlices_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::SavedSlice* Arena::CreateMaybeMessage<::tensorflow::SavedSlice>(Arena*);
template<> ::tensorflow::SavedSliceMeta* Arena::CreateMaybeMessage<::tensorflow::SavedSliceMeta>(Arena*);
template<> ::tensorflow::SavedTensorSliceMeta* Arena::CreateMaybeMessage<::tensorflow::SavedTensorSliceMeta>(Arena*);
template<> ::tensorflow::SavedTensorSlices* Arena::CreateMaybeMessage<::tensorflow::SavedTensorSlices>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class SavedSliceMeta final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedSliceMeta) */ {
 public:
  inline SavedSliceMeta() : SavedSliceMeta(nullptr) {}
  ~SavedSliceMeta() override;
  explicit PROTOBUF_CONSTEXPR SavedSliceMeta(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedSliceMeta(const SavedSliceMeta& from);
  SavedSliceMeta(SavedSliceMeta&& from) noexcept
    : SavedSliceMeta() {
    *this = ::std::move(from);
  }

  inline SavedSliceMeta& operator=(const SavedSliceMeta& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedSliceMeta& operator=(SavedSliceMeta&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedSliceMeta& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedSliceMeta* internal_default_instance() {
    return reinterpret_cast<const SavedSliceMeta*>(
               &_SavedSliceMeta_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SavedSliceMeta& a, SavedSliceMeta& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedSliceMeta* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedSliceMeta* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedSliceMeta* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedSliceMeta>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedSliceMeta& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedSliceMeta& from) {
    SavedSliceMeta::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedSliceMeta* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedSliceMeta";
  }
  protected:
  explicit SavedSliceMeta(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSliceFieldNumber = 4,
    kNameFieldNumber = 1,
    kShapeFieldNumber = 2,
    kTypeFieldNumber = 3,
  };
  // repeated .tensorflow.TensorSliceProto slice = 4;
  int slice_size() const;
  private:
  int _internal_slice_size() const;
  public:
  void clear_slice();
  ::tensorflow::TensorSliceProto* mutable_slice(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto >*
      mutable_slice();
  private:
  const ::tensorflow::TensorSliceProto& _internal_slice(int index) const;
  ::tensorflow::TensorSliceProto* _internal_add_slice();
  public:
  const ::tensorflow::TensorSliceProto& slice(int index) const;
  ::tensorflow::TensorSliceProto* add_slice();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto >&
      slice() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 3;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_type() const;
  void _internal_set_type(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SavedSliceMeta)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto > slice_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::TensorShapeProto* shape_;
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto;
};
// -------------------------------------------------------------------

class SavedTensorSliceMeta final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedTensorSliceMeta) */ {
 public:
  inline SavedTensorSliceMeta() : SavedTensorSliceMeta(nullptr) {}
  ~SavedTensorSliceMeta() override;
  explicit PROTOBUF_CONSTEXPR SavedTensorSliceMeta(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedTensorSliceMeta(const SavedTensorSliceMeta& from);
  SavedTensorSliceMeta(SavedTensorSliceMeta&& from) noexcept
    : SavedTensorSliceMeta() {
    *this = ::std::move(from);
  }

  inline SavedTensorSliceMeta& operator=(const SavedTensorSliceMeta& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedTensorSliceMeta& operator=(SavedTensorSliceMeta&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedTensorSliceMeta& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedTensorSliceMeta* internal_default_instance() {
    return reinterpret_cast<const SavedTensorSliceMeta*>(
               &_SavedTensorSliceMeta_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SavedTensorSliceMeta& a, SavedTensorSliceMeta& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedTensorSliceMeta* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedTensorSliceMeta* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedTensorSliceMeta* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedTensorSliceMeta>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedTensorSliceMeta& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedTensorSliceMeta& from) {
    SavedTensorSliceMeta::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedTensorSliceMeta* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedTensorSliceMeta";
  }
  protected:
  explicit SavedTensorSliceMeta(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorFieldNumber = 1,
    kVersionsFieldNumber = 2,
  };
  // repeated .tensorflow.SavedSliceMeta tensor = 1;
  int tensor_size() const;
  private:
  int _internal_tensor_size() const;
  public:
  void clear_tensor();
  ::tensorflow::SavedSliceMeta* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedSliceMeta >*
      mutable_tensor();
  private:
  const ::tensorflow::SavedSliceMeta& _internal_tensor(int index) const;
  ::tensorflow::SavedSliceMeta* _internal_add_tensor();
  public:
  const ::tensorflow::SavedSliceMeta& tensor(int index) const;
  ::tensorflow::SavedSliceMeta* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedSliceMeta >&
      tensor() const;

  // .tensorflow.VersionDef versions = 2;
  bool has_versions() const;
  private:
  bool _internal_has_versions() const;
  public:
  void clear_versions();
  const ::tensorflow::VersionDef& versions() const;
  PROTOBUF_NODISCARD ::tensorflow::VersionDef* release_versions();
  ::tensorflow::VersionDef* mutable_versions();
  void set_allocated_versions(::tensorflow::VersionDef* versions);
  private:
  const ::tensorflow::VersionDef& _internal_versions() const;
  ::tensorflow::VersionDef* _internal_mutable_versions();
  public:
  void unsafe_arena_set_allocated_versions(
      ::tensorflow::VersionDef* versions);
  ::tensorflow::VersionDef* unsafe_arena_release_versions();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedTensorSliceMeta)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedSliceMeta > tensor_;
    ::tensorflow::VersionDef* versions_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto;
};
// -------------------------------------------------------------------

class SavedSlice final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedSlice) */ {
 public:
  inline SavedSlice() : SavedSlice(nullptr) {}
  ~SavedSlice() override;
  explicit PROTOBUF_CONSTEXPR SavedSlice(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedSlice(const SavedSlice& from);
  SavedSlice(SavedSlice&& from) noexcept
    : SavedSlice() {
    *this = ::std::move(from);
  }

  inline SavedSlice& operator=(const SavedSlice& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedSlice& operator=(SavedSlice&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedSlice& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedSlice* internal_default_instance() {
    return reinterpret_cast<const SavedSlice*>(
               &_SavedSlice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SavedSlice& a, SavedSlice& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedSlice* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedSlice* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedSlice* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedSlice>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedSlice& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedSlice& from) {
    SavedSlice::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedSlice* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedSlice";
  }
  protected:
  explicit SavedSlice(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kSliceFieldNumber = 2,
    kDataFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.TensorSliceProto slice = 2;
  bool has_slice() const;
  private:
  bool _internal_has_slice() const;
  public:
  void clear_slice();
  const ::tensorflow::TensorSliceProto& slice() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorSliceProto* release_slice();
  ::tensorflow::TensorSliceProto* mutable_slice();
  void set_allocated_slice(::tensorflow::TensorSliceProto* slice);
  private:
  const ::tensorflow::TensorSliceProto& _internal_slice() const;
  ::tensorflow::TensorSliceProto* _internal_mutable_slice();
  public:
  void unsafe_arena_set_allocated_slice(
      ::tensorflow::TensorSliceProto* slice);
  ::tensorflow::TensorSliceProto* unsafe_arena_release_slice();

  // .tensorflow.TensorProto data = 3;
  bool has_data() const;
  private:
  bool _internal_has_data() const;
  public:
  void clear_data();
  const ::tensorflow::TensorProto& data() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_data();
  ::tensorflow::TensorProto* mutable_data();
  void set_allocated_data(::tensorflow::TensorProto* data);
  private:
  const ::tensorflow::TensorProto& _internal_data() const;
  ::tensorflow::TensorProto* _internal_mutable_data();
  public:
  void unsafe_arena_set_allocated_data(
      ::tensorflow::TensorProto* data);
  ::tensorflow::TensorProto* unsafe_arena_release_data();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedSlice)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::TensorSliceProto* slice_;
    ::tensorflow::TensorProto* data_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto;
};
// -------------------------------------------------------------------

class SavedTensorSlices final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedTensorSlices) */ {
 public:
  inline SavedTensorSlices() : SavedTensorSlices(nullptr) {}
  ~SavedTensorSlices() override;
  explicit PROTOBUF_CONSTEXPR SavedTensorSlices(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedTensorSlices(const SavedTensorSlices& from);
  SavedTensorSlices(SavedTensorSlices&& from) noexcept
    : SavedTensorSlices() {
    *this = ::std::move(from);
  }

  inline SavedTensorSlices& operator=(const SavedTensorSlices& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedTensorSlices& operator=(SavedTensorSlices&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedTensorSlices& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedTensorSlices* internal_default_instance() {
    return reinterpret_cast<const SavedTensorSlices*>(
               &_SavedTensorSlices_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SavedTensorSlices& a, SavedTensorSlices& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedTensorSlices* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedTensorSlices* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedTensorSlices* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedTensorSlices>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedTensorSlices& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedTensorSlices& from) {
    SavedTensorSlices::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedTensorSlices* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedTensorSlices";
  }
  protected:
  explicit SavedTensorSlices(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMetaFieldNumber = 1,
    kDataFieldNumber = 2,
  };
  // .tensorflow.SavedTensorSliceMeta meta = 1;
  bool has_meta() const;
  private:
  bool _internal_has_meta() const;
  public:
  void clear_meta();
  const ::tensorflow::SavedTensorSliceMeta& meta() const;
  PROTOBUF_NODISCARD ::tensorflow::SavedTensorSliceMeta* release_meta();
  ::tensorflow::SavedTensorSliceMeta* mutable_meta();
  void set_allocated_meta(::tensorflow::SavedTensorSliceMeta* meta);
  private:
  const ::tensorflow::SavedTensorSliceMeta& _internal_meta() const;
  ::tensorflow::SavedTensorSliceMeta* _internal_mutable_meta();
  public:
  void unsafe_arena_set_allocated_meta(
      ::tensorflow::SavedTensorSliceMeta* meta);
  ::tensorflow::SavedTensorSliceMeta* unsafe_arena_release_meta();

  // .tensorflow.SavedSlice data = 2;
  bool has_data() const;
  private:
  bool _internal_has_data() const;
  public:
  void clear_data();
  const ::tensorflow::SavedSlice& data() const;
  PROTOBUF_NODISCARD ::tensorflow::SavedSlice* release_data();
  ::tensorflow::SavedSlice* mutable_data();
  void set_allocated_data(::tensorflow::SavedSlice* data);
  private:
  const ::tensorflow::SavedSlice& _internal_data() const;
  ::tensorflow::SavedSlice* _internal_mutable_data();
  public:
  void unsafe_arena_set_allocated_data(
      ::tensorflow::SavedSlice* data);
  ::tensorflow::SavedSlice* unsafe_arena_release_data();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedTensorSlices)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::SavedTensorSliceMeta* meta_;
    ::tensorflow::SavedSlice* data_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SavedSliceMeta

// string name = 1;
inline void SavedSliceMeta::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& SavedSliceMeta::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedSliceMeta::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedSliceMeta.name)
}
inline std::string* SavedSliceMeta::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSliceMeta.name)
  return _s;
}
inline const std::string& SavedSliceMeta::_internal_name() const {
  return _impl_.name_.Get();
}
inline void SavedSliceMeta::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedSliceMeta::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedSliceMeta::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSliceMeta.name)
  return _impl_.name_.Release();
}
inline void SavedSliceMeta::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSliceMeta.name)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool SavedSliceMeta::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool SavedSliceMeta::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& SavedSliceMeta::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& SavedSliceMeta::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.shape)
  return _internal_shape();
}
inline void SavedSliceMeta::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedSliceMeta.shape)
}
inline ::tensorflow::TensorShapeProto* SavedSliceMeta::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedSliceMeta::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSliceMeta.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedSliceMeta::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* SavedSliceMeta::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSliceMeta.shape)
  return _msg;
}
inline void SavedSliceMeta::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSliceMeta.shape)
}

// .tensorflow.DataType type = 3;
inline void SavedSliceMeta::clear_type() {
  _impl_.type_ = 0;
}
inline ::tensorflow::DataType SavedSliceMeta::_internal_type() const {
  return static_cast< ::tensorflow::DataType >(_impl_.type_);
}
inline ::tensorflow::DataType SavedSliceMeta::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.type)
  return _internal_type();
}
inline void SavedSliceMeta::_internal_set_type(::tensorflow::DataType value) {
  
  _impl_.type_ = value;
}
inline void SavedSliceMeta::set_type(::tensorflow::DataType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedSliceMeta.type)
}

// repeated .tensorflow.TensorSliceProto slice = 4;
inline int SavedSliceMeta::_internal_slice_size() const {
  return _impl_.slice_.size();
}
inline int SavedSliceMeta::slice_size() const {
  return _internal_slice_size();
}
inline ::tensorflow::TensorSliceProto* SavedSliceMeta::mutable_slice(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSliceMeta.slice)
  return _impl_.slice_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto >*
SavedSliceMeta::mutable_slice() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedSliceMeta.slice)
  return &_impl_.slice_;
}
inline const ::tensorflow::TensorSliceProto& SavedSliceMeta::_internal_slice(int index) const {
  return _impl_.slice_.Get(index);
}
inline const ::tensorflow::TensorSliceProto& SavedSliceMeta::slice(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSliceMeta.slice)
  return _internal_slice(index);
}
inline ::tensorflow::TensorSliceProto* SavedSliceMeta::_internal_add_slice() {
  return _impl_.slice_.Add();
}
inline ::tensorflow::TensorSliceProto* SavedSliceMeta::add_slice() {
  ::tensorflow::TensorSliceProto* _add = _internal_add_slice();
  // @@protoc_insertion_point(field_add:tensorflow.SavedSliceMeta.slice)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorSliceProto >&
SavedSliceMeta::slice() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedSliceMeta.slice)
  return _impl_.slice_;
}

// -------------------------------------------------------------------

// SavedTensorSliceMeta

// repeated .tensorflow.SavedSliceMeta tensor = 1;
inline int SavedTensorSliceMeta::_internal_tensor_size() const {
  return _impl_.tensor_.size();
}
inline int SavedTensorSliceMeta::tensor_size() const {
  return _internal_tensor_size();
}
inline void SavedTensorSliceMeta::clear_tensor() {
  _impl_.tensor_.Clear();
}
inline ::tensorflow::SavedSliceMeta* SavedTensorSliceMeta::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSliceMeta.tensor)
  return _impl_.tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedSliceMeta >*
SavedTensorSliceMeta::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedTensorSliceMeta.tensor)
  return &_impl_.tensor_;
}
inline const ::tensorflow::SavedSliceMeta& SavedTensorSliceMeta::_internal_tensor(int index) const {
  return _impl_.tensor_.Get(index);
}
inline const ::tensorflow::SavedSliceMeta& SavedTensorSliceMeta::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSliceMeta.tensor)
  return _internal_tensor(index);
}
inline ::tensorflow::SavedSliceMeta* SavedTensorSliceMeta::_internal_add_tensor() {
  return _impl_.tensor_.Add();
}
inline ::tensorflow::SavedSliceMeta* SavedTensorSliceMeta::add_tensor() {
  ::tensorflow::SavedSliceMeta* _add = _internal_add_tensor();
  // @@protoc_insertion_point(field_add:tensorflow.SavedTensorSliceMeta.tensor)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedSliceMeta >&
SavedTensorSliceMeta::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedTensorSliceMeta.tensor)
  return _impl_.tensor_;
}

// .tensorflow.VersionDef versions = 2;
inline bool SavedTensorSliceMeta::_internal_has_versions() const {
  return this != internal_default_instance() && _impl_.versions_ != nullptr;
}
inline bool SavedTensorSliceMeta::has_versions() const {
  return _internal_has_versions();
}
inline const ::tensorflow::VersionDef& SavedTensorSliceMeta::_internal_versions() const {
  const ::tensorflow::VersionDef* p = _impl_.versions_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::VersionDef&>(
      ::tensorflow::_VersionDef_default_instance_);
}
inline const ::tensorflow::VersionDef& SavedTensorSliceMeta::versions() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSliceMeta.versions)
  return _internal_versions();
}
inline void SavedTensorSliceMeta::unsafe_arena_set_allocated_versions(
    ::tensorflow::VersionDef* versions) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.versions_);
  }
  _impl_.versions_ = versions;
  if (versions) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedTensorSliceMeta.versions)
}
inline ::tensorflow::VersionDef* SavedTensorSliceMeta::release_versions() {
  
  ::tensorflow::VersionDef* temp = _impl_.versions_;
  _impl_.versions_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::VersionDef* SavedTensorSliceMeta::unsafe_arena_release_versions() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedTensorSliceMeta.versions)
  
  ::tensorflow::VersionDef* temp = _impl_.versions_;
  _impl_.versions_ = nullptr;
  return temp;
}
inline ::tensorflow::VersionDef* SavedTensorSliceMeta::_internal_mutable_versions() {
  
  if (_impl_.versions_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VersionDef>(GetArenaForAllocation());
    _impl_.versions_ = p;
  }
  return _impl_.versions_;
}
inline ::tensorflow::VersionDef* SavedTensorSliceMeta::mutable_versions() {
  ::tensorflow::VersionDef* _msg = _internal_mutable_versions();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSliceMeta.versions)
  return _msg;
}
inline void SavedTensorSliceMeta::set_allocated_versions(::tensorflow::VersionDef* versions) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.versions_);
  }
  if (versions) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(versions));
    if (message_arena != submessage_arena) {
      versions = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, versions, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.versions_ = versions;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedTensorSliceMeta.versions)
}

// -------------------------------------------------------------------

// SavedSlice

// string name = 1;
inline void SavedSlice::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& SavedSlice::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSlice.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedSlice::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedSlice.name)
}
inline std::string* SavedSlice::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSlice.name)
  return _s;
}
inline const std::string& SavedSlice::_internal_name() const {
  return _impl_.name_.Get();
}
inline void SavedSlice::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedSlice::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedSlice::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSlice.name)
  return _impl_.name_.Release();
}
inline void SavedSlice::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSlice.name)
}

// .tensorflow.TensorSliceProto slice = 2;
inline bool SavedSlice::_internal_has_slice() const {
  return this != internal_default_instance() && _impl_.slice_ != nullptr;
}
inline bool SavedSlice::has_slice() const {
  return _internal_has_slice();
}
inline const ::tensorflow::TensorSliceProto& SavedSlice::_internal_slice() const {
  const ::tensorflow::TensorSliceProto* p = _impl_.slice_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorSliceProto&>(
      ::tensorflow::_TensorSliceProto_default_instance_);
}
inline const ::tensorflow::TensorSliceProto& SavedSlice::slice() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSlice.slice)
  return _internal_slice();
}
inline void SavedSlice::unsafe_arena_set_allocated_slice(
    ::tensorflow::TensorSliceProto* slice) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.slice_);
  }
  _impl_.slice_ = slice;
  if (slice) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedSlice.slice)
}
inline ::tensorflow::TensorSliceProto* SavedSlice::release_slice() {
  
  ::tensorflow::TensorSliceProto* temp = _impl_.slice_;
  _impl_.slice_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorSliceProto* SavedSlice::unsafe_arena_release_slice() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSlice.slice)
  
  ::tensorflow::TensorSliceProto* temp = _impl_.slice_;
  _impl_.slice_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorSliceProto* SavedSlice::_internal_mutable_slice() {
  
  if (_impl_.slice_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorSliceProto>(GetArenaForAllocation());
    _impl_.slice_ = p;
  }
  return _impl_.slice_;
}
inline ::tensorflow::TensorSliceProto* SavedSlice::mutable_slice() {
  ::tensorflow::TensorSliceProto* _msg = _internal_mutable_slice();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSlice.slice)
  return _msg;
}
inline void SavedSlice::set_allocated_slice(::tensorflow::TensorSliceProto* slice) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.slice_);
  }
  if (slice) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(slice));
    if (message_arena != submessage_arena) {
      slice = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, slice, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.slice_ = slice;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSlice.slice)
}

// .tensorflow.TensorProto data = 3;
inline bool SavedSlice::_internal_has_data() const {
  return this != internal_default_instance() && _impl_.data_ != nullptr;
}
inline bool SavedSlice::has_data() const {
  return _internal_has_data();
}
inline const ::tensorflow::TensorProto& SavedSlice::_internal_data() const {
  const ::tensorflow::TensorProto* p = _impl_.data_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorProto&>(
      ::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& SavedSlice::data() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedSlice.data)
  return _internal_data();
}
inline void SavedSlice::unsafe_arena_set_allocated_data(
    ::tensorflow::TensorProto* data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.data_);
  }
  _impl_.data_ = data;
  if (data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedSlice.data)
}
inline ::tensorflow::TensorProto* SavedSlice::release_data() {
  
  ::tensorflow::TensorProto* temp = _impl_.data_;
  _impl_.data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorProto* SavedSlice::unsafe_arena_release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedSlice.data)
  
  ::tensorflow::TensorProto* temp = _impl_.data_;
  _impl_.data_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* SavedSlice::_internal_mutable_data() {
  
  if (_impl_.data_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaForAllocation());
    _impl_.data_ = p;
  }
  return _impl_.data_;
}
inline ::tensorflow::TensorProto* SavedSlice::mutable_data() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedSlice.data)
  return _msg;
}
inline void SavedSlice::set_allocated_data(::tensorflow::TensorProto* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.data_);
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data));
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.data_ = data;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedSlice.data)
}

// -------------------------------------------------------------------

// SavedTensorSlices

// .tensorflow.SavedTensorSliceMeta meta = 1;
inline bool SavedTensorSlices::_internal_has_meta() const {
  return this != internal_default_instance() && _impl_.meta_ != nullptr;
}
inline bool SavedTensorSlices::has_meta() const {
  return _internal_has_meta();
}
inline void SavedTensorSlices::clear_meta() {
  if (GetArenaForAllocation() == nullptr && _impl_.meta_ != nullptr) {
    delete _impl_.meta_;
  }
  _impl_.meta_ = nullptr;
}
inline const ::tensorflow::SavedTensorSliceMeta& SavedTensorSlices::_internal_meta() const {
  const ::tensorflow::SavedTensorSliceMeta* p = _impl_.meta_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SavedTensorSliceMeta&>(
      ::tensorflow::_SavedTensorSliceMeta_default_instance_);
}
inline const ::tensorflow::SavedTensorSliceMeta& SavedTensorSlices::meta() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSlices.meta)
  return _internal_meta();
}
inline void SavedTensorSlices::unsafe_arena_set_allocated_meta(
    ::tensorflow::SavedTensorSliceMeta* meta) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.meta_);
  }
  _impl_.meta_ = meta;
  if (meta) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedTensorSlices.meta)
}
inline ::tensorflow::SavedTensorSliceMeta* SavedTensorSlices::release_meta() {
  
  ::tensorflow::SavedTensorSliceMeta* temp = _impl_.meta_;
  _impl_.meta_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SavedTensorSliceMeta* SavedTensorSlices::unsafe_arena_release_meta() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedTensorSlices.meta)
  
  ::tensorflow::SavedTensorSliceMeta* temp = _impl_.meta_;
  _impl_.meta_ = nullptr;
  return temp;
}
inline ::tensorflow::SavedTensorSliceMeta* SavedTensorSlices::_internal_mutable_meta() {
  
  if (_impl_.meta_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SavedTensorSliceMeta>(GetArenaForAllocation());
    _impl_.meta_ = p;
  }
  return _impl_.meta_;
}
inline ::tensorflow::SavedTensorSliceMeta* SavedTensorSlices::mutable_meta() {
  ::tensorflow::SavedTensorSliceMeta* _msg = _internal_mutable_meta();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSlices.meta)
  return _msg;
}
inline void SavedTensorSlices::set_allocated_meta(::tensorflow::SavedTensorSliceMeta* meta) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.meta_;
  }
  if (meta) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(meta);
    if (message_arena != submessage_arena) {
      meta = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, meta, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.meta_ = meta;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedTensorSlices.meta)
}

// .tensorflow.SavedSlice data = 2;
inline bool SavedTensorSlices::_internal_has_data() const {
  return this != internal_default_instance() && _impl_.data_ != nullptr;
}
inline bool SavedTensorSlices::has_data() const {
  return _internal_has_data();
}
inline void SavedTensorSlices::clear_data() {
  if (GetArenaForAllocation() == nullptr && _impl_.data_ != nullptr) {
    delete _impl_.data_;
  }
  _impl_.data_ = nullptr;
}
inline const ::tensorflow::SavedSlice& SavedTensorSlices::_internal_data() const {
  const ::tensorflow::SavedSlice* p = _impl_.data_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SavedSlice&>(
      ::tensorflow::_SavedSlice_default_instance_);
}
inline const ::tensorflow::SavedSlice& SavedTensorSlices::data() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedTensorSlices.data)
  return _internal_data();
}
inline void SavedTensorSlices::unsafe_arena_set_allocated_data(
    ::tensorflow::SavedSlice* data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.data_);
  }
  _impl_.data_ = data;
  if (data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedTensorSlices.data)
}
inline ::tensorflow::SavedSlice* SavedTensorSlices::release_data() {
  
  ::tensorflow::SavedSlice* temp = _impl_.data_;
  _impl_.data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SavedSlice* SavedTensorSlices::unsafe_arena_release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedTensorSlices.data)
  
  ::tensorflow::SavedSlice* temp = _impl_.data_;
  _impl_.data_ = nullptr;
  return temp;
}
inline ::tensorflow::SavedSlice* SavedTensorSlices::_internal_mutable_data() {
  
  if (_impl_.data_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SavedSlice>(GetArenaForAllocation());
    _impl_.data_ = p;
  }
  return _impl_.data_;
}
inline ::tensorflow::SavedSlice* SavedTensorSlices::mutable_data() {
  ::tensorflow::SavedSlice* _msg = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedTensorSlices.data)
  return _msg;
}
inline void SavedTensorSlices::set_allocated_data(::tensorflow::SavedSlice* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.data_;
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(data);
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.data_ = data;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedTensorSlices.data)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fsaved_5ftensor_5fslice_2eproto
