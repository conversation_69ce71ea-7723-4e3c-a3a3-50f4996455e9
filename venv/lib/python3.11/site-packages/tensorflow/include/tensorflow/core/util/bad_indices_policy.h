/* Copyright 2024 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_CORE_UTIL_BAD_INDICES_POLICY_H_
#define TENSORFLOW_CORE_UTIL_BAD_INDICES_POLICY_H_

#include "absl/status/statusor.h"
#include "absl/strings/string_view.h"

namespace tensorflow {
enum class BadIndicesPolicy {
  // Default behavior: return an error on CPU and ignore on GPU. This is because
  // we handle bad indices differently on CPU and GPU before this policy is
  // introduced.
  kDefault,
  // Return an error.
  kError,
  // Ignore bad indices.
  kIgnore,
};

absl::StatusOr<BadIndicesPolicy> BadIndicesPolicyFromString(
    absl::string_view str);

}  // namespace tensorflow

#endif  // TENSORFLOW_CORE_UTIL_BAD_INDICES_POLICY_H_
