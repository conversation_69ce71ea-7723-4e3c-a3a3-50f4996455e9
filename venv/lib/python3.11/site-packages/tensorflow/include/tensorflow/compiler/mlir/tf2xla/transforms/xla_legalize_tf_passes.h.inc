/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_INFEEDOPSXLAADJUSTLAYOUT
#define GEN_PASS_DECL_LEGALIZETF
#define GEN_PASS_DECL_LEGALIZETFCOLLECTIVE
#define GEN_PASS_DECL_TFXLADEVICESPECIFICTRANSFORMS
#define GEN_PASS_DECL_VERIFYTFXLALEGALIZATION
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// InfeedOpsXlaAdjustLayout
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_INFEEDOPSXLAADJUSTLAYOUT
#undef GEN_PASS_DECL_INFEEDOPSXLAADJUSTLAYOUT
#endif // GEN_PASS_DECL_INFEEDOPSXLAADJUSTLAYOUT
#ifdef GEN_PASS_DEF_INFEEDOPSXLAADJUSTLAYOUT
namespace impl {

template <typename DerivedT>
class InfeedOpsXlaAdjustLayoutBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = InfeedOpsXlaAdjustLayoutBase;

  InfeedOpsXlaAdjustLayoutBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  InfeedOpsXlaAdjustLayoutBase(const InfeedOpsXlaAdjustLayoutBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  InfeedOpsXlaAdjustLayoutBase& operator=(const InfeedOpsXlaAdjustLayoutBase &) = delete;
  InfeedOpsXlaAdjustLayoutBase(InfeedOpsXlaAdjustLayoutBase &&) = delete;
  InfeedOpsXlaAdjustLayoutBase& operator=(InfeedOpsXlaAdjustLayoutBase &&) = delete;
  ~InfeedOpsXlaAdjustLayoutBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("infeed-ops-xla-adjust-layout");
  }
  ::llvm::StringRef getArgument() const override { return "infeed-ops-xla-adjust-layout"; }

  ::llvm::StringRef getDescription() const override { return "Adjusts Infeed ops layout for XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InfeedOpsXlaAdjustLayout");
  }
  ::llvm::StringRef getName() const override { return "InfeedOpsXlaAdjustLayout"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InfeedOpsXlaAdjustLayoutBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_INFEEDOPSXLAADJUSTLAYOUT
#endif // GEN_PASS_DEF_INFEEDOPSXLAADJUSTLAYOUT

//===----------------------------------------------------------------------===//
// LegalizeTF
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZETF
struct LegalizeTFOptions {
  bool legalize_chlo_ = true;
  bool use_tf2xla_fallback_ = false;
  std::string device_type_ = "INVALID_DEVICE_TYPE";
  bool prefer_tf2xla_ = false;
};
#undef GEN_PASS_DECL_LEGALIZETF
#endif // GEN_PASS_DECL_LEGALIZETF
#ifdef GEN_PASS_DEF_LEGALIZETF
namespace impl {

template <typename DerivedT>
class LegalizeTFBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFBase;

  LegalizeTFBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFBase(const LegalizeTFBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LegalizeTFBase& operator=(const LegalizeTFBase &) = delete;
  LegalizeTFBase(LegalizeTFBase &&) = delete;
  LegalizeTFBase& operator=(LegalizeTFBase &&) = delete;
  ~LegalizeTFBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from TF dialect's or HLO dialect's control flow."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTF");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<chlo::ChloDialect>();
    registry.insert<func::FuncDialect>();
    registry.insert<mhlo::MhloDialect>();
    registry.insert<quant::QuantDialect>();
    registry.insert<shape::ShapeDialect>();
    registry.insert<sparse_tensor::SparseTensorDialect>();
    registry.insert<stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFBase<DerivedT>)

  LegalizeTFBase(LegalizeTFOptions options) : LegalizeTFBase() {
    legalize_chlo_ = std::move(options.legalize_chlo_);
    use_tf2xla_fallback_ = std::move(options.use_tf2xla_fallback_);
    device_type_ = std::move(options.device_type_);
    prefer_tf2xla_ = std::move(options.prefer_tf2xla_);
  }
protected:
  ::mlir::Pass::Option<bool> legalize_chlo_{*this, "legalize-chlo", ::llvm::cl::desc("Legalizes intermediate chlo ops to hlo"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> use_tf2xla_fallback_{*this, "use-tf2xla-fallback", ::llvm::cl::desc("Use TF2XLA fallback for legalization"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> device_type_{*this, "device-type", ::llvm::cl::desc("The device type used by TF2XLA fallback. Must be specified if use-tf2xla-fallback is true, otherwise not used"), ::llvm::cl::init("INVALID_DEVICE_TYPE")};
  ::mlir::Pass::Option<bool> prefer_tf2xla_{*this, "prefer-tf2xla", ::llvm::cl::desc("Prioritize tf2xla fallback legalization over MLIR legalization patterns"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZETF
#endif // GEN_PASS_DEF_LEGALIZETF

//===----------------------------------------------------------------------===//
// LegalizeTFCollective
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZETFCOLLECTIVE
#undef GEN_PASS_DECL_LEGALIZETFCOLLECTIVE
#endif // GEN_PASS_DECL_LEGALIZETFCOLLECTIVE
#ifdef GEN_PASS_DEF_LEGALIZETFCOLLECTIVE
namespace impl {

template <typename DerivedT>
class LegalizeTFCollectiveBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFCollectiveBase;

  LegalizeTFCollectiveBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFCollectiveBase(const LegalizeTFCollectiveBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LegalizeTFCollectiveBase& operator=(const LegalizeTFCollectiveBase &) = delete;
  LegalizeTFCollectiveBase(LegalizeTFCollectiveBase &&) = delete;
  LegalizeTFCollectiveBase& operator=(LegalizeTFCollectiveBase &&) = delete;
  ~LegalizeTFCollectiveBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-collective");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-collective"; }

  ::llvm::StringRef getDescription() const override { return "Legalize TF/XLA collective ops (TensorFlow dialect) to the HLO dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFCollective");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFCollective"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<sparse_tensor::SparseTensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFCollectiveBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZETFCOLLECTIVE
#endif // GEN_PASS_DEF_LEGALIZETFCOLLECTIVE

//===----------------------------------------------------------------------===//
// TFXLADeviceSpecificTransforms
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TFXLADEVICESPECIFICTRANSFORMS
struct TFXLADeviceSpecificTransformsOptions {
  std::string device_type_ = "INVALID_DEVICE_TYPE";
};
#undef GEN_PASS_DECL_TFXLADEVICESPECIFICTRANSFORMS
#endif // GEN_PASS_DECL_TFXLADEVICESPECIFICTRANSFORMS
#ifdef GEN_PASS_DEF_TFXLADEVICESPECIFICTRANSFORMS
namespace impl {

template <typename DerivedT>
class TFXLADeviceSpecificTransformsBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = TFXLADeviceSpecificTransformsBase;

  TFXLADeviceSpecificTransformsBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TFXLADeviceSpecificTransformsBase(const TFXLADeviceSpecificTransformsBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  TFXLADeviceSpecificTransformsBase& operator=(const TFXLADeviceSpecificTransformsBase &) = delete;
  TFXLADeviceSpecificTransformsBase(TFXLADeviceSpecificTransformsBase &&) = delete;
  TFXLADeviceSpecificTransformsBase& operator=(TFXLADeviceSpecificTransformsBase &&) = delete;
  ~TFXLADeviceSpecificTransformsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfxla-device-specific-transforms");
  }
  ::llvm::StringRef getArgument() const override { return "tfxla-device-specific-transforms"; }

  ::llvm::StringRef getDescription() const override { return "Transforms ops that require device context into device independent TF Ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TFXLADeviceSpecificTransforms");
  }
  ::llvm::StringRef getName() const override { return "TFXLADeviceSpecificTransforms"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TFXLADeviceSpecificTransformsBase<DerivedT>)

  TFXLADeviceSpecificTransformsBase(TFXLADeviceSpecificTransformsOptions options) : TFXLADeviceSpecificTransformsBase() {
    device_type_ = std::move(options.device_type_);
  }
protected:
  ::mlir::Pass::Option<std::string> device_type_{*this, "device-type", ::llvm::cl::desc("The device type being targeted."), ::llvm::cl::init("INVALID_DEVICE_TYPE")};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TFXLADEVICESPECIFICTRANSFORMS
#endif // GEN_PASS_DEF_TFXLADEVICESPECIFICTRANSFORMS

//===----------------------------------------------------------------------===//
// VerifyTFXLALegalization
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VERIFYTFXLALEGALIZATION
struct VerifyTFXLALegalizationOptions {
  bool legalize_chlo_ = true;
};
#undef GEN_PASS_DECL_VERIFYTFXLALEGALIZATION
#endif // GEN_PASS_DECL_VERIFYTFXLALEGALIZATION
#ifdef GEN_PASS_DEF_VERIFYTFXLALEGALIZATION
namespace impl {

template <typename DerivedT>
class VerifyTFXLALegalizationBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VerifyTFXLALegalizationBase;

  VerifyTFXLALegalizationBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifyTFXLALegalizationBase(const VerifyTFXLALegalizationBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  VerifyTFXLALegalizationBase& operator=(const VerifyTFXLALegalizationBase &) = delete;
  VerifyTFXLALegalizationBase(VerifyTFXLALegalizationBase &&) = delete;
  VerifyTFXLALegalizationBase& operator=(VerifyTFXLALegalizationBase &&) = delete;
  ~VerifyTFXLALegalizationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfxla-verify-legalization");
  }
  ::llvm::StringRef getArgument() const override { return "tfxla-verify-legalization"; }

  ::llvm::StringRef getDescription() const override { return "Verifies that all TF ops have been legalized to XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifyTFXLALegalization");
  }
  ::llvm::StringRef getName() const override { return "VerifyTFXLALegalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VerifyTFXLALegalizationBase<DerivedT>)

  VerifyTFXLALegalizationBase(VerifyTFXLALegalizationOptions options) : VerifyTFXLALegalizationBase() {
    legalize_chlo_ = std::move(options.legalize_chlo_);
  }
protected:
  ::mlir::Pass::Option<bool> legalize_chlo_{*this, "legalize-chlo", ::llvm::cl::desc("Legalizes intermediate chlo ops to hlo"), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_VERIFYTFXLALEGALIZATION
#endif // GEN_PASS_DEF_VERIFYTFXLALEGALIZATION
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// InfeedOpsXlaAdjustLayout Registration
//===----------------------------------------------------------------------===//

inline void registerInfeedOpsXlaAdjustLayout() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateInfeedsOpsXlaAdjustLayoutPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerInfeedOpsXlaAdjustLayoutPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateInfeedsOpsXlaAdjustLayoutPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTF Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTF() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createLegalizeTFPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeTFPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::createLegalizeTFPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTFCollective Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTFCollective() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateLegalizeTFCollectivePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeTFCollectivePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateLegalizeTFCollectivePass();
  });
}

//===----------------------------------------------------------------------===//
// TFXLADeviceSpecificTransforms Registration
//===----------------------------------------------------------------------===//

inline void registerTFXLADeviceSpecificTransforms() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateTFXLADeviceSpecificTransformsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTFXLADeviceSpecificTransformsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateTFXLADeviceSpecificTransformsPass();
  });
}

//===----------------------------------------------------------------------===//
// VerifyTFXLALegalization Registration
//===----------------------------------------------------------------------===//

inline void registerVerifyTFXLALegalization() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateVerifyTFXLALegalizationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerVerifyTFXLALegalizationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::mhlo::CreateVerifyTFXLALegalizationPass();
  });
}

//===----------------------------------------------------------------------===//
// LegalizeTf Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTfPasses() {
  registerInfeedOpsXlaAdjustLayout();
  registerLegalizeTF();
  registerLegalizeTFCollective();
  registerTFXLADeviceSpecificTransforms();
  registerVerifyTFXLALegalization();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class InfeedOpsXlaAdjustLayoutBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = InfeedOpsXlaAdjustLayoutBase;

  InfeedOpsXlaAdjustLayoutBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  InfeedOpsXlaAdjustLayoutBase(const InfeedOpsXlaAdjustLayoutBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  InfeedOpsXlaAdjustLayoutBase& operator=(const InfeedOpsXlaAdjustLayoutBase &) = delete;
  InfeedOpsXlaAdjustLayoutBase(InfeedOpsXlaAdjustLayoutBase &&) = delete;
  InfeedOpsXlaAdjustLayoutBase& operator=(InfeedOpsXlaAdjustLayoutBase &&) = delete;
  ~InfeedOpsXlaAdjustLayoutBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("infeed-ops-xla-adjust-layout");
  }
  ::llvm::StringRef getArgument() const override { return "infeed-ops-xla-adjust-layout"; }

  ::llvm::StringRef getDescription() const override { return "Adjusts Infeed ops layout for XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InfeedOpsXlaAdjustLayout");
  }
  ::llvm::StringRef getName() const override { return "InfeedOpsXlaAdjustLayout"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InfeedOpsXlaAdjustLayoutBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LegalizeTFBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFBase;

  LegalizeTFBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFBase(const LegalizeTFBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LegalizeTFBase& operator=(const LegalizeTFBase &) = delete;
  LegalizeTFBase(LegalizeTFBase &&) = delete;
  LegalizeTFBase& operator=(LegalizeTFBase &&) = delete;
  ~LegalizeTFBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf"; }

  ::llvm::StringRef getDescription() const override { return "Legalize from TF dialect's or HLO dialect's control flow."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTF");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTF"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<arith::ArithDialect>();
    registry.insert<chlo::ChloDialect>();
    registry.insert<func::FuncDialect>();
    registry.insert<mhlo::MhloDialect>();
    registry.insert<quant::QuantDialect>();
    registry.insert<shape::ShapeDialect>();
    registry.insert<sparse_tensor::SparseTensorDialect>();
    registry.insert<stablehlo::StablehloDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> legalize_chlo_{*this, "legalize-chlo", ::llvm::cl::desc("Legalizes intermediate chlo ops to hlo"), ::llvm::cl::init(true)};
  ::mlir::Pass::Option<bool> use_tf2xla_fallback_{*this, "use-tf2xla-fallback", ::llvm::cl::desc("Use TF2XLA fallback for legalization"), ::llvm::cl::init(false)};
  ::mlir::Pass::Option<std::string> device_type_{*this, "device-type", ::llvm::cl::desc("The device type used by TF2XLA fallback. Must be specified if use-tf2xla-fallback is true, otherwise not used"), ::llvm::cl::init("INVALID_DEVICE_TYPE")};
  ::mlir::Pass::Option<bool> prefer_tf2xla_{*this, "prefer-tf2xla", ::llvm::cl::desc("Prioritize tf2xla fallback legalization over MLIR legalization patterns"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class LegalizeTFCollectiveBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFCollectiveBase;

  LegalizeTFCollectiveBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFCollectiveBase(const LegalizeTFCollectiveBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LegalizeTFCollectiveBase& operator=(const LegalizeTFCollectiveBase &) = delete;
  LegalizeTFCollectiveBase(LegalizeTFCollectiveBase &&) = delete;
  LegalizeTFCollectiveBase& operator=(LegalizeTFCollectiveBase &&) = delete;
  ~LegalizeTFCollectiveBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-collective");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-collective"; }

  ::llvm::StringRef getDescription() const override { return "Legalize TF/XLA collective ops (TensorFlow dialect) to the HLO dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFCollective");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFCollective"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<sparse_tensor::SparseTensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFCollectiveBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TFXLADeviceSpecificTransformsBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = TFXLADeviceSpecificTransformsBase;

  TFXLADeviceSpecificTransformsBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  TFXLADeviceSpecificTransformsBase(const TFXLADeviceSpecificTransformsBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  TFXLADeviceSpecificTransformsBase& operator=(const TFXLADeviceSpecificTransformsBase &) = delete;
  TFXLADeviceSpecificTransformsBase(TFXLADeviceSpecificTransformsBase &&) = delete;
  TFXLADeviceSpecificTransformsBase& operator=(TFXLADeviceSpecificTransformsBase &&) = delete;
  ~TFXLADeviceSpecificTransformsBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfxla-device-specific-transforms");
  }
  ::llvm::StringRef getArgument() const override { return "tfxla-device-specific-transforms"; }

  ::llvm::StringRef getDescription() const override { return "Transforms ops that require device context into device independent TF Ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TFXLADeviceSpecificTransforms");
  }
  ::llvm::StringRef getName() const override { return "TFXLADeviceSpecificTransforms"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TFXLADeviceSpecificTransformsBase<DerivedT>)

protected:
  ::mlir::Pass::Option<std::string> device_type_{*this, "device-type", ::llvm::cl::desc("The device type being targeted."), ::llvm::cl::init("INVALID_DEVICE_TYPE")};
};

template <typename DerivedT>
class VerifyTFXLALegalizationBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VerifyTFXLALegalizationBase;

  VerifyTFXLALegalizationBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifyTFXLALegalizationBase(const VerifyTFXLALegalizationBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  VerifyTFXLALegalizationBase& operator=(const VerifyTFXLALegalizationBase &) = delete;
  VerifyTFXLALegalizationBase(VerifyTFXLALegalizationBase &&) = delete;
  VerifyTFXLALegalizationBase& operator=(VerifyTFXLALegalizationBase &&) = delete;
  ~VerifyTFXLALegalizationBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tfxla-verify-legalization");
  }
  ::llvm::StringRef getArgument() const override { return "tfxla-verify-legalization"; }

  ::llvm::StringRef getDescription() const override { return "Verifies that all TF ops have been legalized to XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifyTFXLALegalization");
  }
  ::llvm::StringRef getName() const override { return "VerifyTFXLALegalization"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VerifyTFXLALegalizationBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> legalize_chlo_{*this, "legalize-chlo", ::llvm::cl::desc("Legalizes intermediate chlo ops to hlo"), ::llvm::cl::init(true)};
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
