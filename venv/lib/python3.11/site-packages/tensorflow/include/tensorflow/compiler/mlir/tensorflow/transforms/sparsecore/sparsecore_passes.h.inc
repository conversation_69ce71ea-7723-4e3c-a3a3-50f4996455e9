/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_EMBEDDINGPIPELININGPASS
#define GEN_PASS_DECL_EMBEDDINGPROGRAMKEYPASS
#define GEN_PASS_DECL_EMBEDDINGSEQUENCINGPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// EmbeddingPipeliningPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_EMBEDDING<PERSON>PELININGPASS
#undef GEN_PASS_DECL_EMBEDDINGPIPELININGPASS
#endif // GEN_PASS_DECL_EMBEDDINGPIPELININGPASS
#ifdef GEN_PASS_DEF_EMBEDDINGPIPELININGPASS
namespace impl {

template <typename DerivedT>
class EmbeddingPipeliningPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = EmbeddingPipeliningPassBase;

  EmbeddingPipeliningPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  EmbeddingPipeliningPassBase(const EmbeddingPipeliningPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  EmbeddingPipeliningPassBase& operator=(const EmbeddingPipeliningPassBase &) = delete;
  EmbeddingPipeliningPassBase(EmbeddingPipeliningPassBase &&) = delete;
  EmbeddingPipeliningPassBase& operator=(EmbeddingPipeliningPassBase &&) = delete;
  ~EmbeddingPipeliningPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-embedding-pipelining");
  }
  ::llvm::StringRef getArgument() const override { return "tf-embedding-pipelining"; }

  ::llvm::StringRef getDescription() const override { return "Rewrite graph for embedding pipelining"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EmbeddingPipeliningPass");
  }
  ::llvm::StringRef getName() const override { return "EmbeddingPipeliningPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EmbeddingPipeliningPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_EMBEDDINGPIPELININGPASS
#endif // GEN_PASS_DEF_EMBEDDINGPIPELININGPASS

//===----------------------------------------------------------------------===//
// EmbeddingProgramKeyPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_EMBEDDINGPROGRAMKEYPASS
#undef GEN_PASS_DECL_EMBEDDINGPROGRAMKEYPASS
#endif // GEN_PASS_DECL_EMBEDDINGPROGRAMKEYPASS
#ifdef GEN_PASS_DEF_EMBEDDINGPROGRAMKEYPASS
namespace impl {

template <typename DerivedT>
class EmbeddingProgramKeyPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = EmbeddingProgramKeyPassBase;

  EmbeddingProgramKeyPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  EmbeddingProgramKeyPassBase(const EmbeddingProgramKeyPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  EmbeddingProgramKeyPassBase& operator=(const EmbeddingProgramKeyPassBase &) = delete;
  EmbeddingProgramKeyPassBase(EmbeddingProgramKeyPassBase &&) = delete;
  EmbeddingProgramKeyPassBase& operator=(EmbeddingProgramKeyPassBase &&) = delete;
  ~EmbeddingProgramKeyPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-embedding-program-key");
  }
  ::llvm::StringRef getArgument() const override { return "tf-embedding-program-key"; }

  ::llvm::StringRef getDescription() const override { return "Sets the program key for embedding ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EmbeddingProgramKeyPass");
  }
  ::llvm::StringRef getName() const override { return "EmbeddingProgramKeyPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EmbeddingProgramKeyPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_EMBEDDINGPROGRAMKEYPASS
#endif // GEN_PASS_DEF_EMBEDDINGPROGRAMKEYPASS

//===----------------------------------------------------------------------===//
// EmbeddingSequencingPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_EMBEDDINGSEQUENCINGPASS
#undef GEN_PASS_DECL_EMBEDDINGSEQUENCINGPASS
#endif // GEN_PASS_DECL_EMBEDDINGSEQUENCINGPASS
#ifdef GEN_PASS_DEF_EMBEDDINGSEQUENCINGPASS
namespace impl {

template <typename DerivedT>
class EmbeddingSequencingPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = EmbeddingSequencingPassBase;

  EmbeddingSequencingPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  EmbeddingSequencingPassBase(const EmbeddingSequencingPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  EmbeddingSequencingPassBase& operator=(const EmbeddingSequencingPassBase &) = delete;
  EmbeddingSequencingPassBase(EmbeddingSequencingPassBase &&) = delete;
  EmbeddingSequencingPassBase& operator=(EmbeddingSequencingPassBase &&) = delete;
  ~EmbeddingSequencingPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-embedding-sequencing");
  }
  ::llvm::StringRef getArgument() const override { return "tf-embedding-sequencing"; }

  ::llvm::StringRef getDescription() const override { return "Rewrite graph for sequential execution of embeddings"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EmbeddingSequencingPass");
  }
  ::llvm::StringRef getName() const override { return "EmbeddingSequencingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EmbeddingSequencingPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_EMBEDDINGSEQUENCINGPASS
#endif // GEN_PASS_DEF_EMBEDDINGSEQUENCINGPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// EmbeddingPipeliningPass Registration
//===----------------------------------------------------------------------===//

inline void registerEmbeddingPipeliningPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateEmbeddingPipeliningPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerEmbeddingPipeliningPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateEmbeddingPipeliningPass();
  });
}

//===----------------------------------------------------------------------===//
// EmbeddingProgramKeyPass Registration
//===----------------------------------------------------------------------===//

inline void registerEmbeddingProgramKeyPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateEmbeddingProgramKeyPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerEmbeddingProgramKeyPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateEmbeddingProgramKeyPass();
  });
}

//===----------------------------------------------------------------------===//
// EmbeddingSequencingPass Registration
//===----------------------------------------------------------------------===//

inline void registerEmbeddingSequencingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateEmbeddingSequencingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerEmbeddingSequencingPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateEmbeddingSequencingPass();
  });
}

//===----------------------------------------------------------------------===//
// SparseCore Registration
//===----------------------------------------------------------------------===//

inline void registerSparseCorePasses() {
  registerEmbeddingPipeliningPass();
  registerEmbeddingProgramKeyPass();
  registerEmbeddingSequencingPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class EmbeddingPipeliningPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = EmbeddingPipeliningPassBase;

  EmbeddingPipeliningPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  EmbeddingPipeliningPassBase(const EmbeddingPipeliningPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  EmbeddingPipeliningPassBase& operator=(const EmbeddingPipeliningPassBase &) = delete;
  EmbeddingPipeliningPassBase(EmbeddingPipeliningPassBase &&) = delete;
  EmbeddingPipeliningPassBase& operator=(EmbeddingPipeliningPassBase &&) = delete;
  ~EmbeddingPipeliningPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-embedding-pipelining");
  }
  ::llvm::StringRef getArgument() const override { return "tf-embedding-pipelining"; }

  ::llvm::StringRef getDescription() const override { return "Rewrite graph for embedding pipelining"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EmbeddingPipeliningPass");
  }
  ::llvm::StringRef getName() const override { return "EmbeddingPipeliningPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EmbeddingPipeliningPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class EmbeddingProgramKeyPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = EmbeddingProgramKeyPassBase;

  EmbeddingProgramKeyPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  EmbeddingProgramKeyPassBase(const EmbeddingProgramKeyPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  EmbeddingProgramKeyPassBase& operator=(const EmbeddingProgramKeyPassBase &) = delete;
  EmbeddingProgramKeyPassBase(EmbeddingProgramKeyPassBase &&) = delete;
  EmbeddingProgramKeyPassBase& operator=(EmbeddingProgramKeyPassBase &&) = delete;
  ~EmbeddingProgramKeyPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-embedding-program-key");
  }
  ::llvm::StringRef getArgument() const override { return "tf-embedding-program-key"; }

  ::llvm::StringRef getDescription() const override { return "Sets the program key for embedding ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EmbeddingProgramKeyPass");
  }
  ::llvm::StringRef getName() const override { return "EmbeddingProgramKeyPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EmbeddingProgramKeyPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class EmbeddingSequencingPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = EmbeddingSequencingPassBase;

  EmbeddingSequencingPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  EmbeddingSequencingPassBase(const EmbeddingSequencingPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  EmbeddingSequencingPassBase& operator=(const EmbeddingSequencingPassBase &) = delete;
  EmbeddingSequencingPassBase(EmbeddingSequencingPassBase &&) = delete;
  EmbeddingSequencingPassBase& operator=(EmbeddingSequencingPassBase &&) = delete;
  ~EmbeddingSequencingPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-embedding-sequencing");
  }
  ::llvm::StringRef getArgument() const override { return "tf-embedding-sequencing"; }

  ::llvm::StringRef getDescription() const override { return "Rewrite graph for sequential execution of embeddings"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("EmbeddingSequencingPass");
  }
  ::llvm::StringRef getName() const override { return "EmbeddingSequencingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(EmbeddingSequencingPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
