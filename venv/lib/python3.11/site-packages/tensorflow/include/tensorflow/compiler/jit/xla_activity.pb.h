// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/jit/xla_activity.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/config.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
namespace tensorflow {
class XlaAutoClusteringActivity;
struct XlaAutoClusteringActivityDefaultTypeInternal;
extern XlaAutoClusteringActivityDefaultTypeInternal _XlaAutoClusteringActivity_default_instance_;
class XlaAutoClusteringSummary;
struct XlaAutoClusteringSummaryDefaultTypeInternal;
extern XlaAutoClusteringSummaryDefaultTypeInternal _XlaAutoClusteringSummary_default_instance_;
class XlaAutoClusteringSummary_Cluster;
struct XlaAutoClusteringSummary_ClusterDefaultTypeInternal;
extern XlaAutoClusteringSummary_ClusterDefaultTypeInternal _XlaAutoClusteringSummary_Cluster_default_instance_;
class XlaAutoClusteringSummary_OpAndCount;
struct XlaAutoClusteringSummary_OpAndCountDefaultTypeInternal;
extern XlaAutoClusteringSummary_OpAndCountDefaultTypeInternal _XlaAutoClusteringSummary_OpAndCount_default_instance_;
class XlaJitCompilationActivity;
struct XlaJitCompilationActivityDefaultTypeInternal;
extern XlaJitCompilationActivityDefaultTypeInternal _XlaJitCompilationActivity_default_instance_;
class XlaOptimizationRemark;
struct XlaOptimizationRemarkDefaultTypeInternal;
extern XlaOptimizationRemarkDefaultTypeInternal _XlaOptimizationRemark_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::XlaAutoClusteringActivity* Arena::CreateMaybeMessage<::tensorflow::XlaAutoClusteringActivity>(Arena*);
template<> ::tensorflow::XlaAutoClusteringSummary* Arena::CreateMaybeMessage<::tensorflow::XlaAutoClusteringSummary>(Arena*);
template<> ::tensorflow::XlaAutoClusteringSummary_Cluster* Arena::CreateMaybeMessage<::tensorflow::XlaAutoClusteringSummary_Cluster>(Arena*);
template<> ::tensorflow::XlaAutoClusteringSummary_OpAndCount* Arena::CreateMaybeMessage<::tensorflow::XlaAutoClusteringSummary_OpAndCount>(Arena*);
template<> ::tensorflow::XlaJitCompilationActivity* Arena::CreateMaybeMessage<::tensorflow::XlaJitCompilationActivity>(Arena*);
template<> ::tensorflow::XlaOptimizationRemark* Arena::CreateMaybeMessage<::tensorflow::XlaOptimizationRemark>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum XlaOptimizationRemark_Warning : int {
  XlaOptimizationRemark_Warning_NONE = 0,
  XlaOptimizationRemark_Warning_INACCURATE_OPERATION = 1,
  XlaOptimizationRemark_Warning_SLOW_OPERATION = 2,
  XlaOptimizationRemark_Warning_UNIMPLEMENTED_OPERATION = 3,
  XlaOptimizationRemark_Warning_SLOW_IMAGE_RESIZE_DIMENSIONS = 4,
  XlaOptimizationRemark_Warning_MEGAMORPHIC_FUNCTION = 5,
  XlaOptimizationRemark_Warning_XlaOptimizationRemark_Warning_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  XlaOptimizationRemark_Warning_XlaOptimizationRemark_Warning_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool XlaOptimizationRemark_Warning_IsValid(int value);
constexpr XlaOptimizationRemark_Warning XlaOptimizationRemark_Warning_Warning_MIN = XlaOptimizationRemark_Warning_NONE;
constexpr XlaOptimizationRemark_Warning XlaOptimizationRemark_Warning_Warning_MAX = XlaOptimizationRemark_Warning_MEGAMORPHIC_FUNCTION;
constexpr int XlaOptimizationRemark_Warning_Warning_ARRAYSIZE = XlaOptimizationRemark_Warning_Warning_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* XlaOptimizationRemark_Warning_descriptor();
template<typename T>
inline const std::string& XlaOptimizationRemark_Warning_Name(T enum_t_value) {
  static_assert(::std::is_same<T, XlaOptimizationRemark_Warning>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function XlaOptimizationRemark_Warning_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    XlaOptimizationRemark_Warning_descriptor(), enum_t_value);
}
inline bool XlaOptimizationRemark_Warning_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, XlaOptimizationRemark_Warning* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<XlaOptimizationRemark_Warning>(
    XlaOptimizationRemark_Warning_descriptor(), name, value);
}
// ===================================================================

class XlaAutoClusteringSummary_OpAndCount final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaAutoClusteringSummary.OpAndCount) */ {
 public:
  inline XlaAutoClusteringSummary_OpAndCount() : XlaAutoClusteringSummary_OpAndCount(nullptr) {}
  ~XlaAutoClusteringSummary_OpAndCount() override;
  explicit PROTOBUF_CONSTEXPR XlaAutoClusteringSummary_OpAndCount(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XlaAutoClusteringSummary_OpAndCount(const XlaAutoClusteringSummary_OpAndCount& from);
  XlaAutoClusteringSummary_OpAndCount(XlaAutoClusteringSummary_OpAndCount&& from) noexcept
    : XlaAutoClusteringSummary_OpAndCount() {
    *this = ::std::move(from);
  }

  inline XlaAutoClusteringSummary_OpAndCount& operator=(const XlaAutoClusteringSummary_OpAndCount& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaAutoClusteringSummary_OpAndCount& operator=(XlaAutoClusteringSummary_OpAndCount&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XlaAutoClusteringSummary_OpAndCount& default_instance() {
    return *internal_default_instance();
  }
  static inline const XlaAutoClusteringSummary_OpAndCount* internal_default_instance() {
    return reinterpret_cast<const XlaAutoClusteringSummary_OpAndCount*>(
               &_XlaAutoClusteringSummary_OpAndCount_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(XlaAutoClusteringSummary_OpAndCount& a, XlaAutoClusteringSummary_OpAndCount& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaAutoClusteringSummary_OpAndCount* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XlaAutoClusteringSummary_OpAndCount* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XlaAutoClusteringSummary_OpAndCount* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XlaAutoClusteringSummary_OpAndCount>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XlaAutoClusteringSummary_OpAndCount& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XlaAutoClusteringSummary_OpAndCount& from) {
    XlaAutoClusteringSummary_OpAndCount::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaAutoClusteringSummary_OpAndCount* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaAutoClusteringSummary.OpAndCount";
  }
  protected:
  explicit XlaAutoClusteringSummary_OpAndCount(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpFieldNumber = 1,
    kCountFieldNumber = 2,
  };
  // string op = 1;
  void clear_op();
  const std::string& op() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op();
  PROTOBUF_NODISCARD std::string* release_op();
  void set_allocated_op(std::string* op);
  private:
  const std::string& _internal_op() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op(const std::string& value);
  std::string* _internal_mutable_op();
  public:

  // int32 count = 2;
  void clear_count();
  int32_t count() const;
  void set_count(int32_t value);
  private:
  int32_t _internal_count() const;
  void _internal_set_count(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.XlaAutoClusteringSummary.OpAndCount)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_;
    int32_t count_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// -------------------------------------------------------------------

class XlaAutoClusteringSummary_Cluster final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaAutoClusteringSummary.Cluster) */ {
 public:
  inline XlaAutoClusteringSummary_Cluster() : XlaAutoClusteringSummary_Cluster(nullptr) {}
  ~XlaAutoClusteringSummary_Cluster() override;
  explicit PROTOBUF_CONSTEXPR XlaAutoClusteringSummary_Cluster(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XlaAutoClusteringSummary_Cluster(const XlaAutoClusteringSummary_Cluster& from);
  XlaAutoClusteringSummary_Cluster(XlaAutoClusteringSummary_Cluster&& from) noexcept
    : XlaAutoClusteringSummary_Cluster() {
    *this = ::std::move(from);
  }

  inline XlaAutoClusteringSummary_Cluster& operator=(const XlaAutoClusteringSummary_Cluster& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaAutoClusteringSummary_Cluster& operator=(XlaAutoClusteringSummary_Cluster&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XlaAutoClusteringSummary_Cluster& default_instance() {
    return *internal_default_instance();
  }
  static inline const XlaAutoClusteringSummary_Cluster* internal_default_instance() {
    return reinterpret_cast<const XlaAutoClusteringSummary_Cluster*>(
               &_XlaAutoClusteringSummary_Cluster_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(XlaAutoClusteringSummary_Cluster& a, XlaAutoClusteringSummary_Cluster& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaAutoClusteringSummary_Cluster* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XlaAutoClusteringSummary_Cluster* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XlaAutoClusteringSummary_Cluster* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XlaAutoClusteringSummary_Cluster>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XlaAutoClusteringSummary_Cluster& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XlaAutoClusteringSummary_Cluster& from) {
    XlaAutoClusteringSummary_Cluster::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaAutoClusteringSummary_Cluster* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaAutoClusteringSummary.Cluster";
  }
  protected:
  explicit XlaAutoClusteringSummary_Cluster(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpHistogramFieldNumber = 3,
    kNameFieldNumber = 1,
    kSizeFieldNumber = 2,
  };
  // repeated .tensorflow.XlaAutoClusteringSummary.OpAndCount op_histogram = 3;
  int op_histogram_size() const;
  private:
  int _internal_op_histogram_size() const;
  public:
  void clear_op_histogram();
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* mutable_op_histogram(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >*
      mutable_op_histogram();
  private:
  const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& _internal_op_histogram(int index) const;
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* _internal_add_op_histogram();
  public:
  const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& op_histogram(int index) const;
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* add_op_histogram();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >&
      op_histogram() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int32 size = 2;
  void clear_size();
  int32_t size() const;
  void set_size(int32_t value);
  private:
  int32_t _internal_size() const;
  void _internal_set_size(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.XlaAutoClusteringSummary.Cluster)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount > op_histogram_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int32_t size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// -------------------------------------------------------------------

class XlaAutoClusteringSummary final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaAutoClusteringSummary) */ {
 public:
  inline XlaAutoClusteringSummary() : XlaAutoClusteringSummary(nullptr) {}
  ~XlaAutoClusteringSummary() override;
  explicit PROTOBUF_CONSTEXPR XlaAutoClusteringSummary(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XlaAutoClusteringSummary(const XlaAutoClusteringSummary& from);
  XlaAutoClusteringSummary(XlaAutoClusteringSummary&& from) noexcept
    : XlaAutoClusteringSummary() {
    *this = ::std::move(from);
  }

  inline XlaAutoClusteringSummary& operator=(const XlaAutoClusteringSummary& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaAutoClusteringSummary& operator=(XlaAutoClusteringSummary&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XlaAutoClusteringSummary& default_instance() {
    return *internal_default_instance();
  }
  static inline const XlaAutoClusteringSummary* internal_default_instance() {
    return reinterpret_cast<const XlaAutoClusteringSummary*>(
               &_XlaAutoClusteringSummary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(XlaAutoClusteringSummary& a, XlaAutoClusteringSummary& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaAutoClusteringSummary* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XlaAutoClusteringSummary* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XlaAutoClusteringSummary* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XlaAutoClusteringSummary>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XlaAutoClusteringSummary& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XlaAutoClusteringSummary& from) {
    XlaAutoClusteringSummary::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaAutoClusteringSummary* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaAutoClusteringSummary";
  }
  protected:
  explicit XlaAutoClusteringSummary(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef XlaAutoClusteringSummary_OpAndCount OpAndCount;
  typedef XlaAutoClusteringSummary_Cluster Cluster;

  // accessors -------------------------------------------------------

  enum : int {
    kClustersFieldNumber = 3,
    kUnclusteredOpHistogramFieldNumber = 4,
    kUnclusteredNodeCountFieldNumber = 1,
    kClusteredNodeCountFieldNumber = 2,
  };
  // repeated .tensorflow.XlaAutoClusteringSummary.Cluster clusters = 3;
  int clusters_size() const;
  private:
  int _internal_clusters_size() const;
  public:
  void clear_clusters();
  ::tensorflow::XlaAutoClusteringSummary_Cluster* mutable_clusters(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_Cluster >*
      mutable_clusters();
  private:
  const ::tensorflow::XlaAutoClusteringSummary_Cluster& _internal_clusters(int index) const;
  ::tensorflow::XlaAutoClusteringSummary_Cluster* _internal_add_clusters();
  public:
  const ::tensorflow::XlaAutoClusteringSummary_Cluster& clusters(int index) const;
  ::tensorflow::XlaAutoClusteringSummary_Cluster* add_clusters();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_Cluster >&
      clusters() const;

  // repeated .tensorflow.XlaAutoClusteringSummary.OpAndCount unclustered_op_histogram = 4;
  int unclustered_op_histogram_size() const;
  private:
  int _internal_unclustered_op_histogram_size() const;
  public:
  void clear_unclustered_op_histogram();
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* mutable_unclustered_op_histogram(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >*
      mutable_unclustered_op_histogram();
  private:
  const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& _internal_unclustered_op_histogram(int index) const;
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* _internal_add_unclustered_op_histogram();
  public:
  const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& unclustered_op_histogram(int index) const;
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* add_unclustered_op_histogram();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >&
      unclustered_op_histogram() const;

  // int32 unclustered_node_count = 1;
  void clear_unclustered_node_count();
  int32_t unclustered_node_count() const;
  void set_unclustered_node_count(int32_t value);
  private:
  int32_t _internal_unclustered_node_count() const;
  void _internal_set_unclustered_node_count(int32_t value);
  public:

  // int32 clustered_node_count = 2;
  void clear_clustered_node_count();
  int32_t clustered_node_count() const;
  void set_clustered_node_count(int32_t value);
  private:
  int32_t _internal_clustered_node_count() const;
  void _internal_set_clustered_node_count(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.XlaAutoClusteringSummary)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_Cluster > clusters_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount > unclustered_op_histogram_;
    int32_t unclustered_node_count_;
    int32_t clustered_node_count_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// -------------------------------------------------------------------

class XlaAutoClusteringActivity final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaAutoClusteringActivity) */ {
 public:
  inline XlaAutoClusteringActivity() : XlaAutoClusteringActivity(nullptr) {}
  ~XlaAutoClusteringActivity() override;
  explicit PROTOBUF_CONSTEXPR XlaAutoClusteringActivity(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XlaAutoClusteringActivity(const XlaAutoClusteringActivity& from);
  XlaAutoClusteringActivity(XlaAutoClusteringActivity&& from) noexcept
    : XlaAutoClusteringActivity() {
    *this = ::std::move(from);
  }

  inline XlaAutoClusteringActivity& operator=(const XlaAutoClusteringActivity& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaAutoClusteringActivity& operator=(XlaAutoClusteringActivity&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XlaAutoClusteringActivity& default_instance() {
    return *internal_default_instance();
  }
  static inline const XlaAutoClusteringActivity* internal_default_instance() {
    return reinterpret_cast<const XlaAutoClusteringActivity*>(
               &_XlaAutoClusteringActivity_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(XlaAutoClusteringActivity& a, XlaAutoClusteringActivity& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaAutoClusteringActivity* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XlaAutoClusteringActivity* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XlaAutoClusteringActivity* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XlaAutoClusteringActivity>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XlaAutoClusteringActivity& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XlaAutoClusteringActivity& from) {
    XlaAutoClusteringActivity::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaAutoClusteringActivity* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaAutoClusteringActivity";
  }
  protected:
  explicit XlaAutoClusteringActivity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSummaryFieldNumber = 3,
    kGlobalJitLevelFieldNumber = 1,
    kCpuGlobalJitEnabledFieldNumber = 2,
  };
  // .tensorflow.XlaAutoClusteringSummary summary = 3;
  bool has_summary() const;
  private:
  bool _internal_has_summary() const;
  public:
  void clear_summary();
  const ::tensorflow::XlaAutoClusteringSummary& summary() const;
  PROTOBUF_NODISCARD ::tensorflow::XlaAutoClusteringSummary* release_summary();
  ::tensorflow::XlaAutoClusteringSummary* mutable_summary();
  void set_allocated_summary(::tensorflow::XlaAutoClusteringSummary* summary);
  private:
  const ::tensorflow::XlaAutoClusteringSummary& _internal_summary() const;
  ::tensorflow::XlaAutoClusteringSummary* _internal_mutable_summary();
  public:
  void unsafe_arena_set_allocated_summary(
      ::tensorflow::XlaAutoClusteringSummary* summary);
  ::tensorflow::XlaAutoClusteringSummary* unsafe_arena_release_summary();

  // .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 1;
  void clear_global_jit_level();
  ::tensorflow::OptimizerOptions_GlobalJitLevel global_jit_level() const;
  void set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value);
  private:
  ::tensorflow::OptimizerOptions_GlobalJitLevel _internal_global_jit_level() const;
  void _internal_set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value);
  public:

  // bool cpu_global_jit_enabled = 2;
  void clear_cpu_global_jit_enabled();
  bool cpu_global_jit_enabled() const;
  void set_cpu_global_jit_enabled(bool value);
  private:
  bool _internal_cpu_global_jit_enabled() const;
  void _internal_set_cpu_global_jit_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.XlaAutoClusteringActivity)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::XlaAutoClusteringSummary* summary_;
    int global_jit_level_;
    bool cpu_global_jit_enabled_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// -------------------------------------------------------------------

class XlaJitCompilationActivity final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaJitCompilationActivity) */ {
 public:
  inline XlaJitCompilationActivity() : XlaJitCompilationActivity(nullptr) {}
  ~XlaJitCompilationActivity() override;
  explicit PROTOBUF_CONSTEXPR XlaJitCompilationActivity(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XlaJitCompilationActivity(const XlaJitCompilationActivity& from);
  XlaJitCompilationActivity(XlaJitCompilationActivity&& from) noexcept
    : XlaJitCompilationActivity() {
    *this = ::std::move(from);
  }

  inline XlaJitCompilationActivity& operator=(const XlaJitCompilationActivity& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaJitCompilationActivity& operator=(XlaJitCompilationActivity&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XlaJitCompilationActivity& default_instance() {
    return *internal_default_instance();
  }
  static inline const XlaJitCompilationActivity* internal_default_instance() {
    return reinterpret_cast<const XlaJitCompilationActivity*>(
               &_XlaJitCompilationActivity_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(XlaJitCompilationActivity& a, XlaJitCompilationActivity& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaJitCompilationActivity* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XlaJitCompilationActivity* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XlaJitCompilationActivity* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XlaJitCompilationActivity>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XlaJitCompilationActivity& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XlaJitCompilationActivity& from) {
    XlaJitCompilationActivity::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaJitCompilationActivity* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaJitCompilationActivity";
  }
  protected:
  explicit XlaJitCompilationActivity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClusterNameFieldNumber = 1,
    kCompileTimeUsFieldNumber = 3,
    kCompileCountFieldNumber = 2,
    kUsedPersistentCacheFieldNumber = 5,
    kCumulativeCompileTimeUsFieldNumber = 4,
  };
  // string cluster_name = 1;
  void clear_cluster_name();
  const std::string& cluster_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cluster_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cluster_name();
  PROTOBUF_NODISCARD std::string* release_cluster_name();
  void set_allocated_cluster_name(std::string* cluster_name);
  private:
  const std::string& _internal_cluster_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cluster_name(const std::string& value);
  std::string* _internal_mutable_cluster_name();
  public:

  // int64 compile_time_us = 3;
  void clear_compile_time_us();
  int64_t compile_time_us() const;
  void set_compile_time_us(int64_t value);
  private:
  int64_t _internal_compile_time_us() const;
  void _internal_set_compile_time_us(int64_t value);
  public:

  // int32 compile_count = 2;
  void clear_compile_count();
  int32_t compile_count() const;
  void set_compile_count(int32_t value);
  private:
  int32_t _internal_compile_count() const;
  void _internal_set_compile_count(int32_t value);
  public:

  // bool used_persistent_cache = 5;
  void clear_used_persistent_cache();
  bool used_persistent_cache() const;
  void set_used_persistent_cache(bool value);
  private:
  bool _internal_used_persistent_cache() const;
  void _internal_set_used_persistent_cache(bool value);
  public:

  // int64 cumulative_compile_time_us = 4;
  void clear_cumulative_compile_time_us();
  int64_t cumulative_compile_time_us() const;
  void set_cumulative_compile_time_us(int64_t value);
  private:
  int64_t _internal_cumulative_compile_time_us() const;
  void _internal_set_cumulative_compile_time_us(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.XlaJitCompilationActivity)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cluster_name_;
    int64_t compile_time_us_;
    int32_t compile_count_;
    bool used_persistent_cache_;
    int64_t cumulative_compile_time_us_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// -------------------------------------------------------------------

class XlaOptimizationRemark final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.XlaOptimizationRemark) */ {
 public:
  inline XlaOptimizationRemark() : XlaOptimizationRemark(nullptr) {}
  ~XlaOptimizationRemark() override;
  explicit PROTOBUF_CONSTEXPR XlaOptimizationRemark(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  XlaOptimizationRemark(const XlaOptimizationRemark& from);
  XlaOptimizationRemark(XlaOptimizationRemark&& from) noexcept
    : XlaOptimizationRemark() {
    *this = ::std::move(from);
  }

  inline XlaOptimizationRemark& operator=(const XlaOptimizationRemark& from) {
    CopyFrom(from);
    return *this;
  }
  inline XlaOptimizationRemark& operator=(XlaOptimizationRemark&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const XlaOptimizationRemark& default_instance() {
    return *internal_default_instance();
  }
  static inline const XlaOptimizationRemark* internal_default_instance() {
    return reinterpret_cast<const XlaOptimizationRemark*>(
               &_XlaOptimizationRemark_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(XlaOptimizationRemark& a, XlaOptimizationRemark& b) {
    a.Swap(&b);
  }
  inline void Swap(XlaOptimizationRemark* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(XlaOptimizationRemark* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  XlaOptimizationRemark* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<XlaOptimizationRemark>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const XlaOptimizationRemark& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const XlaOptimizationRemark& from) {
    XlaOptimizationRemark::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(XlaOptimizationRemark* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.XlaOptimizationRemark";
  }
  protected:
  explicit XlaOptimizationRemark(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef XlaOptimizationRemark_Warning Warning;
  static constexpr Warning NONE =
    XlaOptimizationRemark_Warning_NONE;
  static constexpr Warning INACCURATE_OPERATION =
    XlaOptimizationRemark_Warning_INACCURATE_OPERATION;
  static constexpr Warning SLOW_OPERATION =
    XlaOptimizationRemark_Warning_SLOW_OPERATION;
  static constexpr Warning UNIMPLEMENTED_OPERATION =
    XlaOptimizationRemark_Warning_UNIMPLEMENTED_OPERATION;
  static constexpr Warning SLOW_IMAGE_RESIZE_DIMENSIONS =
    XlaOptimizationRemark_Warning_SLOW_IMAGE_RESIZE_DIMENSIONS;
  static constexpr Warning MEGAMORPHIC_FUNCTION =
    XlaOptimizationRemark_Warning_MEGAMORPHIC_FUNCTION;
  static inline bool Warning_IsValid(int value) {
    return XlaOptimizationRemark_Warning_IsValid(value);
  }
  static constexpr Warning Warning_MIN =
    XlaOptimizationRemark_Warning_Warning_MIN;
  static constexpr Warning Warning_MAX =
    XlaOptimizationRemark_Warning_Warning_MAX;
  static constexpr int Warning_ARRAYSIZE =
    XlaOptimizationRemark_Warning_Warning_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Warning_descriptor() {
    return XlaOptimizationRemark_Warning_descriptor();
  }
  template<typename T>
  static inline const std::string& Warning_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Warning>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Warning_Name.");
    return XlaOptimizationRemark_Warning_Name(enum_t_value);
  }
  static inline bool Warning_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Warning* value) {
    return XlaOptimizationRemark_Warning_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kDebugInformationFieldNumber = 2,
    kWarningFieldNumber = 1,
  };
  // string debug_information = 2;
  void clear_debug_information();
  const std::string& debug_information() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_debug_information(ArgT0&& arg0, ArgT... args);
  std::string* mutable_debug_information();
  PROTOBUF_NODISCARD std::string* release_debug_information();
  void set_allocated_debug_information(std::string* debug_information);
  private:
  const std::string& _internal_debug_information() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_debug_information(const std::string& value);
  std::string* _internal_mutable_debug_information();
  public:

  // .tensorflow.XlaOptimizationRemark.Warning warning = 1;
  void clear_warning();
  ::tensorflow::XlaOptimizationRemark_Warning warning() const;
  void set_warning(::tensorflow::XlaOptimizationRemark_Warning value);
  private:
  ::tensorflow::XlaOptimizationRemark_Warning _internal_warning() const;
  void _internal_set_warning(::tensorflow::XlaOptimizationRemark_Warning value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.XlaOptimizationRemark)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr debug_information_;
    int warning_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// XlaAutoClusteringSummary_OpAndCount

// string op = 1;
inline void XlaAutoClusteringSummary_OpAndCount::clear_op() {
  _impl_.op_.ClearToEmpty();
}
inline const std::string& XlaAutoClusteringSummary_OpAndCount::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
  return _internal_op();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XlaAutoClusteringSummary_OpAndCount::set_op(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
}
inline std::string* XlaAutoClusteringSummary_OpAndCount::mutable_op() {
  std::string* _s = _internal_mutable_op();
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
  return _s;
}
inline const std::string& XlaAutoClusteringSummary_OpAndCount::_internal_op() const {
  return _impl_.op_.Get();
}
inline void XlaAutoClusteringSummary_OpAndCount::_internal_set_op(const std::string& value) {
  
  _impl_.op_.Set(value, GetArenaForAllocation());
}
inline std::string* XlaAutoClusteringSummary_OpAndCount::_internal_mutable_op() {
  
  return _impl_.op_.Mutable(GetArenaForAllocation());
}
inline std::string* XlaAutoClusteringSummary_OpAndCount::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
  return _impl_.op_.Release();
}
inline void XlaAutoClusteringSummary_OpAndCount::set_allocated_op(std::string* op) {
  if (op != nullptr) {
    
  } else {
    
  }
  _impl_.op_.SetAllocated(op, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_.IsDefault()) {
    _impl_.op_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.XlaAutoClusteringSummary.OpAndCount.op)
}

// int32 count = 2;
inline void XlaAutoClusteringSummary_OpAndCount::clear_count() {
  _impl_.count_ = 0;
}
inline int32_t XlaAutoClusteringSummary_OpAndCount::_internal_count() const {
  return _impl_.count_;
}
inline int32_t XlaAutoClusteringSummary_OpAndCount::count() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.OpAndCount.count)
  return _internal_count();
}
inline void XlaAutoClusteringSummary_OpAndCount::_internal_set_count(int32_t value) {
  
  _impl_.count_ = value;
}
inline void XlaAutoClusteringSummary_OpAndCount::set_count(int32_t value) {
  _internal_set_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.OpAndCount.count)
}

// -------------------------------------------------------------------

// XlaAutoClusteringSummary_Cluster

// string name = 1;
inline void XlaAutoClusteringSummary_Cluster::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& XlaAutoClusteringSummary_Cluster::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.Cluster.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XlaAutoClusteringSummary_Cluster::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.Cluster.name)
}
inline std::string* XlaAutoClusteringSummary_Cluster::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringSummary.Cluster.name)
  return _s;
}
inline const std::string& XlaAutoClusteringSummary_Cluster::_internal_name() const {
  return _impl_.name_.Get();
}
inline void XlaAutoClusteringSummary_Cluster::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* XlaAutoClusteringSummary_Cluster::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* XlaAutoClusteringSummary_Cluster::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.XlaAutoClusteringSummary.Cluster.name)
  return _impl_.name_.Release();
}
inline void XlaAutoClusteringSummary_Cluster::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.XlaAutoClusteringSummary.Cluster.name)
}

// int32 size = 2;
inline void XlaAutoClusteringSummary_Cluster::clear_size() {
  _impl_.size_ = 0;
}
inline int32_t XlaAutoClusteringSummary_Cluster::_internal_size() const {
  return _impl_.size_;
}
inline int32_t XlaAutoClusteringSummary_Cluster::size() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.Cluster.size)
  return _internal_size();
}
inline void XlaAutoClusteringSummary_Cluster::_internal_set_size(int32_t value) {
  
  _impl_.size_ = value;
}
inline void XlaAutoClusteringSummary_Cluster::set_size(int32_t value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.Cluster.size)
}

// repeated .tensorflow.XlaAutoClusteringSummary.OpAndCount op_histogram = 3;
inline int XlaAutoClusteringSummary_Cluster::_internal_op_histogram_size() const {
  return _impl_.op_histogram_.size();
}
inline int XlaAutoClusteringSummary_Cluster::op_histogram_size() const {
  return _internal_op_histogram_size();
}
inline void XlaAutoClusteringSummary_Cluster::clear_op_histogram() {
  _impl_.op_histogram_.Clear();
}
inline ::tensorflow::XlaAutoClusteringSummary_OpAndCount* XlaAutoClusteringSummary_Cluster::mutable_op_histogram(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringSummary.Cluster.op_histogram)
  return _impl_.op_histogram_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >*
XlaAutoClusteringSummary_Cluster::mutable_op_histogram() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.XlaAutoClusteringSummary.Cluster.op_histogram)
  return &_impl_.op_histogram_;
}
inline const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& XlaAutoClusteringSummary_Cluster::_internal_op_histogram(int index) const {
  return _impl_.op_histogram_.Get(index);
}
inline const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& XlaAutoClusteringSummary_Cluster::op_histogram(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.Cluster.op_histogram)
  return _internal_op_histogram(index);
}
inline ::tensorflow::XlaAutoClusteringSummary_OpAndCount* XlaAutoClusteringSummary_Cluster::_internal_add_op_histogram() {
  return _impl_.op_histogram_.Add();
}
inline ::tensorflow::XlaAutoClusteringSummary_OpAndCount* XlaAutoClusteringSummary_Cluster::add_op_histogram() {
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* _add = _internal_add_op_histogram();
  // @@protoc_insertion_point(field_add:tensorflow.XlaAutoClusteringSummary.Cluster.op_histogram)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >&
XlaAutoClusteringSummary_Cluster::op_histogram() const {
  // @@protoc_insertion_point(field_list:tensorflow.XlaAutoClusteringSummary.Cluster.op_histogram)
  return _impl_.op_histogram_;
}

// -------------------------------------------------------------------

// XlaAutoClusteringSummary

// int32 unclustered_node_count = 1;
inline void XlaAutoClusteringSummary::clear_unclustered_node_count() {
  _impl_.unclustered_node_count_ = 0;
}
inline int32_t XlaAutoClusteringSummary::_internal_unclustered_node_count() const {
  return _impl_.unclustered_node_count_;
}
inline int32_t XlaAutoClusteringSummary::unclustered_node_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.unclustered_node_count)
  return _internal_unclustered_node_count();
}
inline void XlaAutoClusteringSummary::_internal_set_unclustered_node_count(int32_t value) {
  
  _impl_.unclustered_node_count_ = value;
}
inline void XlaAutoClusteringSummary::set_unclustered_node_count(int32_t value) {
  _internal_set_unclustered_node_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.unclustered_node_count)
}

// int32 clustered_node_count = 2;
inline void XlaAutoClusteringSummary::clear_clustered_node_count() {
  _impl_.clustered_node_count_ = 0;
}
inline int32_t XlaAutoClusteringSummary::_internal_clustered_node_count() const {
  return _impl_.clustered_node_count_;
}
inline int32_t XlaAutoClusteringSummary::clustered_node_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.clustered_node_count)
  return _internal_clustered_node_count();
}
inline void XlaAutoClusteringSummary::_internal_set_clustered_node_count(int32_t value) {
  
  _impl_.clustered_node_count_ = value;
}
inline void XlaAutoClusteringSummary::set_clustered_node_count(int32_t value) {
  _internal_set_clustered_node_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringSummary.clustered_node_count)
}

// repeated .tensorflow.XlaAutoClusteringSummary.Cluster clusters = 3;
inline int XlaAutoClusteringSummary::_internal_clusters_size() const {
  return _impl_.clusters_.size();
}
inline int XlaAutoClusteringSummary::clusters_size() const {
  return _internal_clusters_size();
}
inline void XlaAutoClusteringSummary::clear_clusters() {
  _impl_.clusters_.Clear();
}
inline ::tensorflow::XlaAutoClusteringSummary_Cluster* XlaAutoClusteringSummary::mutable_clusters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringSummary.clusters)
  return _impl_.clusters_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_Cluster >*
XlaAutoClusteringSummary::mutable_clusters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.XlaAutoClusteringSummary.clusters)
  return &_impl_.clusters_;
}
inline const ::tensorflow::XlaAutoClusteringSummary_Cluster& XlaAutoClusteringSummary::_internal_clusters(int index) const {
  return _impl_.clusters_.Get(index);
}
inline const ::tensorflow::XlaAutoClusteringSummary_Cluster& XlaAutoClusteringSummary::clusters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.clusters)
  return _internal_clusters(index);
}
inline ::tensorflow::XlaAutoClusteringSummary_Cluster* XlaAutoClusteringSummary::_internal_add_clusters() {
  return _impl_.clusters_.Add();
}
inline ::tensorflow::XlaAutoClusteringSummary_Cluster* XlaAutoClusteringSummary::add_clusters() {
  ::tensorflow::XlaAutoClusteringSummary_Cluster* _add = _internal_add_clusters();
  // @@protoc_insertion_point(field_add:tensorflow.XlaAutoClusteringSummary.clusters)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_Cluster >&
XlaAutoClusteringSummary::clusters() const {
  // @@protoc_insertion_point(field_list:tensorflow.XlaAutoClusteringSummary.clusters)
  return _impl_.clusters_;
}

// repeated .tensorflow.XlaAutoClusteringSummary.OpAndCount unclustered_op_histogram = 4;
inline int XlaAutoClusteringSummary::_internal_unclustered_op_histogram_size() const {
  return _impl_.unclustered_op_histogram_.size();
}
inline int XlaAutoClusteringSummary::unclustered_op_histogram_size() const {
  return _internal_unclustered_op_histogram_size();
}
inline void XlaAutoClusteringSummary::clear_unclustered_op_histogram() {
  _impl_.unclustered_op_histogram_.Clear();
}
inline ::tensorflow::XlaAutoClusteringSummary_OpAndCount* XlaAutoClusteringSummary::mutable_unclustered_op_histogram(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringSummary.unclustered_op_histogram)
  return _impl_.unclustered_op_histogram_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >*
XlaAutoClusteringSummary::mutable_unclustered_op_histogram() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.XlaAutoClusteringSummary.unclustered_op_histogram)
  return &_impl_.unclustered_op_histogram_;
}
inline const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& XlaAutoClusteringSummary::_internal_unclustered_op_histogram(int index) const {
  return _impl_.unclustered_op_histogram_.Get(index);
}
inline const ::tensorflow::XlaAutoClusteringSummary_OpAndCount& XlaAutoClusteringSummary::unclustered_op_histogram(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringSummary.unclustered_op_histogram)
  return _internal_unclustered_op_histogram(index);
}
inline ::tensorflow::XlaAutoClusteringSummary_OpAndCount* XlaAutoClusteringSummary::_internal_add_unclustered_op_histogram() {
  return _impl_.unclustered_op_histogram_.Add();
}
inline ::tensorflow::XlaAutoClusteringSummary_OpAndCount* XlaAutoClusteringSummary::add_unclustered_op_histogram() {
  ::tensorflow::XlaAutoClusteringSummary_OpAndCount* _add = _internal_add_unclustered_op_histogram();
  // @@protoc_insertion_point(field_add:tensorflow.XlaAutoClusteringSummary.unclustered_op_histogram)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::XlaAutoClusteringSummary_OpAndCount >&
XlaAutoClusteringSummary::unclustered_op_histogram() const {
  // @@protoc_insertion_point(field_list:tensorflow.XlaAutoClusteringSummary.unclustered_op_histogram)
  return _impl_.unclustered_op_histogram_;
}

// -------------------------------------------------------------------

// XlaAutoClusteringActivity

// .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 1;
inline void XlaAutoClusteringActivity::clear_global_jit_level() {
  _impl_.global_jit_level_ = 0;
}
inline ::tensorflow::OptimizerOptions_GlobalJitLevel XlaAutoClusteringActivity::_internal_global_jit_level() const {
  return static_cast< ::tensorflow::OptimizerOptions_GlobalJitLevel >(_impl_.global_jit_level_);
}
inline ::tensorflow::OptimizerOptions_GlobalJitLevel XlaAutoClusteringActivity::global_jit_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringActivity.global_jit_level)
  return _internal_global_jit_level();
}
inline void XlaAutoClusteringActivity::_internal_set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value) {
  
  _impl_.global_jit_level_ = value;
}
inline void XlaAutoClusteringActivity::set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value) {
  _internal_set_global_jit_level(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringActivity.global_jit_level)
}

// bool cpu_global_jit_enabled = 2;
inline void XlaAutoClusteringActivity::clear_cpu_global_jit_enabled() {
  _impl_.cpu_global_jit_enabled_ = false;
}
inline bool XlaAutoClusteringActivity::_internal_cpu_global_jit_enabled() const {
  return _impl_.cpu_global_jit_enabled_;
}
inline bool XlaAutoClusteringActivity::cpu_global_jit_enabled() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringActivity.cpu_global_jit_enabled)
  return _internal_cpu_global_jit_enabled();
}
inline void XlaAutoClusteringActivity::_internal_set_cpu_global_jit_enabled(bool value) {
  
  _impl_.cpu_global_jit_enabled_ = value;
}
inline void XlaAutoClusteringActivity::set_cpu_global_jit_enabled(bool value) {
  _internal_set_cpu_global_jit_enabled(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaAutoClusteringActivity.cpu_global_jit_enabled)
}

// .tensorflow.XlaAutoClusteringSummary summary = 3;
inline bool XlaAutoClusteringActivity::_internal_has_summary() const {
  return this != internal_default_instance() && _impl_.summary_ != nullptr;
}
inline bool XlaAutoClusteringActivity::has_summary() const {
  return _internal_has_summary();
}
inline void XlaAutoClusteringActivity::clear_summary() {
  if (GetArenaForAllocation() == nullptr && _impl_.summary_ != nullptr) {
    delete _impl_.summary_;
  }
  _impl_.summary_ = nullptr;
}
inline const ::tensorflow::XlaAutoClusteringSummary& XlaAutoClusteringActivity::_internal_summary() const {
  const ::tensorflow::XlaAutoClusteringSummary* p = _impl_.summary_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::XlaAutoClusteringSummary&>(
      ::tensorflow::_XlaAutoClusteringSummary_default_instance_);
}
inline const ::tensorflow::XlaAutoClusteringSummary& XlaAutoClusteringActivity::summary() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaAutoClusteringActivity.summary)
  return _internal_summary();
}
inline void XlaAutoClusteringActivity::unsafe_arena_set_allocated_summary(
    ::tensorflow::XlaAutoClusteringSummary* summary) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.summary_);
  }
  _impl_.summary_ = summary;
  if (summary) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.XlaAutoClusteringActivity.summary)
}
inline ::tensorflow::XlaAutoClusteringSummary* XlaAutoClusteringActivity::release_summary() {
  
  ::tensorflow::XlaAutoClusteringSummary* temp = _impl_.summary_;
  _impl_.summary_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::XlaAutoClusteringSummary* XlaAutoClusteringActivity::unsafe_arena_release_summary() {
  // @@protoc_insertion_point(field_release:tensorflow.XlaAutoClusteringActivity.summary)
  
  ::tensorflow::XlaAutoClusteringSummary* temp = _impl_.summary_;
  _impl_.summary_ = nullptr;
  return temp;
}
inline ::tensorflow::XlaAutoClusteringSummary* XlaAutoClusteringActivity::_internal_mutable_summary() {
  
  if (_impl_.summary_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::XlaAutoClusteringSummary>(GetArenaForAllocation());
    _impl_.summary_ = p;
  }
  return _impl_.summary_;
}
inline ::tensorflow::XlaAutoClusteringSummary* XlaAutoClusteringActivity::mutable_summary() {
  ::tensorflow::XlaAutoClusteringSummary* _msg = _internal_mutable_summary();
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaAutoClusteringActivity.summary)
  return _msg;
}
inline void XlaAutoClusteringActivity::set_allocated_summary(::tensorflow::XlaAutoClusteringSummary* summary) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.summary_;
  }
  if (summary) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(summary);
    if (message_arena != submessage_arena) {
      summary = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, summary, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.summary_ = summary;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.XlaAutoClusteringActivity.summary)
}

// -------------------------------------------------------------------

// XlaJitCompilationActivity

// string cluster_name = 1;
inline void XlaJitCompilationActivity::clear_cluster_name() {
  _impl_.cluster_name_.ClearToEmpty();
}
inline const std::string& XlaJitCompilationActivity::cluster_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaJitCompilationActivity.cluster_name)
  return _internal_cluster_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XlaJitCompilationActivity::set_cluster_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.cluster_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.XlaJitCompilationActivity.cluster_name)
}
inline std::string* XlaJitCompilationActivity::mutable_cluster_name() {
  std::string* _s = _internal_mutable_cluster_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaJitCompilationActivity.cluster_name)
  return _s;
}
inline const std::string& XlaJitCompilationActivity::_internal_cluster_name() const {
  return _impl_.cluster_name_.Get();
}
inline void XlaJitCompilationActivity::_internal_set_cluster_name(const std::string& value) {
  
  _impl_.cluster_name_.Set(value, GetArenaForAllocation());
}
inline std::string* XlaJitCompilationActivity::_internal_mutable_cluster_name() {
  
  return _impl_.cluster_name_.Mutable(GetArenaForAllocation());
}
inline std::string* XlaJitCompilationActivity::release_cluster_name() {
  // @@protoc_insertion_point(field_release:tensorflow.XlaJitCompilationActivity.cluster_name)
  return _impl_.cluster_name_.Release();
}
inline void XlaJitCompilationActivity::set_allocated_cluster_name(std::string* cluster_name) {
  if (cluster_name != nullptr) {
    
  } else {
    
  }
  _impl_.cluster_name_.SetAllocated(cluster_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.cluster_name_.IsDefault()) {
    _impl_.cluster_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.XlaJitCompilationActivity.cluster_name)
}

// int32 compile_count = 2;
inline void XlaJitCompilationActivity::clear_compile_count() {
  _impl_.compile_count_ = 0;
}
inline int32_t XlaJitCompilationActivity::_internal_compile_count() const {
  return _impl_.compile_count_;
}
inline int32_t XlaJitCompilationActivity::compile_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaJitCompilationActivity.compile_count)
  return _internal_compile_count();
}
inline void XlaJitCompilationActivity::_internal_set_compile_count(int32_t value) {
  
  _impl_.compile_count_ = value;
}
inline void XlaJitCompilationActivity::set_compile_count(int32_t value) {
  _internal_set_compile_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaJitCompilationActivity.compile_count)
}

// int64 compile_time_us = 3;
inline void XlaJitCompilationActivity::clear_compile_time_us() {
  _impl_.compile_time_us_ = int64_t{0};
}
inline int64_t XlaJitCompilationActivity::_internal_compile_time_us() const {
  return _impl_.compile_time_us_;
}
inline int64_t XlaJitCompilationActivity::compile_time_us() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaJitCompilationActivity.compile_time_us)
  return _internal_compile_time_us();
}
inline void XlaJitCompilationActivity::_internal_set_compile_time_us(int64_t value) {
  
  _impl_.compile_time_us_ = value;
}
inline void XlaJitCompilationActivity::set_compile_time_us(int64_t value) {
  _internal_set_compile_time_us(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaJitCompilationActivity.compile_time_us)
}

// int64 cumulative_compile_time_us = 4;
inline void XlaJitCompilationActivity::clear_cumulative_compile_time_us() {
  _impl_.cumulative_compile_time_us_ = int64_t{0};
}
inline int64_t XlaJitCompilationActivity::_internal_cumulative_compile_time_us() const {
  return _impl_.cumulative_compile_time_us_;
}
inline int64_t XlaJitCompilationActivity::cumulative_compile_time_us() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaJitCompilationActivity.cumulative_compile_time_us)
  return _internal_cumulative_compile_time_us();
}
inline void XlaJitCompilationActivity::_internal_set_cumulative_compile_time_us(int64_t value) {
  
  _impl_.cumulative_compile_time_us_ = value;
}
inline void XlaJitCompilationActivity::set_cumulative_compile_time_us(int64_t value) {
  _internal_set_cumulative_compile_time_us(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaJitCompilationActivity.cumulative_compile_time_us)
}

// bool used_persistent_cache = 5;
inline void XlaJitCompilationActivity::clear_used_persistent_cache() {
  _impl_.used_persistent_cache_ = false;
}
inline bool XlaJitCompilationActivity::_internal_used_persistent_cache() const {
  return _impl_.used_persistent_cache_;
}
inline bool XlaJitCompilationActivity::used_persistent_cache() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaJitCompilationActivity.used_persistent_cache)
  return _internal_used_persistent_cache();
}
inline void XlaJitCompilationActivity::_internal_set_used_persistent_cache(bool value) {
  
  _impl_.used_persistent_cache_ = value;
}
inline void XlaJitCompilationActivity::set_used_persistent_cache(bool value) {
  _internal_set_used_persistent_cache(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaJitCompilationActivity.used_persistent_cache)
}

// -------------------------------------------------------------------

// XlaOptimizationRemark

// .tensorflow.XlaOptimizationRemark.Warning warning = 1;
inline void XlaOptimizationRemark::clear_warning() {
  _impl_.warning_ = 0;
}
inline ::tensorflow::XlaOptimizationRemark_Warning XlaOptimizationRemark::_internal_warning() const {
  return static_cast< ::tensorflow::XlaOptimizationRemark_Warning >(_impl_.warning_);
}
inline ::tensorflow::XlaOptimizationRemark_Warning XlaOptimizationRemark::warning() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaOptimizationRemark.warning)
  return _internal_warning();
}
inline void XlaOptimizationRemark::_internal_set_warning(::tensorflow::XlaOptimizationRemark_Warning value) {
  
  _impl_.warning_ = value;
}
inline void XlaOptimizationRemark::set_warning(::tensorflow::XlaOptimizationRemark_Warning value) {
  _internal_set_warning(value);
  // @@protoc_insertion_point(field_set:tensorflow.XlaOptimizationRemark.warning)
}

// string debug_information = 2;
inline void XlaOptimizationRemark::clear_debug_information() {
  _impl_.debug_information_.ClearToEmpty();
}
inline const std::string& XlaOptimizationRemark::debug_information() const {
  // @@protoc_insertion_point(field_get:tensorflow.XlaOptimizationRemark.debug_information)
  return _internal_debug_information();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void XlaOptimizationRemark::set_debug_information(ArgT0&& arg0, ArgT... args) {
 
 _impl_.debug_information_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.XlaOptimizationRemark.debug_information)
}
inline std::string* XlaOptimizationRemark::mutable_debug_information() {
  std::string* _s = _internal_mutable_debug_information();
  // @@protoc_insertion_point(field_mutable:tensorflow.XlaOptimizationRemark.debug_information)
  return _s;
}
inline const std::string& XlaOptimizationRemark::_internal_debug_information() const {
  return _impl_.debug_information_.Get();
}
inline void XlaOptimizationRemark::_internal_set_debug_information(const std::string& value) {
  
  _impl_.debug_information_.Set(value, GetArenaForAllocation());
}
inline std::string* XlaOptimizationRemark::_internal_mutable_debug_information() {
  
  return _impl_.debug_information_.Mutable(GetArenaForAllocation());
}
inline std::string* XlaOptimizationRemark::release_debug_information() {
  // @@protoc_insertion_point(field_release:tensorflow.XlaOptimizationRemark.debug_information)
  return _impl_.debug_information_.Release();
}
inline void XlaOptimizationRemark::set_allocated_debug_information(std::string* debug_information) {
  if (debug_information != nullptr) {
    
  } else {
    
  }
  _impl_.debug_information_.SetAllocated(debug_information, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.debug_information_.IsDefault()) {
    _impl_.debug_information_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.XlaOptimizationRemark.debug_information)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::XlaOptimizationRemark_Warning> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::XlaOptimizationRemark_Warning>() {
  return ::tensorflow::XlaOptimizationRemark_Warning_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fjit_2fxla_5factivity_2eproto
