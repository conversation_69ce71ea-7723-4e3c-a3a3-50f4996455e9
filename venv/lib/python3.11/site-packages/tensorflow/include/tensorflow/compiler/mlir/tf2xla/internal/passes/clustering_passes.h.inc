/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_EXTRACTHEADTAILOUTSIDECOMPILATIONPASS
#define GEN_PASS_DECL_EXTRACTOUTSIDECOMPILATIONPASS
#define GEN_PASS_DECL_HOISTBROADCASTREADPASS
#define GEN_PASS_DECL_MARKOPSFOROUTSI<PERSON>COMPILATIONPASS
#define GEN_PASS_DECL_TPUCLUSTERFORMATIONPASS
#define GEN_PASS_DECL_TPUSHARDINGIDENTIFICATIONPASS
#define GEN_PASS_DECL_TPUVALIDATEINPUTSPASS
#define GEN_PASS_DECL_TPUVALIDATESESSIONINPUTSPASS
#define GEN_PASS_DECL_VERIFYCLUSTERINGPASS
#define GEN_PASS_DECL_XLABROADCASTPASS
#define GEN_PASS_DECL_XLACLUSTERFORMATIONPASS
#undef GEN_PA<PERSON>_<PERSON><PERSON>
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// ExtractHeadTailOutsideCompilationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_EXTRACTHEADTAILOUTSIDECOMPILATIONPASS
#undef GEN_PASS_DECL_EXTRACTHEADTAILOUTSIDECOMPILATIONPASS
#endif // GEN_PASS_DECL_EXTRACTHEADTAILOUTSIDECOMPILATIONPASS
#ifdef GEN_PASS_DEF_EXTRACTHEADTAILOUTSIDECOMPILATIONPASS
namespace impl {

template <typename DerivedT>
class ExtractHeadTailOutsideCompilationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ExtractHeadTailOutsideCompilationPassBase;

  ExtractHeadTailOutsideCompilationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ExtractHeadTailOutsideCompilationPassBase(const ExtractHeadTailOutsideCompilationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ExtractHeadTailOutsideCompilationPassBase& operator=(const ExtractHeadTailOutsideCompilationPassBase &) = delete;
  ExtractHeadTailOutsideCompilationPassBase(ExtractHeadTailOutsideCompilationPassBase &&) = delete;
  ExtractHeadTailOutsideCompilationPassBase& operator=(ExtractHeadTailOutsideCompilationPassBase &&) = delete;
  ~ExtractHeadTailOutsideCompilationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-extract-head-tail-outside-compilation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-extract-head-tail-outside-compilation"; }

  ::llvm::StringRef getDescription() const override { return "Extracts head or tail outside compilation to separate host launches before/after device cluster."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExtractHeadTailOutsideCompilationPass");
  }
  ::llvm::StringRef getName() const override { return "ExtractHeadTailOutsideCompilationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ExtractHeadTailOutsideCompilationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_EXTRACTHEADTAILOUTSIDECOMPILATIONPASS
#endif // GEN_PASS_DEF_EXTRACTHEADTAILOUTSIDECOMPILATIONPASS

//===----------------------------------------------------------------------===//
// ExtractOutsideCompilationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_EXTRACTOUTSIDECOMPILATIONPASS
#undef GEN_PASS_DECL_EXTRACTOUTSIDECOMPILATIONPASS
#endif // GEN_PASS_DECL_EXTRACTOUTSIDECOMPILATIONPASS
#ifdef GEN_PASS_DEF_EXTRACTOUTSIDECOMPILATIONPASS
namespace impl {

template <typename DerivedT>
class ExtractOutsideCompilationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ExtractOutsideCompilationPassBase;

  ExtractOutsideCompilationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ExtractOutsideCompilationPassBase(const ExtractOutsideCompilationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ExtractOutsideCompilationPassBase& operator=(const ExtractOutsideCompilationPassBase &) = delete;
  ExtractOutsideCompilationPassBase(ExtractOutsideCompilationPassBase &&) = delete;
  ExtractOutsideCompilationPassBase& operator=(ExtractOutsideCompilationPassBase &&) = delete;
  ~ExtractOutsideCompilationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-extract-outside-compilation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-extract-outside-compilation"; }

  ::llvm::StringRef getDescription() const override { return "Extracts device outside compilation computation to a separate tf_device.parallel_execute region."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExtractOutsideCompilationPass");
  }
  ::llvm::StringRef getName() const override { return "ExtractOutsideCompilationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ExtractOutsideCompilationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_EXTRACTOUTSIDECOMPILATIONPASS
#endif // GEN_PASS_DEF_EXTRACTOUTSIDECOMPILATIONPASS

//===----------------------------------------------------------------------===//
// HoistBroadcastReadPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_HOISTBROADCASTREADPASS
#undef GEN_PASS_DECL_HOISTBROADCASTREADPASS
#endif // GEN_PASS_DECL_HOISTBROADCASTREADPASS
#ifdef GEN_PASS_DEF_HOISTBROADCASTREADPASS
namespace impl {

template <typename DerivedT>
class HoistBroadcastReadPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = HoistBroadcastReadPassBase;

  HoistBroadcastReadPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  HoistBroadcastReadPassBase(const HoistBroadcastReadPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  HoistBroadcastReadPassBase& operator=(const HoistBroadcastReadPassBase &) = delete;
  HoistBroadcastReadPassBase(HoistBroadcastReadPassBase &&) = delete;
  HoistBroadcastReadPassBase& operator=(HoistBroadcastReadPassBase &&) = delete;
  ~HoistBroadcastReadPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-hoist-broadcast-read");
  }
  ::llvm::StringRef getArgument() const override { return "tf-hoist-broadcast-read"; }

  ::llvm::StringRef getDescription() const override { return "Hoist reads out of a replicate that are on a resource that is broacast to all replicas."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HoistBroadcastReadPass");
  }
  ::llvm::StringRef getName() const override { return "HoistBroadcastReadPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(HoistBroadcastReadPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_HOISTBROADCASTREADPASS
#endif // GEN_PASS_DEF_HOISTBROADCASTREADPASS

//===----------------------------------------------------------------------===//
// MarkOpsForOutsideCompilationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_MARKOPSFOROUTSIDECOMPILATIONPASS
#undef GEN_PASS_DECL_MARKOPSFOROUTSIDECOMPILATIONPASS
#endif // GEN_PASS_DECL_MARKOPSFOROUTSIDECOMPILATIONPASS
#ifdef GEN_PASS_DEF_MARKOPSFOROUTSIDECOMPILATIONPASS
namespace impl {

template <typename DerivedT>
class MarkOpsForOutsideCompilationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = MarkOpsForOutsideCompilationPassBase;

  MarkOpsForOutsideCompilationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  MarkOpsForOutsideCompilationPassBase(const MarkOpsForOutsideCompilationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  MarkOpsForOutsideCompilationPassBase& operator=(const MarkOpsForOutsideCompilationPassBase &) = delete;
  MarkOpsForOutsideCompilationPassBase(MarkOpsForOutsideCompilationPassBase &&) = delete;
  MarkOpsForOutsideCompilationPassBase& operator=(MarkOpsForOutsideCompilationPassBase &&) = delete;
  ~MarkOpsForOutsideCompilationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-mark-ops-for-outside-compilation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-mark-ops-for-outside-compilation"; }

  ::llvm::StringRef getDescription() const override { return "Marks ops in device cluster for outside compilation if they are unsupported on device."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MarkOpsForOutsideCompilationPass");
  }
  ::llvm::StringRef getName() const override { return "MarkOpsForOutsideCompilationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(MarkOpsForOutsideCompilationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_MARKOPSFOROUTSIDECOMPILATIONPASS
#endif // GEN_PASS_DEF_MARKOPSFOROUTSIDECOMPILATIONPASS

//===----------------------------------------------------------------------===//
// TPUClusterFormationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TPUCLUSTERFORMATIONPASS
#undef GEN_PASS_DECL_TPUCLUSTERFORMATIONPASS
#endif // GEN_PASS_DECL_TPUCLUSTERFORMATIONPASS
#ifdef GEN_PASS_DEF_TPUCLUSTERFORMATIONPASS
namespace impl {

template <typename DerivedT>
class TPUClusterFormationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUClusterFormationPassBase;

  TPUClusterFormationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUClusterFormationPassBase(const TPUClusterFormationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  TPUClusterFormationPassBase& operator=(const TPUClusterFormationPassBase &) = delete;
  TPUClusterFormationPassBase(TPUClusterFormationPassBase &&) = delete;
  TPUClusterFormationPassBase& operator=(TPUClusterFormationPassBase &&) = delete;
  ~TPUClusterFormationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-cluster-formation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-cluster-formation"; }

  ::llvm::StringRef getDescription() const override { return "Forms clusters from operations assigned to the same TPU computation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUClusterFormationPass");
  }
  ::llvm::StringRef getName() const override { return "TPUClusterFormationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUClusterFormationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TPUCLUSTERFORMATIONPASS
#endif // GEN_PASS_DEF_TPUCLUSTERFORMATIONPASS

//===----------------------------------------------------------------------===//
// TPUShardingIdentificationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TPUSHARDINGIDENTIFICATIONPASS
#undef GEN_PASS_DECL_TPUSHARDINGIDENTIFICATIONPASS
#endif // GEN_PASS_DECL_TPUSHARDINGIDENTIFICATIONPASS
#ifdef GEN_PASS_DEF_TPUSHARDINGIDENTIFICATIONPASS
namespace impl {

template <typename DerivedT>
class TPUShardingIdentificationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUShardingIdentificationPassBase;

  TPUShardingIdentificationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUShardingIdentificationPassBase(const TPUShardingIdentificationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  TPUShardingIdentificationPassBase& operator=(const TPUShardingIdentificationPassBase &) = delete;
  TPUShardingIdentificationPassBase(TPUShardingIdentificationPassBase &&) = delete;
  TPUShardingIdentificationPassBase& operator=(TPUShardingIdentificationPassBase &&) = delete;
  ~TPUShardingIdentificationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-sharding-identification");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-sharding-identification"; }

  ::llvm::StringRef getDescription() const override { return "Identifies and handles inputs/outputs of TPU computation that is sharded across logical cores."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUShardingIdentificationPass");
  }
  ::llvm::StringRef getName() const override { return "TPUShardingIdentificationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUShardingIdentificationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TPUSHARDINGIDENTIFICATIONPASS
#endif // GEN_PASS_DEF_TPUSHARDINGIDENTIFICATIONPASS

//===----------------------------------------------------------------------===//
// TPUValidateInputsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TPUVALIDATEINPUTSPASS
#undef GEN_PASS_DECL_TPUVALIDATEINPUTSPASS
#endif // GEN_PASS_DECL_TPUVALIDATEINPUTSPASS
#ifdef GEN_PASS_DEF_TPUVALIDATEINPUTSPASS
namespace impl {

template <typename DerivedT>
class TPUValidateInputsPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = TPUValidateInputsPassBase;

  TPUValidateInputsPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUValidateInputsPassBase(const TPUValidateInputsPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  TPUValidateInputsPassBase& operator=(const TPUValidateInputsPassBase &) = delete;
  TPUValidateInputsPassBase(TPUValidateInputsPassBase &&) = delete;
  TPUValidateInputsPassBase& operator=(TPUValidateInputsPassBase &&) = delete;
  ~TPUValidateInputsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-validate-inputs");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-validate-inputs"; }

  ::llvm::StringRef getDescription() const override { return "Validates inputs to the TPU TF/XLA bridge"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUValidateInputsPass");
  }
  ::llvm::StringRef getName() const override { return "TPUValidateInputsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUValidateInputsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TPUVALIDATEINPUTSPASS
#endif // GEN_PASS_DEF_TPUVALIDATEINPUTSPASS

//===----------------------------------------------------------------------===//
// TPUValidateSessionInputsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TPUVALIDATESESSIONINPUTSPASS
#undef GEN_PASS_DECL_TPUVALIDATESESSIONINPUTSPASS
#endif // GEN_PASS_DECL_TPUVALIDATESESSIONINPUTSPASS
#ifdef GEN_PASS_DEF_TPUVALIDATESESSIONINPUTSPASS
namespace impl {

template <typename DerivedT>
class TPUValidateSessionInputsPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = TPUValidateSessionInputsPassBase;

  TPUValidateSessionInputsPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUValidateSessionInputsPassBase(const TPUValidateSessionInputsPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  TPUValidateSessionInputsPassBase& operator=(const TPUValidateSessionInputsPassBase &) = delete;
  TPUValidateSessionInputsPassBase(TPUValidateSessionInputsPassBase &&) = delete;
  TPUValidateSessionInputsPassBase& operator=(TPUValidateSessionInputsPassBase &&) = delete;
  ~TPUValidateSessionInputsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-validate-session-inputs");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-validate-session-inputs"; }

  ::llvm::StringRef getDescription() const override { return "Validates inputs to the TPU TF/XLA bridge in session api"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUValidateSessionInputsPass");
  }
  ::llvm::StringRef getName() const override { return "TPUValidateSessionInputsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUValidateSessionInputsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TPUVALIDATESESSIONINPUTSPASS
#endif // GEN_PASS_DEF_TPUVALIDATESESSIONINPUTSPASS

//===----------------------------------------------------------------------===//
// VerifyClusteringPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VERIFYCLUSTERINGPASS
#undef GEN_PASS_DECL_VERIFYCLUSTERINGPASS
#endif // GEN_PASS_DECL_VERIFYCLUSTERINGPASS
#ifdef GEN_PASS_DEF_VERIFYCLUSTERINGPASS
namespace impl {

template <typename DerivedT>
class VerifyClusteringPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VerifyClusteringPassBase;

  VerifyClusteringPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifyClusteringPassBase(const VerifyClusteringPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  VerifyClusteringPassBase& operator=(const VerifyClusteringPassBase &) = delete;
  VerifyClusteringPassBase(VerifyClusteringPassBase &&) = delete;
  VerifyClusteringPassBase& operator=(VerifyClusteringPassBase &&) = delete;
  ~VerifyClusteringPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("verify-clustering-pass");
  }
  ::llvm::StringRef getArgument() const override { return "verify-clustering-pass"; }

  ::llvm::StringRef getDescription() const override { return "Verify that the Bridge output is correct and errors if verification fails."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifyClusteringPass");
  }
  ::llvm::StringRef getName() const override { return "VerifyClusteringPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VerifyClusteringPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_VERIFYCLUSTERINGPASS
#endif // GEN_PASS_DEF_VERIFYCLUSTERINGPASS

//===----------------------------------------------------------------------===//
// XlaBroadcastPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_XLABROADCASTPASS
#undef GEN_PASS_DECL_XLABROADCASTPASS
#endif // GEN_PASS_DECL_XLABROADCASTPASS
#ifdef GEN_PASS_DEF_XLABROADCASTPASS
namespace impl {

template <typename DerivedT>
class XlaBroadcastPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = XlaBroadcastPassBase;

  XlaBroadcastPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  XlaBroadcastPassBase(const XlaBroadcastPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  XlaBroadcastPassBase& operator=(const XlaBroadcastPassBase &) = delete;
  XlaBroadcastPassBase(XlaBroadcastPassBase &&) = delete;
  XlaBroadcastPassBase& operator=(XlaBroadcastPassBase &&) = delete;
  ~XlaBroadcastPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-xla-broadcast");
  }
  ::llvm::StringRef getArgument() const override { return "tf-xla-broadcast"; }

  ::llvm::StringRef getDescription() const override { return "Moves a broadcast from host into XLA, encoded as XlaAllReduce"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("XlaBroadcastPass");
  }
  ::llvm::StringRef getName() const override { return "XlaBroadcastPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(XlaBroadcastPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_XLABROADCASTPASS
#endif // GEN_PASS_DEF_XLABROADCASTPASS

//===----------------------------------------------------------------------===//
// XlaClusterFormationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_XLACLUSTERFORMATIONPASS
#undef GEN_PASS_DECL_XLACLUSTERFORMATIONPASS
#endif // GEN_PASS_DECL_XLACLUSTERFORMATIONPASS
#ifdef GEN_PASS_DEF_XLACLUSTERFORMATIONPASS
namespace impl {

template <typename DerivedT>
class XlaClusterFormationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = XlaClusterFormationPassBase;

  XlaClusterFormationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  XlaClusterFormationPassBase(const XlaClusterFormationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  XlaClusterFormationPassBase& operator=(const XlaClusterFormationPassBase &) = delete;
  XlaClusterFormationPassBase(XlaClusterFormationPassBase &&) = delete;
  XlaClusterFormationPassBase& operator=(XlaClusterFormationPassBase &&) = delete;
  ~XlaClusterFormationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-xla-cluster-formation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-xla-cluster-formation"; }

  ::llvm::StringRef getDescription() const override { return "Encapsulate partitioned calls within a Cluster op"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("XlaClusterFormationPass");
  }
  ::llvm::StringRef getName() const override { return "XlaClusterFormationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(XlaClusterFormationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_XLACLUSTERFORMATIONPASS
#endif // GEN_PASS_DEF_XLACLUSTERFORMATIONPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// ExtractHeadTailOutsideCompilationPass Registration
//===----------------------------------------------------------------------===//

inline void registerExtractHeadTailOutsideCompilationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateExtractHeadTailOutsideCompilationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerExtractHeadTailOutsideCompilationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateExtractHeadTailOutsideCompilationPass();
  });
}

//===----------------------------------------------------------------------===//
// ExtractOutsideCompilationPass Registration
//===----------------------------------------------------------------------===//

inline void registerExtractOutsideCompilationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateExtractOutsideCompilationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerExtractOutsideCompilationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateExtractOutsideCompilationPass();
  });
}

//===----------------------------------------------------------------------===//
// HoistBroadcastReadPass Registration
//===----------------------------------------------------------------------===//

inline void registerHoistBroadcastReadPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateHoistBroadcastReadPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerHoistBroadcastReadPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateHoistBroadcastReadPass();
  });
}

//===----------------------------------------------------------------------===//
// MarkOpsForOutsideCompilationPass Registration
//===----------------------------------------------------------------------===//

inline void registerMarkOpsForOutsideCompilationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateMarkOpsForOutsideCompilationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerMarkOpsForOutsideCompilationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateMarkOpsForOutsideCompilationPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUClusterFormationPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUClusterFormationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateTPUClusterFormationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTPUClusterFormationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateTPUClusterFormationPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUShardingIdentificationPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUShardingIdentificationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateTPUShardingIdentificationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTPUShardingIdentificationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateTPUShardingIdentificationPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUValidateInputsPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUValidateInputsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateTPUValidateInputsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTPUValidateInputsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateTPUValidateInputsPass();
  });
}

//===----------------------------------------------------------------------===//
// TPUValidateSessionInputsPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUValidateSessionInputsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateTPUValidateSessionInputsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTPUValidateSessionInputsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateTPUValidateSessionInputsPass();
  });
}

//===----------------------------------------------------------------------===//
// VerifyClusteringPass Registration
//===----------------------------------------------------------------------===//

inline void registerVerifyClusteringPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateVerifyClusteringPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerVerifyClusteringPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateVerifyClusteringPass();
  });
}

//===----------------------------------------------------------------------===//
// XlaBroadcastPass Registration
//===----------------------------------------------------------------------===//

inline void registerXlaBroadcastPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateXlaBroadcastPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerXlaBroadcastPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateXlaBroadcastPass();
  });
}

//===----------------------------------------------------------------------===//
// XlaClusterFormationPass Registration
//===----------------------------------------------------------------------===//

inline void registerXlaClusterFormationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateXlaClusterFormationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerXlaClusterFormationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return tensorflow::tf2xla::internal::CreateXlaClusterFormationPass();
  });
}

//===----------------------------------------------------------------------===//
// TFXLABridgeClustering Registration
//===----------------------------------------------------------------------===//

inline void registerTFXLABridgeClusteringPasses() {
  registerExtractHeadTailOutsideCompilationPass();
  registerExtractOutsideCompilationPass();
  registerHoistBroadcastReadPass();
  registerMarkOpsForOutsideCompilationPass();
  registerTPUClusterFormationPass();
  registerTPUShardingIdentificationPass();
  registerTPUValidateInputsPass();
  registerTPUValidateSessionInputsPass();
  registerVerifyClusteringPass();
  registerXlaBroadcastPass();
  registerXlaClusterFormationPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class ExtractHeadTailOutsideCompilationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ExtractHeadTailOutsideCompilationPassBase;

  ExtractHeadTailOutsideCompilationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ExtractHeadTailOutsideCompilationPassBase(const ExtractHeadTailOutsideCompilationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ExtractHeadTailOutsideCompilationPassBase& operator=(const ExtractHeadTailOutsideCompilationPassBase &) = delete;
  ExtractHeadTailOutsideCompilationPassBase(ExtractHeadTailOutsideCompilationPassBase &&) = delete;
  ExtractHeadTailOutsideCompilationPassBase& operator=(ExtractHeadTailOutsideCompilationPassBase &&) = delete;
  ~ExtractHeadTailOutsideCompilationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-extract-head-tail-outside-compilation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-extract-head-tail-outside-compilation"; }

  ::llvm::StringRef getDescription() const override { return "Extracts head or tail outside compilation to separate host launches before/after device cluster."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExtractHeadTailOutsideCompilationPass");
  }
  ::llvm::StringRef getName() const override { return "ExtractHeadTailOutsideCompilationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ExtractHeadTailOutsideCompilationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ExtractOutsideCompilationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ExtractOutsideCompilationPassBase;

  ExtractOutsideCompilationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ExtractOutsideCompilationPassBase(const ExtractOutsideCompilationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ExtractOutsideCompilationPassBase& operator=(const ExtractOutsideCompilationPassBase &) = delete;
  ExtractOutsideCompilationPassBase(ExtractOutsideCompilationPassBase &&) = delete;
  ExtractOutsideCompilationPassBase& operator=(ExtractOutsideCompilationPassBase &&) = delete;
  ~ExtractOutsideCompilationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-extract-outside-compilation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-extract-outside-compilation"; }

  ::llvm::StringRef getDescription() const override { return "Extracts device outside compilation computation to a separate tf_device.parallel_execute region."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ExtractOutsideCompilationPass");
  }
  ::llvm::StringRef getName() const override { return "ExtractOutsideCompilationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ExtractOutsideCompilationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class HoistBroadcastReadPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = HoistBroadcastReadPassBase;

  HoistBroadcastReadPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  HoistBroadcastReadPassBase(const HoistBroadcastReadPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  HoistBroadcastReadPassBase& operator=(const HoistBroadcastReadPassBase &) = delete;
  HoistBroadcastReadPassBase(HoistBroadcastReadPassBase &&) = delete;
  HoistBroadcastReadPassBase& operator=(HoistBroadcastReadPassBase &&) = delete;
  ~HoistBroadcastReadPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-hoist-broadcast-read");
  }
  ::llvm::StringRef getArgument() const override { return "tf-hoist-broadcast-read"; }

  ::llvm::StringRef getDescription() const override { return "Hoist reads out of a replicate that are on a resource that is broacast to all replicas."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("HoistBroadcastReadPass");
  }
  ::llvm::StringRef getName() const override { return "HoistBroadcastReadPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(HoistBroadcastReadPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class MarkOpsForOutsideCompilationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = MarkOpsForOutsideCompilationPassBase;

  MarkOpsForOutsideCompilationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  MarkOpsForOutsideCompilationPassBase(const MarkOpsForOutsideCompilationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  MarkOpsForOutsideCompilationPassBase& operator=(const MarkOpsForOutsideCompilationPassBase &) = delete;
  MarkOpsForOutsideCompilationPassBase(MarkOpsForOutsideCompilationPassBase &&) = delete;
  MarkOpsForOutsideCompilationPassBase& operator=(MarkOpsForOutsideCompilationPassBase &&) = delete;
  ~MarkOpsForOutsideCompilationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-mark-ops-for-outside-compilation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-mark-ops-for-outside-compilation"; }

  ::llvm::StringRef getDescription() const override { return "Marks ops in device cluster for outside compilation if they are unsupported on device."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("MarkOpsForOutsideCompilationPass");
  }
  ::llvm::StringRef getName() const override { return "MarkOpsForOutsideCompilationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(MarkOpsForOutsideCompilationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TPUClusterFormationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUClusterFormationPassBase;

  TPUClusterFormationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUClusterFormationPassBase(const TPUClusterFormationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  TPUClusterFormationPassBase& operator=(const TPUClusterFormationPassBase &) = delete;
  TPUClusterFormationPassBase(TPUClusterFormationPassBase &&) = delete;
  TPUClusterFormationPassBase& operator=(TPUClusterFormationPassBase &&) = delete;
  ~TPUClusterFormationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-cluster-formation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-cluster-formation"; }

  ::llvm::StringRef getDescription() const override { return "Forms clusters from operations assigned to the same TPU computation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUClusterFormationPass");
  }
  ::llvm::StringRef getName() const override { return "TPUClusterFormationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUClusterFormationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TPUShardingIdentificationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUShardingIdentificationPassBase;

  TPUShardingIdentificationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUShardingIdentificationPassBase(const TPUShardingIdentificationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  TPUShardingIdentificationPassBase& operator=(const TPUShardingIdentificationPassBase &) = delete;
  TPUShardingIdentificationPassBase(TPUShardingIdentificationPassBase &&) = delete;
  TPUShardingIdentificationPassBase& operator=(TPUShardingIdentificationPassBase &&) = delete;
  ~TPUShardingIdentificationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-sharding-identification");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-sharding-identification"; }

  ::llvm::StringRef getDescription() const override { return "Identifies and handles inputs/outputs of TPU computation that is sharded across logical cores."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUShardingIdentificationPass");
  }
  ::llvm::StringRef getName() const override { return "TPUShardingIdentificationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUShardingIdentificationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TPUValidateInputsPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = TPUValidateInputsPassBase;

  TPUValidateInputsPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUValidateInputsPassBase(const TPUValidateInputsPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  TPUValidateInputsPassBase& operator=(const TPUValidateInputsPassBase &) = delete;
  TPUValidateInputsPassBase(TPUValidateInputsPassBase &&) = delete;
  TPUValidateInputsPassBase& operator=(TPUValidateInputsPassBase &&) = delete;
  ~TPUValidateInputsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-validate-inputs");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-validate-inputs"; }

  ::llvm::StringRef getDescription() const override { return "Validates inputs to the TPU TF/XLA bridge"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUValidateInputsPass");
  }
  ::llvm::StringRef getName() const override { return "TPUValidateInputsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUValidateInputsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TPUValidateSessionInputsPassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = TPUValidateSessionInputsPassBase;

  TPUValidateSessionInputsPassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUValidateSessionInputsPassBase(const TPUValidateSessionInputsPassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  TPUValidateSessionInputsPassBase& operator=(const TPUValidateSessionInputsPassBase &) = delete;
  TPUValidateSessionInputsPassBase(TPUValidateSessionInputsPassBase &&) = delete;
  TPUValidateSessionInputsPassBase& operator=(TPUValidateSessionInputsPassBase &&) = delete;
  ~TPUValidateSessionInputsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-validate-session-inputs");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-validate-session-inputs"; }

  ::llvm::StringRef getDescription() const override { return "Validates inputs to the TPU TF/XLA bridge in session api"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUValidateSessionInputsPass");
  }
  ::llvm::StringRef getName() const override { return "TPUValidateSessionInputsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUValidateSessionInputsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class VerifyClusteringPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VerifyClusteringPassBase;

  VerifyClusteringPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifyClusteringPassBase(const VerifyClusteringPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  VerifyClusteringPassBase& operator=(const VerifyClusteringPassBase &) = delete;
  VerifyClusteringPassBase(VerifyClusteringPassBase &&) = delete;
  VerifyClusteringPassBase& operator=(VerifyClusteringPassBase &&) = delete;
  ~VerifyClusteringPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("verify-clustering-pass");
  }
  ::llvm::StringRef getArgument() const override { return "verify-clustering-pass"; }

  ::llvm::StringRef getDescription() const override { return "Verify that the Bridge output is correct and errors if verification fails."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifyClusteringPass");
  }
  ::llvm::StringRef getName() const override { return "VerifyClusteringPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VerifyClusteringPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class XlaBroadcastPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = XlaBroadcastPassBase;

  XlaBroadcastPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  XlaBroadcastPassBase(const XlaBroadcastPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  XlaBroadcastPassBase& operator=(const XlaBroadcastPassBase &) = delete;
  XlaBroadcastPassBase(XlaBroadcastPassBase &&) = delete;
  XlaBroadcastPassBase& operator=(XlaBroadcastPassBase &&) = delete;
  ~XlaBroadcastPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-xla-broadcast");
  }
  ::llvm::StringRef getArgument() const override { return "tf-xla-broadcast"; }

  ::llvm::StringRef getDescription() const override { return "Moves a broadcast from host into XLA, encoded as XlaAllReduce"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("XlaBroadcastPass");
  }
  ::llvm::StringRef getName() const override { return "XlaBroadcastPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(XlaBroadcastPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class XlaClusterFormationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = XlaClusterFormationPassBase;

  XlaClusterFormationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  XlaClusterFormationPassBase(const XlaClusterFormationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  XlaClusterFormationPassBase& operator=(const XlaClusterFormationPassBase &) = delete;
  XlaClusterFormationPassBase(XlaClusterFormationPassBase &&) = delete;
  XlaClusterFormationPassBase& operator=(XlaClusterFormationPassBase &&) = delete;
  ~XlaClusterFormationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-xla-cluster-formation");
  }
  ::llvm::StringRef getArgument() const override { return "tf-xla-cluster-formation"; }

  ::llvm::StringRef getDescription() const override { return "Encapsulate partitioned calls within a Cluster op"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("XlaClusterFormationPass");
  }
  ::llvm::StringRef getName() const override { return "XlaClusterFormationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(XlaClusterFormationPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
