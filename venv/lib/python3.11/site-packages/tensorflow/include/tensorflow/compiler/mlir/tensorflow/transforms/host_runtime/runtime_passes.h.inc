/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_TPUMERGEVARIABLESWITHEXECUTEPASS
#define GEN_PASS_DECL_TPUREWRITEPASS
#define GEN_PASS_DECL_TPUVA<PERSON>ABLERUNTIMEREFORMATTINGPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// TPUMergeVariablesWithExecutePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PA<PERSON>_<PERSON>CL_TPUMERGEVARIABLESWITHEXECUTEPASS
#undef GEN_PASS_DECL_TPUMERGEVARIABLESWITHEXECUTEPASS
#endif // GEN_PASS_DECL_TPUMERGEVARIABLESWITHEXECUTEPASS
#ifdef GEN_PASS_DEF_TPUMERGEVARIABLESWITHEXECUTEPASS
namespace impl {

template <typename DerivedT>
class TPUMergeVariablesWithExecutePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUMergeVariablesWithExecutePassBase;

  TPUMergeVariablesWithExecutePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUMergeVariablesWithExecutePassBase(const TPUMergeVariablesWithExecutePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  TPUMergeVariablesWithExecutePassBase& operator=(const TPUMergeVariablesWithExecutePassBase &) = delete;
  TPUMergeVariablesWithExecutePassBase(TPUMergeVariablesWithExecutePassBase &&) = delete;
  TPUMergeVariablesWithExecutePassBase& operator=(TPUMergeVariablesWithExecutePassBase &&) = delete;
  ~TPUMergeVariablesWithExecutePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-merge-variables-with-execute");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-merge-variables-with-execute"; }

  ::llvm::StringRef getDescription() const override { return "Merges device variable reads and updates into TPU execute ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUMergeVariablesWithExecutePass");
  }
  ::llvm::StringRef getName() const override { return "TPUMergeVariablesWithExecutePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUMergeVariablesWithExecutePassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TPUMERGEVARIABLESWITHEXECUTEPASS
#endif // GEN_PASS_DEF_TPUMERGEVARIABLESWITHEXECUTEPASS

//===----------------------------------------------------------------------===//
// TPURewritePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TPUREWRITEPASS
struct TPURewritePassOptions {
  bool tpu_compile_metadata_debug_ = false;
};
#undef GEN_PASS_DECL_TPUREWRITEPASS
#endif // GEN_PASS_DECL_TPUREWRITEPASS
#ifdef GEN_PASS_DEF_TPUREWRITEPASS
namespace impl {

template <typename DerivedT>
class TPURewritePassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = TPURewritePassBase;

  TPURewritePassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPURewritePassBase(const TPURewritePassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  TPURewritePassBase& operator=(const TPURewritePassBase &) = delete;
  TPURewritePassBase(TPURewritePassBase &&) = delete;
  TPURewritePassBase& operator=(TPURewritePassBase &&) = delete;
  ~TPURewritePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Rewrites a `tf_device.cluster_func` on TPUs into TPU runtime operations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPURewritePass");
  }
  ::llvm::StringRef getName() const override { return "TPURewritePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::mhlo::MhloDialect>();
    registry.insert<mlir::tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPURewritePassBase<DerivedT>)

  TPURewritePassBase(TPURewritePassOptions options) : TPURewritePassBase() {
    tpu_compile_metadata_debug_ = std::move(options.tpu_compile_metadata_debug_);
  }
protected:
  ::mlir::Pass::Option<bool> tpu_compile_metadata_debug_{*this, "tpu-compile-metadata-debug", ::llvm::cl::desc("Whether to serialize TPUCompileMetadataProto metadata in 'tf._TPUCompileMlir' op as a proto debug string"), ::llvm::cl::init(false)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TPUREWRITEPASS
#endif // GEN_PASS_DEF_TPUREWRITEPASS

//===----------------------------------------------------------------------===//
// TPUVariableRuntimeReformattingPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_TPUVARIABLERUNTIMEREFORMATTINGPASS
#undef GEN_PASS_DECL_TPUVARIABLERUNTIMEREFORMATTINGPASS
#endif // GEN_PASS_DECL_TPUVARIABLERUNTIMEREFORMATTINGPASS
#ifdef GEN_PASS_DEF_TPUVARIABLERUNTIMEREFORMATTINGPASS
namespace impl {

template <typename DerivedT>
class TPUVariableRuntimeReformattingPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUVariableRuntimeReformattingPassBase;

  TPUVariableRuntimeReformattingPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUVariableRuntimeReformattingPassBase(const TPUVariableRuntimeReformattingPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  TPUVariableRuntimeReformattingPassBase& operator=(const TPUVariableRuntimeReformattingPassBase &) = delete;
  TPUVariableRuntimeReformattingPassBase(TPUVariableRuntimeReformattingPassBase &&) = delete;
  TPUVariableRuntimeReformattingPassBase& operator=(TPUVariableRuntimeReformattingPassBase &&) = delete;
  ~TPUVariableRuntimeReformattingPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-variable-runtime-reformatting");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-variable-runtime-reformatting"; }

  ::llvm::StringRef getDescription() const override { return "Adds device variable formatting op to allow compilation-guided variable formatting."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUVariableRuntimeReformattingPass");
  }
  ::llvm::StringRef getName() const override { return "TPUVariableRuntimeReformattingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUVariableRuntimeReformattingPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_TPUVARIABLERUNTIMEREFORMATTINGPASS
#endif // GEN_PASS_DEF_TPUVARIABLERUNTIMEREFORMATTINGPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// TPUMergeVariablesWithExecutePass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUMergeVariablesWithExecutePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUMergeVariablesWithExecutePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTPUMergeVariablesWithExecutePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUMergeVariablesWithExecutePass();
  });
}

//===----------------------------------------------------------------------===//
// TPURewritePass Registration
//===----------------------------------------------------------------------===//

inline void registerTPURewritePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::TFTPU::CreateTPURewritePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTPURewritePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::TFTPU::CreateTPURewritePass();
  });
}

//===----------------------------------------------------------------------===//
// TPUVariableRuntimeReformattingPass Registration
//===----------------------------------------------------------------------===//

inline void registerTPUVariableRuntimeReformattingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUVariableRuntimeReformattingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerTPUVariableRuntimeReformattingPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFTPU::CreateTPUVariableRuntimeReformattingPass();
  });
}

//===----------------------------------------------------------------------===//
// RuntimeLowering Registration
//===----------------------------------------------------------------------===//

inline void registerRuntimeLoweringPasses() {
  registerTPUMergeVariablesWithExecutePass();
  registerTPURewritePass();
  registerTPUVariableRuntimeReformattingPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class TPUMergeVariablesWithExecutePassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUMergeVariablesWithExecutePassBase;

  TPUMergeVariablesWithExecutePassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUMergeVariablesWithExecutePassBase(const TPUMergeVariablesWithExecutePassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  TPUMergeVariablesWithExecutePassBase& operator=(const TPUMergeVariablesWithExecutePassBase &) = delete;
  TPUMergeVariablesWithExecutePassBase(TPUMergeVariablesWithExecutePassBase &&) = delete;
  TPUMergeVariablesWithExecutePassBase& operator=(TPUMergeVariablesWithExecutePassBase &&) = delete;
  ~TPUMergeVariablesWithExecutePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-merge-variables-with-execute");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-merge-variables-with-execute"; }

  ::llvm::StringRef getDescription() const override { return "Merges device variable reads and updates into TPU execute ops"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUMergeVariablesWithExecutePass");
  }
  ::llvm::StringRef getName() const override { return "TPUMergeVariablesWithExecutePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUMergeVariablesWithExecutePassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class TPURewritePassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = TPURewritePassBase;

  TPURewritePassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPURewritePassBase(const TPURewritePassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  TPURewritePassBase& operator=(const TPURewritePassBase &) = delete;
  TPURewritePassBase(TPURewritePassBase &&) = delete;
  TPURewritePassBase& operator=(TPURewritePassBase &&) = delete;
  ~TPURewritePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Rewrites a `tf_device.cluster_func` on TPUs into TPU runtime operations."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPURewritePass");
  }
  ::llvm::StringRef getName() const override { return "TPURewritePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::mhlo::MhloDialect>();
    registry.insert<mlir::tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPURewritePassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> tpu_compile_metadata_debug_{*this, "tpu-compile-metadata-debug", ::llvm::cl::desc("Whether to serialize TPUCompileMetadataProto metadata in 'tf._TPUCompileMlir' op as a proto debug string"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class TPUVariableRuntimeReformattingPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = TPUVariableRuntimeReformattingPassBase;

  TPUVariableRuntimeReformattingPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  TPUVariableRuntimeReformattingPassBase(const TPUVariableRuntimeReformattingPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  TPUVariableRuntimeReformattingPassBase& operator=(const TPUVariableRuntimeReformattingPassBase &) = delete;
  TPUVariableRuntimeReformattingPassBase(TPUVariableRuntimeReformattingPassBase &&) = delete;
  TPUVariableRuntimeReformattingPassBase& operator=(TPUVariableRuntimeReformattingPassBase &&) = delete;
  ~TPUVariableRuntimeReformattingPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-tpu-variable-runtime-reformatting");
  }
  ::llvm::StringRef getArgument() const override { return "tf-tpu-variable-runtime-reformatting"; }

  ::llvm::StringRef getDescription() const override { return "Adds device variable formatting op to allow compilation-guided variable formatting."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("TPUVariableRuntimeReformattingPass");
  }
  ::llvm::StringRef getName() const override { return "TPUVariableRuntimeReformattingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(TPUVariableRuntimeReformattingPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
