/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Op Declarations                                                            *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|* From: tf_ops.td                                                            *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

namespace mlir {
namespace TF {
class _ArrayToListOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _EagerConstOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _FusedBatchNormExOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _FusedConv2DOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _FusedMatMulOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _HostRecvOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _HostSendOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _InternalTestMustExecuteTrait_;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _InternalTestNonResourceValueSideEffects_;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _ListToArrayOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _RecvOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _SendOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _TPUCompileMlirOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _TPUDeviceOrdinalPlaceholderOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _UnaryOpsCompositionOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _XlaCompileMlirPlaceholderProgramKeyOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _XlaCompileOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _XlaHostComputeMlirOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _XlaRecvAtHostOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _XlaRecvAtHostV2Op;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _XlaRunOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _XlaSendFromHostOp;
} // namespace TF
} // namespace mlir
namespace mlir {
namespace TF {
class _XlaSendFromHostV2Op;
} // namespace TF
} // namespace mlir
#ifdef GET_OP_CLASSES
#undef GET_OP_CLASSES

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_ArrayToListOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _ArrayToListOpGenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  _ArrayToListOpGenericAdaptorBase(::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._ArrayToList", odsAttrs.getContext());
  }

  _ArrayToListOpGenericAdaptorBase(::mlir::Operation *op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), odsRegions(op->getRegions()) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

};
} // namespace detail
template <typename RangeT>
class _ArrayToListOpGenericAdaptor : public detail::_ArrayToListOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_ArrayToListOpGenericAdaptorBase;
public:
  _ArrayToListOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _ArrayToListOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _ArrayToListOpGenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  _ArrayToListOpGenericAdaptor(RangeT values, const _ArrayToListOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _ArrayToListOp, typename = std::enable_if_t<std::is_same_v<LateInst, _ArrayToListOp>>>
  _ArrayToListOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getInput() {
    return getODSOperands(0);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _ArrayToListOpAdaptor : public _ArrayToListOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _ArrayToListOpGenericAdaptor::_ArrayToListOpGenericAdaptor;
  _ArrayToListOpAdaptor(_ArrayToListOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _ArrayToListOp : public ::mlir::Op<_ArrayToListOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::VariadicResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::OpInvariants, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _ArrayToListOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _ArrayToListOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("N"), ::llvm::StringRef("T"), ::llvm::StringRef("out_types")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getNAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getNAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getOutTypesAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getOutTypesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._ArrayToList");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getInput() {
    return getODSOperands(0);
  }

  ::mlir::MutableOperandRange getInputMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOutput() {
    return getODSResults(0);
  }

  size_t getN();
  ::mlir::Type getT();
  mlir::ResultElementTypeRange getOutTypes();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 3 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_ArrayToListOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_EagerConstOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _EagerConstOpGenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  _EagerConstOpGenericAdaptorBase(::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._EagerConst", odsAttrs.getContext());
  }

  _EagerConstOpGenericAdaptorBase(::mlir::Operation *op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), odsRegions(op->getRegions()) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

};
} // namespace detail
template <typename RangeT>
class _EagerConstOpGenericAdaptor : public detail::_EagerConstOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_EagerConstOpGenericAdaptorBase;
public:
  _EagerConstOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _EagerConstOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _EagerConstOpGenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  _EagerConstOpGenericAdaptor(RangeT values, const _EagerConstOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _EagerConstOp, typename = std::enable_if_t<std::is_same_v<LateInst, _EagerConstOp>>>
  _EagerConstOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getInput() {
    return (*getODSOperands(0).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _EagerConstOpAdaptor : public _EagerConstOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _EagerConstOpGenericAdaptor::_EagerConstOpGenericAdaptor;
  _EagerConstOpAdaptor(_EagerConstOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _EagerConstOp : public ::mlir::Op<_EagerConstOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::TensorType>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::OneOperand, ::mlir::OpTrait::OpInvariants, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _EagerConstOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _EagerConstOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._EagerConst");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getInput() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::OpOperand &getInputMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getOutput() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  ::mlir::Type getT();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type output, ::mlir::Value input);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value input);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_EagerConstOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_FusedBatchNormExOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _FusedBatchNormExOpGenericAdaptorBase {
public:
  struct Properties {
    using activation_modeTy = ::mlir::StringAttr;
    activation_modeTy activation_mode;

    auto getActivationMode() {
      auto &propStorage = this->activation_mode;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setActivationMode(const ::mlir::StringAttr &propValue) {
      this->activation_mode = propValue;
    }
    using data_formatTy = ::mlir::StringAttr;
    data_formatTy data_format;

    auto getDataFormat() {
      auto &propStorage = this->data_format;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setDataFormat(const ::mlir::StringAttr &propValue) {
      this->data_format = propValue;
    }
    using epsilonTy = ::mlir::FloatAttr;
    epsilonTy epsilon;

    auto getEpsilon() {
      auto &propStorage = this->epsilon;
      return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(propStorage);
    }
    void setEpsilon(const ::mlir::FloatAttr &propValue) {
      this->epsilon = propValue;
    }
    using exponential_avg_factorTy = ::mlir::FloatAttr;
    exponential_avg_factorTy exponential_avg_factor;

    auto getExponentialAvgFactor() {
      auto &propStorage = this->exponential_avg_factor;
      return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(propStorage);
    }
    void setExponentialAvgFactor(const ::mlir::FloatAttr &propValue) {
      this->exponential_avg_factor = propValue;
    }
    using is_trainingTy = ::mlir::BoolAttr;
    is_trainingTy is_training;

    auto getIsTraining() {
      auto &propStorage = this->is_training;
      return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(propStorage);
    }
    void setIsTraining(const ::mlir::BoolAttr &propValue) {
      this->is_training = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.activation_mode == this->activation_mode &&
        rhs.data_format == this->data_format &&
        rhs.epsilon == this->epsilon &&
        rhs.exponential_avg_factor == this->exponential_avg_factor &&
        rhs.is_training == this->is_training &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _FusedBatchNormExOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._FusedBatchNormEx", odsAttrs.getContext());
  }

  _FusedBatchNormExOpGenericAdaptorBase(_FusedBatchNormExOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::FloatAttr getEpsilonAttr();
  ::llvm::APFloat getEpsilon();
  ::mlir::FloatAttr getExponentialAvgFactorAttr();
  ::llvm::APFloat getExponentialAvgFactor();
  ::mlir::StringAttr getActivationModeAttr();
  ::llvm::StringRef getActivationMode();
  ::mlir::StringAttr getDataFormatAttr();
  ::llvm::StringRef getDataFormat();
  ::mlir::BoolAttr getIsTrainingAttr();
  bool getIsTraining();
};
} // namespace detail
template <typename RangeT>
class _FusedBatchNormExOpGenericAdaptor : public detail::_FusedBatchNormExOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_FusedBatchNormExOpGenericAdaptorBase;
public:
  _FusedBatchNormExOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _FusedBatchNormExOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _FusedBatchNormExOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _FusedBatchNormExOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _FusedBatchNormExOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _FusedBatchNormExOpGenericAdaptor(RangeT values, const _FusedBatchNormExOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _FusedBatchNormExOp, typename = std::enable_if_t<std::is_same_v<LateInst, _FusedBatchNormExOp>>>
  _FusedBatchNormExOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getX() {
    return (*getODSOperands(0).begin());
  }

  ValueT getScale() {
    return (*getODSOperands(1).begin());
  }

  ValueT getOffset() {
    return (*getODSOperands(2).begin());
  }

  ValueT getMean() {
    return (*getODSOperands(3).begin());
  }

  ValueT getVariance() {
    return (*getODSOperands(4).begin());
  }

  RangeT getSideInput() {
    return getODSOperands(5);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _FusedBatchNormExOpAdaptor : public _FusedBatchNormExOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _FusedBatchNormExOpGenericAdaptor::_FusedBatchNormExOpGenericAdaptor;
  _FusedBatchNormExOpAdaptor(_FusedBatchNormExOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _FusedBatchNormExOp : public ::mlir::Op<_FusedBatchNormExOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::NResults<6>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<5>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _FusedBatchNormExOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _FusedBatchNormExOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T"), ::llvm::StringRef("U"), ::llvm::StringRef("activation_mode"), ::llvm::StringRef("data_format"), ::llvm::StringRef("epsilon"), ::llvm::StringRef("exponential_avg_factor"), ::llvm::StringRef("is_training"), ::llvm::StringRef("num_side_inputs")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getUAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getUAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getActivationModeAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getActivationModeAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getDataFormatAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getDataFormatAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getEpsilonAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getEpsilonAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getExponentialAvgFactorAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getExponentialAvgFactorAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  ::mlir::StringAttr getIsTrainingAttrName() {
    return getAttributeNameForIndex(6);
  }

  static ::mlir::StringAttr getIsTrainingAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 6);
  }

  ::mlir::StringAttr getNumSideInputsAttrName() {
    return getAttributeNameForIndex(7);
  }

  static ::mlir::StringAttr getNumSideInputsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 7);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._FusedBatchNormEx");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getX() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getScale() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(1).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getOffset() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(2).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getMean() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(3).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getVariance() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(4).begin());
  }

  ::mlir::Operation::operand_range getSideInput() {
    return getODSOperands(5);
  }

  ::mlir::OpOperand &getXMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getScaleMutable() {
    auto range = getODSOperandIndexAndLength(1);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getOffsetMutable() {
    auto range = getODSOperandIndexAndLength(2);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getMeanMutable() {
    auto range = getODSOperandIndexAndLength(3);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getVarianceMutable() {
    auto range = getODSOperandIndexAndLength(4);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getSideInputMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getY() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getBatchMean() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(1).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getBatchVariance() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(2).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getReserveSpace_1() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(3).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getReserveSpace_2() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(4).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getReserveSpace_3() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(5).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::FloatAttr getEpsilonAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(getProperties().epsilon);
  }

  ::llvm::APFloat getEpsilon();
  ::mlir::FloatAttr getExponentialAvgFactorAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(getProperties().exponential_avg_factor);
  }

  ::llvm::APFloat getExponentialAvgFactor();
  ::mlir::StringAttr getActivationModeAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().activation_mode);
  }

  ::llvm::StringRef getActivationMode();
  ::mlir::StringAttr getDataFormatAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().data_format);
  }

  ::llvm::StringRef getDataFormat();
  ::mlir::BoolAttr getIsTrainingAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().is_training);
  }

  bool getIsTraining();
  size_t getNumSideInputs();
  ::mlir::Type getT();
  ::mlir::Type getU();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setEpsilonAttr(::mlir::FloatAttr attr) {
    getProperties().epsilon = attr;
  }

  void setEpsilon(::std::optional<::llvm::APFloat> attrValue);
  void setExponentialAvgFactorAttr(::mlir::FloatAttr attr) {
    getProperties().exponential_avg_factor = attr;
  }

  void setExponentialAvgFactor(::std::optional<::llvm::APFloat> attrValue);
  void setActivationModeAttr(::mlir::StringAttr attr) {
    getProperties().activation_mode = attr;
  }

  void setActivationMode(::std::optional<::llvm::StringRef> attrValue);
  void setDataFormatAttr(::mlir::StringAttr attr) {
    getProperties().data_format = attr;
  }

  void setDataFormat(::std::optional<::llvm::StringRef> attrValue);
  void setIsTrainingAttr(::mlir::BoolAttr attr) {
    getProperties().is_training = attr;
  }

  void setIsTraining(::std::optional<bool> attrValue);
  ::mlir::Attribute removeEpsilonAttr() {
      auto &attr = getProperties().epsilon;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeExponentialAvgFactorAttr() {
      auto &attr = getProperties().exponential_avg_factor;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeActivationModeAttr() {
      auto &attr = getProperties().activation_mode;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeDataFormatAttr() {
      auto &attr = getProperties().data_format;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeIsTrainingAttr() {
      auto &attr = getProperties().is_training;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type y, ::mlir::Type batch_mean, ::mlir::Type batch_variance, ::mlir::Type reserve_space_1, ::mlir::Type reserve_space_2, ::mlir::Type reserve_space_3, ::mlir::Value x, ::mlir::Value scale, ::mlir::Value offset, ::mlir::Value mean, ::mlir::Value variance, ::mlir::ValueRange side_input, /*optional*/::mlir::FloatAttr epsilon, /*optional*/::mlir::FloatAttr exponential_avg_factor, /*optional*/::mlir::StringAttr activation_mode, /*optional*/::mlir::StringAttr data_format = nullptr, /*optional*/::mlir::BoolAttr is_training = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value x, ::mlir::Value scale, ::mlir::Value offset, ::mlir::Value mean, ::mlir::Value variance, ::mlir::ValueRange side_input, /*optional*/::mlir::FloatAttr epsilon, /*optional*/::mlir::FloatAttr exponential_avg_factor, /*optional*/::mlir::StringAttr activation_mode, /*optional*/::mlir::StringAttr data_format = nullptr, /*optional*/::mlir::BoolAttr is_training = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type y, ::mlir::Type batch_mean, ::mlir::Type batch_variance, ::mlir::Type reserve_space_1, ::mlir::Type reserve_space_2, ::mlir::Type reserve_space_3, ::mlir::Value x, ::mlir::Value scale, ::mlir::Value offset, ::mlir::Value mean, ::mlir::Value variance, ::mlir::ValueRange side_input, /*optional*/::llvm::APFloat epsilon, /*optional*/::llvm::APFloat exponential_avg_factor, /*optional*/::llvm::StringRef activation_mode = "Identity", /*optional*/::llvm::StringRef data_format = "NHWC", /*optional*/bool is_training = true);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value x, ::mlir::Value scale, ::mlir::Value offset, ::mlir::Value mean, ::mlir::Value variance, ::mlir::ValueRange side_input, /*optional*/::llvm::APFloat epsilon, /*optional*/::llvm::APFloat exponential_avg_factor, /*optional*/::llvm::StringRef activation_mode = "Identity", /*optional*/::llvm::StringRef data_format = "NHWC", /*optional*/bool is_training = true);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 8 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_FusedBatchNormExOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_FusedConv2DOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _FusedConv2DOpGenericAdaptorBase {
public:
  struct Properties {
    using data_formatTy = ::mlir::StringAttr;
    data_formatTy data_format;

    auto getDataFormat() {
      auto &propStorage = this->data_format;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setDataFormat(const ::mlir::StringAttr &propValue) {
      this->data_format = propValue;
    }
    using dilationsTy = ::mlir::ArrayAttr;
    dilationsTy dilations;

    auto getDilations() {
      auto &propStorage = this->dilations;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setDilations(const ::mlir::ArrayAttr &propValue) {
      this->dilations = propValue;
    }
    using epsilonTy = ::mlir::FloatAttr;
    epsilonTy epsilon;

    auto getEpsilon() {
      auto &propStorage = this->epsilon;
      return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(propStorage);
    }
    void setEpsilon(const ::mlir::FloatAttr &propValue) {
      this->epsilon = propValue;
    }
    using explicit_paddingsTy = ::mlir::ArrayAttr;
    explicit_paddingsTy explicit_paddings;

    auto getExplicitPaddings() {
      auto &propStorage = this->explicit_paddings;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setExplicitPaddings(const ::mlir::ArrayAttr &propValue) {
      this->explicit_paddings = propValue;
    }
    using filter_formatTy = ::mlir::StringAttr;
    filter_formatTy filter_format;

    auto getFilterFormat() {
      auto &propStorage = this->filter_format;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setFilterFormat(const ::mlir::StringAttr &propValue) {
      this->filter_format = propValue;
    }
    using fused_opsTy = ::mlir::ArrayAttr;
    fused_opsTy fused_ops;

    auto getFusedOps() {
      auto &propStorage = this->fused_ops;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setFusedOps(const ::mlir::ArrayAttr &propValue) {
      this->fused_ops = propValue;
    }
    using leakyrelu_alphaTy = ::mlir::FloatAttr;
    leakyrelu_alphaTy leakyrelu_alpha;

    auto getLeakyreluAlpha() {
      auto &propStorage = this->leakyrelu_alpha;
      return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(propStorage);
    }
    void setLeakyreluAlpha(const ::mlir::FloatAttr &propValue) {
      this->leakyrelu_alpha = propValue;
    }
    using num_argsTy = ::mlir::IntegerAttr;
    num_argsTy num_args;

    auto getNumArgs() {
      auto &propStorage = this->num_args;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setNumArgs(const ::mlir::IntegerAttr &propValue) {
      this->num_args = propValue;
    }
    using paddingTy = ::mlir::StringAttr;
    paddingTy padding;

    auto getPadding() {
      auto &propStorage = this->padding;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setPadding(const ::mlir::StringAttr &propValue) {
      this->padding = propValue;
    }
    using stridesTy = ::mlir::ArrayAttr;
    stridesTy strides;

    auto getStrides() {
      auto &propStorage = this->strides;
      return ::llvm::cast<::mlir::ArrayAttr>(propStorage);
    }
    void setStrides(const ::mlir::ArrayAttr &propValue) {
      this->strides = propValue;
    }
    using use_cudnn_on_gpuTy = ::mlir::BoolAttr;
    use_cudnn_on_gpuTy use_cudnn_on_gpu;

    auto getUseCudnnOnGpu() {
      auto &propStorage = this->use_cudnn_on_gpu;
      return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(propStorage);
    }
    void setUseCudnnOnGpu(const ::mlir::BoolAttr &propValue) {
      this->use_cudnn_on_gpu = propValue;
    }
    using operandSegmentSizesTy = std::array<int32_t, 4>;
    operandSegmentSizesTy operandSegmentSizes;
    ::llvm::ArrayRef<int32_t> getOperandSegmentSizes() const {
      auto &propStorage = this->operandSegmentSizes;
      return propStorage;
    }
    void setOperandSegmentSizes(::llvm::ArrayRef<int32_t> propValue) {
      auto &propStorage = this->operandSegmentSizes;
      ::llvm::copy(propValue, propStorage.begin());
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.data_format == this->data_format &&
        rhs.dilations == this->dilations &&
        rhs.epsilon == this->epsilon &&
        rhs.explicit_paddings == this->explicit_paddings &&
        rhs.filter_format == this->filter_format &&
        rhs.fused_ops == this->fused_ops &&
        rhs.leakyrelu_alpha == this->leakyrelu_alpha &&
        rhs.num_args == this->num_args &&
        rhs.padding == this->padding &&
        rhs.strides == this->strides &&
        rhs.use_cudnn_on_gpu == this->use_cudnn_on_gpu &&
        rhs.operandSegmentSizes == this->operandSegmentSizes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _FusedConv2DOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._FusedConv2D", odsAttrs.getContext());
  }

  _FusedConv2DOpGenericAdaptorBase(_FusedConv2DOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::IntegerAttr getNumArgsAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().num_args);
    return attr;
  }

  uint64_t getNumArgs();
  ::mlir::ArrayAttr getStridesAttr() {
    auto attr = ::llvm::cast<::mlir::ArrayAttr>(getProperties().strides);
    return attr;
  }

  ::mlir::ArrayAttr getStrides();
  ::mlir::StringAttr getPaddingAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().padding);
    return attr;
  }

  ::llvm::StringRef getPadding();
  ::mlir::ArrayAttr getExplicitPaddingsAttr();
  ::mlir::ArrayAttr getExplicitPaddings();
  ::mlir::StringAttr getDataFormatAttr();
  ::llvm::StringRef getDataFormat();
  ::mlir::StringAttr getFilterFormatAttr();
  ::llvm::StringRef getFilterFormat();
  ::mlir::ArrayAttr getDilationsAttr();
  ::mlir::ArrayAttr getDilations();
  ::mlir::BoolAttr getUseCudnnOnGpuAttr();
  bool getUseCudnnOnGpu();
  ::mlir::ArrayAttr getFusedOpsAttr();
  ::mlir::ArrayAttr getFusedOps();
  ::mlir::FloatAttr getEpsilonAttr();
  ::llvm::APFloat getEpsilon();
  ::mlir::FloatAttr getLeakyreluAlphaAttr();
  ::llvm::APFloat getLeakyreluAlpha();
};
} // namespace detail
template <typename RangeT>
class _FusedConv2DOpGenericAdaptor : public detail::_FusedConv2DOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_FusedConv2DOpGenericAdaptorBase;
public:
  _FusedConv2DOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _FusedConv2DOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _FusedConv2DOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _FusedConv2DOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs) : _FusedConv2DOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _FusedConv2DOpGenericAdaptor(RangeT values, const _FusedConv2DOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _FusedConv2DOp, typename = std::enable_if_t<std::is_same_v<LateInst, _FusedConv2DOp>>>
  _FusedConv2DOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getInput() {
    return (*getODSOperands(0).begin());
  }

  ValueT getFilter() {
    return (*getODSOperands(1).begin());
  }

  RangeT getArgs() {
    return getODSOperands(2);
  }

  RangeT getHostArgs() {
    return getODSOperands(3);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _FusedConv2DOpAdaptor : public _FusedConv2DOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _FusedConv2DOpGenericAdaptor::_FusedConv2DOpGenericAdaptor;
  _FusedConv2DOpAdaptor(_FusedConv2DOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _FusedConv2DOp : public ::mlir::Op<_FusedConv2DOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::TensorType>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<2>::Impl, ::mlir::OpTrait::AttrSizedOperandSegments, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _FusedConv2DOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _FusedConv2DOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T"), ::llvm::StringRef("TArgs"), ::llvm::StringRef("data_format"), ::llvm::StringRef("dilations"), ::llvm::StringRef("epsilon"), ::llvm::StringRef("explicit_paddings"), ::llvm::StringRef("filter_format"), ::llvm::StringRef("fused_ops"), ::llvm::StringRef("leakyrelu_alpha"), ::llvm::StringRef("num_args"), ::llvm::StringRef("num_host_args"), ::llvm::StringRef("padding"), ::llvm::StringRef("strides"), ::llvm::StringRef("use_cudnn_on_gpu"), ::llvm::StringRef("operandSegmentSizes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getTArgsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getTArgsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getDataFormatAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getDataFormatAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getDilationsAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getDilationsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getEpsilonAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getEpsilonAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getExplicitPaddingsAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getExplicitPaddingsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  ::mlir::StringAttr getFilterFormatAttrName() {
    return getAttributeNameForIndex(6);
  }

  static ::mlir::StringAttr getFilterFormatAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 6);
  }

  ::mlir::StringAttr getFusedOpsAttrName() {
    return getAttributeNameForIndex(7);
  }

  static ::mlir::StringAttr getFusedOpsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 7);
  }

  ::mlir::StringAttr getLeakyreluAlphaAttrName() {
    return getAttributeNameForIndex(8);
  }

  static ::mlir::StringAttr getLeakyreluAlphaAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 8);
  }

  ::mlir::StringAttr getNumArgsAttrName() {
    return getAttributeNameForIndex(9);
  }

  static ::mlir::StringAttr getNumArgsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 9);
  }

  ::mlir::StringAttr getNumHostArgsAttrName() {
    return getAttributeNameForIndex(10);
  }

  static ::mlir::StringAttr getNumHostArgsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 10);
  }

  ::mlir::StringAttr getPaddingAttrName() {
    return getAttributeNameForIndex(11);
  }

  static ::mlir::StringAttr getPaddingAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 11);
  }

  ::mlir::StringAttr getStridesAttrName() {
    return getAttributeNameForIndex(12);
  }

  static ::mlir::StringAttr getStridesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 12);
  }

  ::mlir::StringAttr getUseCudnnOnGpuAttrName() {
    return getAttributeNameForIndex(13);
  }

  static ::mlir::StringAttr getUseCudnnOnGpuAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 13);
  }

  ::mlir::StringAttr getOperandSegmentSizesAttrName() {
   return (*this)->getName().getAttributeNames().back();
  }

  static ::mlir::StringAttr getOperandSegmentSizesAttrName(::mlir::OperationName name) {
   return name.getAttributeNames().back();
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._FusedConv2D");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getInput() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getFilter() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(1).begin());
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(2);
  }

  ::mlir::Operation::operand_range getHostArgs() {
    return getODSOperands(3);
  }

  ::mlir::OpOperand &getInputMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getFilterMutable() {
    auto range = getODSOperandIndexAndLength(1);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  ::mlir::MutableOperandRange getHostArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getOutput() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getNumArgsAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().num_args);
  }

  uint64_t getNumArgs();
  ::mlir::ArrayAttr getStridesAttr() {
    return ::llvm::cast<::mlir::ArrayAttr>(getProperties().strides);
  }

  ::mlir::ArrayAttr getStrides();
  ::mlir::StringAttr getPaddingAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().padding);
  }

  ::llvm::StringRef getPadding();
  ::mlir::ArrayAttr getExplicitPaddingsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().explicit_paddings);
  }

  ::mlir::ArrayAttr getExplicitPaddings();
  ::mlir::StringAttr getDataFormatAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().data_format);
  }

  ::llvm::StringRef getDataFormat();
  ::mlir::StringAttr getFilterFormatAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().filter_format);
  }

  ::llvm::StringRef getFilterFormat();
  ::mlir::ArrayAttr getDilationsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().dilations);
  }

  ::mlir::ArrayAttr getDilations();
  ::mlir::BoolAttr getUseCudnnOnGpuAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().use_cudnn_on_gpu);
  }

  bool getUseCudnnOnGpu();
  ::mlir::ArrayAttr getFusedOpsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().fused_ops);
  }

  ::mlir::ArrayAttr getFusedOps();
  ::mlir::FloatAttr getEpsilonAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(getProperties().epsilon);
  }

  ::llvm::APFloat getEpsilon();
  ::mlir::FloatAttr getLeakyreluAlphaAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(getProperties().leakyrelu_alpha);
  }

  ::llvm::APFloat getLeakyreluAlpha();
  size_t getNumHostArgs();
  ::mlir::Type getT();
  mlir::OperandElementTypeRange getTArgs();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setNumArgsAttr(::mlir::IntegerAttr attr) {
    getProperties().num_args = attr;
  }

  void setNumArgs(uint64_t attrValue);
  void setStridesAttr(::mlir::ArrayAttr attr) {
    getProperties().strides = attr;
  }

  void setPaddingAttr(::mlir::StringAttr attr) {
    getProperties().padding = attr;
  }

  void setPadding(::llvm::StringRef attrValue);
  void setExplicitPaddingsAttr(::mlir::ArrayAttr attr) {
    getProperties().explicit_paddings = attr;
  }

  void setDataFormatAttr(::mlir::StringAttr attr) {
    getProperties().data_format = attr;
  }

  void setDataFormat(::std::optional<::llvm::StringRef> attrValue);
  void setFilterFormatAttr(::mlir::StringAttr attr) {
    getProperties().filter_format = attr;
  }

  void setFilterFormat(::std::optional<::llvm::StringRef> attrValue);
  void setDilationsAttr(::mlir::ArrayAttr attr) {
    getProperties().dilations = attr;
  }

  void setUseCudnnOnGpuAttr(::mlir::BoolAttr attr) {
    getProperties().use_cudnn_on_gpu = attr;
  }

  void setUseCudnnOnGpu(::std::optional<bool> attrValue);
  void setFusedOpsAttr(::mlir::ArrayAttr attr) {
    getProperties().fused_ops = attr;
  }

  void setEpsilonAttr(::mlir::FloatAttr attr) {
    getProperties().epsilon = attr;
  }

  void setEpsilon(::std::optional<::llvm::APFloat> attrValue);
  void setLeakyreluAlphaAttr(::mlir::FloatAttr attr) {
    getProperties().leakyrelu_alpha = attr;
  }

  void setLeakyreluAlpha(::std::optional<::llvm::APFloat> attrValue);
  ::mlir::Attribute removeExplicitPaddingsAttr() {
      auto &attr = getProperties().explicit_paddings;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeDataFormatAttr() {
      auto &attr = getProperties().data_format;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeFilterFormatAttr() {
      auto &attr = getProperties().filter_format;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeDilationsAttr() {
      auto &attr = getProperties().dilations;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeUseCudnnOnGpuAttr() {
      auto &attr = getProperties().use_cudnn_on_gpu;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeFusedOpsAttr() {
      auto &attr = getProperties().fused_ops;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeEpsilonAttr() {
      auto &attr = getProperties().epsilon;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeLeakyreluAlphaAttr() {
      auto &attr = getProperties().leakyrelu_alpha;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type output, ::mlir::Value input, ::mlir::Value filter, ::mlir::ValueRange args, ::mlir::ValueRange host_args, ::mlir::IntegerAttr num_args, ::mlir::ArrayAttr strides, ::mlir::StringAttr padding, /*optional*/::mlir::ArrayAttr explicit_paddings, /*optional*/::mlir::StringAttr data_format, /*optional*/::mlir::StringAttr filter_format, /*optional*/::mlir::ArrayAttr dilations, /*optional*/::mlir::BoolAttr use_cudnn_on_gpu, /*optional*/::mlir::ArrayAttr fused_ops, /*optional*/::mlir::FloatAttr epsilon, /*optional*/::mlir::FloatAttr leakyrelu_alpha);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value input, ::mlir::Value filter, ::mlir::ValueRange args, ::mlir::ValueRange host_args, ::mlir::IntegerAttr num_args, ::mlir::ArrayAttr strides, ::mlir::StringAttr padding, /*optional*/::mlir::ArrayAttr explicit_paddings, /*optional*/::mlir::StringAttr data_format, /*optional*/::mlir::StringAttr filter_format, /*optional*/::mlir::ArrayAttr dilations, /*optional*/::mlir::BoolAttr use_cudnn_on_gpu, /*optional*/::mlir::ArrayAttr fused_ops, /*optional*/::mlir::FloatAttr epsilon, /*optional*/::mlir::FloatAttr leakyrelu_alpha);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type output, ::mlir::Value input, ::mlir::Value filter, ::mlir::ValueRange args, ::mlir::ValueRange host_args, uint64_t num_args, ::mlir::ArrayAttr strides, ::llvm::StringRef padding, /*optional*/::mlir::ArrayAttr explicit_paddings, /*optional*/::llvm::StringRef data_format, /*optional*/::llvm::StringRef filter_format, /*optional*/::mlir::ArrayAttr dilations, /*optional*/bool use_cudnn_on_gpu, /*optional*/::mlir::ArrayAttr fused_ops, /*optional*/::llvm::APFloat epsilon, /*optional*/::llvm::APFloat leakyrelu_alpha);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value input, ::mlir::Value filter, ::mlir::ValueRange args, ::mlir::ValueRange host_args, uint64_t num_args, ::mlir::ArrayAttr strides, ::llvm::StringRef padding, /*optional*/::mlir::ArrayAttr explicit_paddings, /*optional*/::llvm::StringRef data_format, /*optional*/::llvm::StringRef filter_format, /*optional*/::mlir::ArrayAttr dilations, /*optional*/bool use_cudnn_on_gpu, /*optional*/::mlir::ArrayAttr fused_ops, /*optional*/::llvm::APFloat epsilon, /*optional*/::llvm::APFloat leakyrelu_alpha);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 14 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_FusedConv2DOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_FusedMatMulOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _FusedMatMulOpGenericAdaptorBase {
public:
  struct Properties {
    using epsilonTy = ::mlir::FloatAttr;
    epsilonTy epsilon;

    auto getEpsilon() {
      auto &propStorage = this->epsilon;
      return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(propStorage);
    }
    void setEpsilon(const ::mlir::FloatAttr &propValue) {
      this->epsilon = propValue;
    }
    using fused_opsTy = ::mlir::ArrayAttr;
    fused_opsTy fused_ops;

    auto getFusedOps() {
      auto &propStorage = this->fused_ops;
      return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(propStorage);
    }
    void setFusedOps(const ::mlir::ArrayAttr &propValue) {
      this->fused_ops = propValue;
    }
    using leakyrelu_alphaTy = ::mlir::FloatAttr;
    leakyrelu_alphaTy leakyrelu_alpha;

    auto getLeakyreluAlpha() {
      auto &propStorage = this->leakyrelu_alpha;
      return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(propStorage);
    }
    void setLeakyreluAlpha(const ::mlir::FloatAttr &propValue) {
      this->leakyrelu_alpha = propValue;
    }
    using transpose_aTy = ::mlir::BoolAttr;
    transpose_aTy transpose_a;

    auto getTransposeA() {
      auto &propStorage = this->transpose_a;
      return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(propStorage);
    }
    void setTransposeA(const ::mlir::BoolAttr &propValue) {
      this->transpose_a = propValue;
    }
    using transpose_bTy = ::mlir::BoolAttr;
    transpose_bTy transpose_b;

    auto getTransposeB() {
      auto &propStorage = this->transpose_b;
      return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(propStorage);
    }
    void setTransposeB(const ::mlir::BoolAttr &propValue) {
      this->transpose_b = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.epsilon == this->epsilon &&
        rhs.fused_ops == this->fused_ops &&
        rhs.leakyrelu_alpha == this->leakyrelu_alpha &&
        rhs.transpose_a == this->transpose_a &&
        rhs.transpose_b == this->transpose_b &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _FusedMatMulOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._FusedMatMul", odsAttrs.getContext());
  }

  _FusedMatMulOpGenericAdaptorBase(_FusedMatMulOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::BoolAttr getTransposeAAttr();
  bool getTransposeA();
  ::mlir::BoolAttr getTransposeBAttr();
  bool getTransposeB();
  ::mlir::ArrayAttr getFusedOpsAttr();
  ::mlir::ArrayAttr getFusedOps();
  ::mlir::FloatAttr getEpsilonAttr();
  ::llvm::APFloat getEpsilon();
  ::mlir::FloatAttr getLeakyreluAlphaAttr();
  ::llvm::APFloat getLeakyreluAlpha();
};
} // namespace detail
template <typename RangeT>
class _FusedMatMulOpGenericAdaptor : public detail::_FusedMatMulOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_FusedMatMulOpGenericAdaptorBase;
public:
  _FusedMatMulOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _FusedMatMulOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _FusedMatMulOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _FusedMatMulOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _FusedMatMulOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _FusedMatMulOpGenericAdaptor(RangeT values, const _FusedMatMulOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _FusedMatMulOp, typename = std::enable_if_t<std::is_same_v<LateInst, _FusedMatMulOp>>>
  _FusedMatMulOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getA() {
    return (*getODSOperands(0).begin());
  }

  ValueT getB() {
    return (*getODSOperands(1).begin());
  }

  RangeT getArgs() {
    return getODSOperands(2);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _FusedMatMulOpAdaptor : public _FusedMatMulOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _FusedMatMulOpGenericAdaptor::_FusedMatMulOpGenericAdaptor;
  _FusedMatMulOpAdaptor(_FusedMatMulOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _FusedMatMulOp : public ::mlir::Op<_FusedMatMulOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::TensorType>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<2>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::OpTrait::TF::SameOperandsAndResultElementTypeResolveRef, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _FusedMatMulOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _FusedMatMulOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T"), ::llvm::StringRef("epsilon"), ::llvm::StringRef("fused_ops"), ::llvm::StringRef("leakyrelu_alpha"), ::llvm::StringRef("num_args"), ::llvm::StringRef("transpose_a"), ::llvm::StringRef("transpose_b")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getEpsilonAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getEpsilonAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getFusedOpsAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getFusedOpsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getLeakyreluAlphaAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getLeakyreluAlphaAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getNumArgsAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getNumArgsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getTransposeAAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getTransposeAAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  ::mlir::StringAttr getTransposeBAttrName() {
    return getAttributeNameForIndex(6);
  }

  static ::mlir::StringAttr getTransposeBAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 6);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._FusedMatMul");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getA() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getB() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(1).begin());
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(2);
  }

  ::mlir::OpOperand &getAMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getBMutable() {
    auto range = getODSOperandIndexAndLength(1);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::MutableOperandRange getArgsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getProduct() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::BoolAttr getTransposeAAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().transpose_a);
  }

  bool getTransposeA();
  ::mlir::BoolAttr getTransposeBAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().transpose_b);
  }

  bool getTransposeB();
  ::mlir::ArrayAttr getFusedOpsAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::ArrayAttr>(getProperties().fused_ops);
  }

  ::mlir::ArrayAttr getFusedOps();
  ::mlir::FloatAttr getEpsilonAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(getProperties().epsilon);
  }

  ::llvm::APFloat getEpsilon();
  ::mlir::FloatAttr getLeakyreluAlphaAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::FloatAttr>(getProperties().leakyrelu_alpha);
  }

  ::llvm::APFloat getLeakyreluAlpha();
  size_t getNumArgs();
  ::mlir::Type getT();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setTransposeAAttr(::mlir::BoolAttr attr) {
    getProperties().transpose_a = attr;
  }

  void setTransposeA(::std::optional<bool> attrValue);
  void setTransposeBAttr(::mlir::BoolAttr attr) {
    getProperties().transpose_b = attr;
  }

  void setTransposeB(::std::optional<bool> attrValue);
  void setFusedOpsAttr(::mlir::ArrayAttr attr) {
    getProperties().fused_ops = attr;
  }

  void setEpsilonAttr(::mlir::FloatAttr attr) {
    getProperties().epsilon = attr;
  }

  void setEpsilon(::std::optional<::llvm::APFloat> attrValue);
  void setLeakyreluAlphaAttr(::mlir::FloatAttr attr) {
    getProperties().leakyrelu_alpha = attr;
  }

  void setLeakyreluAlpha(::std::optional<::llvm::APFloat> attrValue);
  ::mlir::Attribute removeTransposeAAttr() {
      auto &attr = getProperties().transpose_a;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeTransposeBAttr() {
      auto &attr = getProperties().transpose_b;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeFusedOpsAttr() {
      auto &attr = getProperties().fused_ops;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeEpsilonAttr() {
      auto &attr = getProperties().epsilon;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeLeakyreluAlphaAttr() {
      auto &attr = getProperties().leakyrelu_alpha;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type product, ::mlir::Value a, ::mlir::Value b, ::mlir::ValueRange args, /*optional*/::mlir::BoolAttr transpose_a, /*optional*/::mlir::BoolAttr transpose_b, /*optional*/::mlir::ArrayAttr fused_ops, /*optional*/::mlir::FloatAttr epsilon, /*optional*/::mlir::FloatAttr leakyrelu_alpha);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value a, ::mlir::Value b, ::mlir::ValueRange args, /*optional*/::mlir::BoolAttr transpose_a, /*optional*/::mlir::BoolAttr transpose_b, /*optional*/::mlir::ArrayAttr fused_ops, /*optional*/::mlir::FloatAttr epsilon, /*optional*/::mlir::FloatAttr leakyrelu_alpha);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type product, ::mlir::Value a, ::mlir::Value b, ::mlir::ValueRange args, /*optional*/bool transpose_a, /*optional*/bool transpose_b, /*optional*/::mlir::ArrayAttr fused_ops, /*optional*/::llvm::APFloat epsilon, /*optional*/::llvm::APFloat leakyrelu_alpha);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value a, ::mlir::Value b, ::mlir::ValueRange args, /*optional*/bool transpose_a, /*optional*/bool transpose_b, /*optional*/::mlir::ArrayAttr fused_ops, /*optional*/::llvm::APFloat epsilon, /*optional*/::llvm::APFloat leakyrelu_alpha);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 7 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_FusedMatMulOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_HostRecvOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _HostRecvOpGenericAdaptorBase {
public:
  struct Properties {
    using client_terminatedTy = ::mlir::BoolAttr;
    client_terminatedTy client_terminated;

    auto getClientTerminated() {
      auto &propStorage = this->client_terminated;
      return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(propStorage);
    }
    void setClientTerminated(const ::mlir::BoolAttr &propValue) {
      this->client_terminated = propValue;
    }
    using recv_deviceTy = ::mlir::StringAttr;
    recv_deviceTy recv_device;

    auto getRecvDevice() {
      auto &propStorage = this->recv_device;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setRecvDevice(const ::mlir::StringAttr &propValue) {
      this->recv_device = propValue;
    }
    using send_deviceTy = ::mlir::StringAttr;
    send_deviceTy send_device;

    auto getSendDevice() {
      auto &propStorage = this->send_device;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setSendDevice(const ::mlir::StringAttr &propValue) {
      this->send_device = propValue;
    }
    using send_device_incarnationTy = ::mlir::IntegerAttr;
    send_device_incarnationTy send_device_incarnation;

    auto getSendDeviceIncarnation() {
      auto &propStorage = this->send_device_incarnation;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setSendDeviceIncarnation(const ::mlir::IntegerAttr &propValue) {
      this->send_device_incarnation = propValue;
    }
    using tensor_nameTy = ::mlir::StringAttr;
    tensor_nameTy tensor_name;

    auto getTensorName() {
      auto &propStorage = this->tensor_name;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setTensorName(const ::mlir::StringAttr &propValue) {
      this->tensor_name = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.client_terminated == this->client_terminated &&
        rhs.recv_device == this->recv_device &&
        rhs.send_device == this->send_device &&
        rhs.send_device_incarnation == this->send_device_incarnation &&
        rhs.tensor_name == this->tensor_name &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _HostRecvOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._HostRecv", odsAttrs.getContext());
  }

  _HostRecvOpGenericAdaptorBase(_HostRecvOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getTensorNameAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().tensor_name);
    return attr;
  }

  ::llvm::StringRef getTensorName();
  ::mlir::StringAttr getSendDeviceAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().send_device);
    return attr;
  }

  ::llvm::StringRef getSendDevice();
  ::mlir::IntegerAttr getSendDeviceIncarnationAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().send_device_incarnation);
    return attr;
  }

  uint64_t getSendDeviceIncarnation();
  ::mlir::StringAttr getRecvDeviceAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().recv_device);
    return attr;
  }

  ::llvm::StringRef getRecvDevice();
  ::mlir::BoolAttr getClientTerminatedAttr();
  bool getClientTerminated();
};
} // namespace detail
template <typename RangeT>
class _HostRecvOpGenericAdaptor : public detail::_HostRecvOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_HostRecvOpGenericAdaptorBase;
public:
  _HostRecvOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _HostRecvOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _HostRecvOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _HostRecvOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _HostRecvOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _HostRecvOpGenericAdaptor(RangeT values, const _HostRecvOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _HostRecvOp, typename = std::enable_if_t<std::is_same_v<LateInst, _HostRecvOp>>>
  _HostRecvOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _HostRecvOpAdaptor : public _HostRecvOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _HostRecvOpGenericAdaptor::_HostRecvOpGenericAdaptor;
  _HostRecvOpAdaptor(_HostRecvOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _HostRecvOp : public ::mlir::Op<_HostRecvOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::TensorType>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, GetResourceInstanceInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _HostRecvOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _HostRecvOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("client_terminated"), ::llvm::StringRef("recv_device"), ::llvm::StringRef("send_device"), ::llvm::StringRef("send_device_incarnation"), ::llvm::StringRef("tensor_name"), ::llvm::StringRef("tensor_type")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getClientTerminatedAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getClientTerminatedAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getRecvDeviceAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getRecvDeviceAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getSendDeviceAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getSendDeviceAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getSendDeviceIncarnationAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getSendDeviceIncarnationAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getTensorNameAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getTensorNameAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getTensorTypeAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getTensorTypeAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._HostRecv");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getTensor() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getTensorNameAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().tensor_name);
  }

  ::llvm::StringRef getTensorName();
  ::mlir::StringAttr getSendDeviceAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().send_device);
  }

  ::llvm::StringRef getSendDevice();
  ::mlir::IntegerAttr getSendDeviceIncarnationAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().send_device_incarnation);
  }

  uint64_t getSendDeviceIncarnation();
  ::mlir::StringAttr getRecvDeviceAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().recv_device);
  }

  ::llvm::StringRef getRecvDevice();
  ::mlir::BoolAttr getClientTerminatedAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().client_terminated);
  }

  bool getClientTerminated();
  ::mlir::Type getTensorType();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setTensorNameAttr(::mlir::StringAttr attr) {
    getProperties().tensor_name = attr;
  }

  void setTensorName(::llvm::StringRef attrValue);
  void setSendDeviceAttr(::mlir::StringAttr attr) {
    getProperties().send_device = attr;
  }

  void setSendDevice(::llvm::StringRef attrValue);
  void setSendDeviceIncarnationAttr(::mlir::IntegerAttr attr) {
    getProperties().send_device_incarnation = attr;
  }

  void setSendDeviceIncarnation(uint64_t attrValue);
  void setRecvDeviceAttr(::mlir::StringAttr attr) {
    getProperties().recv_device = attr;
  }

  void setRecvDevice(::llvm::StringRef attrValue);
  void setClientTerminatedAttr(::mlir::BoolAttr attr) {
    getProperties().client_terminated = attr;
  }

  void setClientTerminated(::std::optional<bool> attrValue);
  ::mlir::Attribute removeClientTerminatedAttr() {
      auto &attr = getProperties().client_terminated;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type tensor, ::mlir::StringAttr tensor_name, ::mlir::StringAttr send_device, ::mlir::IntegerAttr send_device_incarnation, ::mlir::StringAttr recv_device, /*optional*/::mlir::BoolAttr client_terminated = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::StringAttr tensor_name, ::mlir::StringAttr send_device, ::mlir::IntegerAttr send_device_incarnation, ::mlir::StringAttr recv_device, /*optional*/::mlir::BoolAttr client_terminated = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type tensor, ::llvm::StringRef tensor_name, ::llvm::StringRef send_device, uint64_t send_device_incarnation, ::llvm::StringRef recv_device, /*optional*/bool client_terminated = false);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef tensor_name, ::llvm::StringRef send_device, uint64_t send_device_incarnation, ::llvm::StringRef recv_device, /*optional*/bool client_terminated = false);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  std::optional<std::string> GetResourceInstanceStr();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 6 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_HostRecvOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_HostSendOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _HostSendOpGenericAdaptorBase {
public:
  struct Properties {
    using client_terminatedTy = ::mlir::BoolAttr;
    client_terminatedTy client_terminated;

    auto getClientTerminated() {
      auto &propStorage = this->client_terminated;
      return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(propStorage);
    }
    void setClientTerminated(const ::mlir::BoolAttr &propValue) {
      this->client_terminated = propValue;
    }
    using recv_deviceTy = ::mlir::StringAttr;
    recv_deviceTy recv_device;

    auto getRecvDevice() {
      auto &propStorage = this->recv_device;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setRecvDevice(const ::mlir::StringAttr &propValue) {
      this->recv_device = propValue;
    }
    using send_deviceTy = ::mlir::StringAttr;
    send_deviceTy send_device;

    auto getSendDevice() {
      auto &propStorage = this->send_device;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setSendDevice(const ::mlir::StringAttr &propValue) {
      this->send_device = propValue;
    }
    using send_device_incarnationTy = ::mlir::IntegerAttr;
    send_device_incarnationTy send_device_incarnation;

    auto getSendDeviceIncarnation() {
      auto &propStorage = this->send_device_incarnation;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setSendDeviceIncarnation(const ::mlir::IntegerAttr &propValue) {
      this->send_device_incarnation = propValue;
    }
    using tensor_nameTy = ::mlir::StringAttr;
    tensor_nameTy tensor_name;

    auto getTensorName() {
      auto &propStorage = this->tensor_name;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setTensorName(const ::mlir::StringAttr &propValue) {
      this->tensor_name = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.client_terminated == this->client_terminated &&
        rhs.recv_device == this->recv_device &&
        rhs.send_device == this->send_device &&
        rhs.send_device_incarnation == this->send_device_incarnation &&
        rhs.tensor_name == this->tensor_name &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _HostSendOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._HostSend", odsAttrs.getContext());
  }

  _HostSendOpGenericAdaptorBase(_HostSendOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getTensorNameAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().tensor_name);
    return attr;
  }

  ::llvm::StringRef getTensorName();
  ::mlir::StringAttr getSendDeviceAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().send_device);
    return attr;
  }

  ::llvm::StringRef getSendDevice();
  ::mlir::IntegerAttr getSendDeviceIncarnationAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().send_device_incarnation);
    return attr;
  }

  uint64_t getSendDeviceIncarnation();
  ::mlir::StringAttr getRecvDeviceAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().recv_device);
    return attr;
  }

  ::llvm::StringRef getRecvDevice();
  ::mlir::BoolAttr getClientTerminatedAttr();
  bool getClientTerminated();
};
} // namespace detail
template <typename RangeT>
class _HostSendOpGenericAdaptor : public detail::_HostSendOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_HostSendOpGenericAdaptorBase;
public:
  _HostSendOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _HostSendOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _HostSendOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _HostSendOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _HostSendOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _HostSendOpGenericAdaptor(RangeT values, const _HostSendOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _HostSendOp, typename = std::enable_if_t<std::is_same_v<LateInst, _HostSendOp>>>
  _HostSendOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getTensor() {
    return (*getODSOperands(0).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _HostSendOpAdaptor : public _HostSendOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _HostSendOpGenericAdaptor::_HostSendOpGenericAdaptor;
  _HostSendOpAdaptor(_HostSendOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _HostSendOp : public ::mlir::Op<_HostSendOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::OneOperand, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, GetResourceInstanceInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _HostSendOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _HostSendOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T"), ::llvm::StringRef("client_terminated"), ::llvm::StringRef("recv_device"), ::llvm::StringRef("send_device"), ::llvm::StringRef("send_device_incarnation"), ::llvm::StringRef("tensor_name")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getClientTerminatedAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getClientTerminatedAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getRecvDeviceAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getRecvDeviceAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getSendDeviceAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getSendDeviceAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getSendDeviceIncarnationAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getSendDeviceIncarnationAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getTensorNameAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getTensorNameAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._HostSend");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getTensor() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::OpOperand &getTensorMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getTensorNameAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().tensor_name);
  }

  ::llvm::StringRef getTensorName();
  ::mlir::StringAttr getSendDeviceAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().send_device);
  }

  ::llvm::StringRef getSendDevice();
  ::mlir::IntegerAttr getSendDeviceIncarnationAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().send_device_incarnation);
  }

  uint64_t getSendDeviceIncarnation();
  ::mlir::StringAttr getRecvDeviceAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().recv_device);
  }

  ::llvm::StringRef getRecvDevice();
  ::mlir::BoolAttr getClientTerminatedAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().client_terminated);
  }

  bool getClientTerminated();
  ::mlir::Type getT();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setTensorNameAttr(::mlir::StringAttr attr) {
    getProperties().tensor_name = attr;
  }

  void setTensorName(::llvm::StringRef attrValue);
  void setSendDeviceAttr(::mlir::StringAttr attr) {
    getProperties().send_device = attr;
  }

  void setSendDevice(::llvm::StringRef attrValue);
  void setSendDeviceIncarnationAttr(::mlir::IntegerAttr attr) {
    getProperties().send_device_incarnation = attr;
  }

  void setSendDeviceIncarnation(uint64_t attrValue);
  void setRecvDeviceAttr(::mlir::StringAttr attr) {
    getProperties().recv_device = attr;
  }

  void setRecvDevice(::llvm::StringRef attrValue);
  void setClientTerminatedAttr(::mlir::BoolAttr attr) {
    getProperties().client_terminated = attr;
  }

  void setClientTerminated(::std::optional<bool> attrValue);
  ::mlir::Attribute removeClientTerminatedAttr() {
      auto &attr = getProperties().client_terminated;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value tensor, ::mlir::StringAttr tensor_name, ::mlir::StringAttr send_device, ::mlir::IntegerAttr send_device_incarnation, ::mlir::StringAttr recv_device, /*optional*/::mlir::BoolAttr client_terminated = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value tensor, ::mlir::StringAttr tensor_name, ::mlir::StringAttr send_device, ::mlir::IntegerAttr send_device_incarnation, ::mlir::StringAttr recv_device, /*optional*/::mlir::BoolAttr client_terminated = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value tensor, ::llvm::StringRef tensor_name, ::llvm::StringRef send_device, uint64_t send_device_incarnation, ::llvm::StringRef recv_device, /*optional*/bool client_terminated = false);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value tensor, ::llvm::StringRef tensor_name, ::llvm::StringRef send_device, uint64_t send_device_incarnation, ::llvm::StringRef recv_device, /*optional*/bool client_terminated = false);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  std::optional<std::string> GetResourceInstanceStr();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 6 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_HostSendOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_InternalTestMustExecuteTrait_ declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _InternalTestMustExecuteTrait_GenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  _InternalTestMustExecuteTrait_GenericAdaptorBase(::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._InternalTestMustExecuteTrait_", odsAttrs.getContext());
  }

  _InternalTestMustExecuteTrait_GenericAdaptorBase(::mlir::Operation *op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), odsRegions(op->getRegions()) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

};
} // namespace detail
template <typename RangeT>
class _InternalTestMustExecuteTrait_GenericAdaptor : public detail::_InternalTestMustExecuteTrait_GenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_InternalTestMustExecuteTrait_GenericAdaptorBase;
public:
  _InternalTestMustExecuteTrait_GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _InternalTestMustExecuteTrait_GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _InternalTestMustExecuteTrait_GenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  _InternalTestMustExecuteTrait_GenericAdaptor(RangeT values, const _InternalTestMustExecuteTrait_GenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _InternalTestMustExecuteTrait_, typename = std::enable_if_t<std::is_same_v<LateInst, _InternalTestMustExecuteTrait_>>>
  _InternalTestMustExecuteTrait_GenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _InternalTestMustExecuteTrait_Adaptor : public _InternalTestMustExecuteTrait_GenericAdaptor<::mlir::ValueRange> {
public:
  using _InternalTestMustExecuteTrait_GenericAdaptor::_InternalTestMustExecuteTrait_GenericAdaptor;
  _InternalTestMustExecuteTrait_Adaptor(_InternalTestMustExecuteTrait_ op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _InternalTestMustExecuteTrait_ : public ::mlir::Op<_InternalTestMustExecuteTrait_, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::OpInvariants, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _InternalTestMustExecuteTrait_Adaptor;
  template <typename RangeT>
  using GenericAdaptor = _InternalTestMustExecuteTrait_GenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    return {};
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._InternalTestMustExecuteTrait_");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_InternalTestMustExecuteTrait_)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_InternalTestNonResourceValueSideEffects_ declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _InternalTestNonResourceValueSideEffects_GenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  _InternalTestNonResourceValueSideEffects_GenericAdaptorBase(::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._InternalTestNonResourceValueSideEffects_", odsAttrs.getContext());
  }

  _InternalTestNonResourceValueSideEffects_GenericAdaptorBase(::mlir::Operation *op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), odsRegions(op->getRegions()) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

};
} // namespace detail
template <typename RangeT>
class _InternalTestNonResourceValueSideEffects_GenericAdaptor : public detail::_InternalTestNonResourceValueSideEffects_GenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_InternalTestNonResourceValueSideEffects_GenericAdaptorBase;
public:
  _InternalTestNonResourceValueSideEffects_GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _InternalTestNonResourceValueSideEffects_GenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _InternalTestNonResourceValueSideEffects_GenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  _InternalTestNonResourceValueSideEffects_GenericAdaptor(RangeT values, const _InternalTestNonResourceValueSideEffects_GenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _InternalTestNonResourceValueSideEffects_, typename = std::enable_if_t<std::is_same_v<LateInst, _InternalTestNonResourceValueSideEffects_>>>
  _InternalTestNonResourceValueSideEffects_GenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getKey() {
    return (*getODSOperands(0).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _InternalTestNonResourceValueSideEffects_Adaptor : public _InternalTestNonResourceValueSideEffects_GenericAdaptor<::mlir::ValueRange> {
public:
  using _InternalTestNonResourceValueSideEffects_GenericAdaptor::_InternalTestNonResourceValueSideEffects_GenericAdaptor;
  _InternalTestNonResourceValueSideEffects_Adaptor(_InternalTestNonResourceValueSideEffects_ op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _InternalTestNonResourceValueSideEffects_ : public ::mlir::Op<_InternalTestNonResourceValueSideEffects_, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::OneOperand, ::mlir::OpTrait::OpInvariants, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _InternalTestNonResourceValueSideEffects_Adaptor;
  template <typename RangeT>
  using GenericAdaptor = _InternalTestNonResourceValueSideEffects_GenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    return {};
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._InternalTestNonResourceValueSideEffects_");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getKey() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::OpOperand &getKeyMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value key);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value key);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_InternalTestNonResourceValueSideEffects_)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_ListToArrayOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _ListToArrayOpGenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  _ListToArrayOpGenericAdaptorBase(::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._ListToArray", odsAttrs.getContext());
  }

  _ListToArrayOpGenericAdaptorBase(::mlir::Operation *op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), odsRegions(op->getRegions()) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

};
} // namespace detail
template <typename RangeT>
class _ListToArrayOpGenericAdaptor : public detail::_ListToArrayOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_ListToArrayOpGenericAdaptorBase;
public:
  _ListToArrayOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _ListToArrayOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _ListToArrayOpGenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  _ListToArrayOpGenericAdaptor(RangeT values, const _ListToArrayOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _ListToArrayOp, typename = std::enable_if_t<std::is_same_v<LateInst, _ListToArrayOp>>>
  _ListToArrayOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getInput() {
    return getODSOperands(0);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _ListToArrayOpAdaptor : public _ListToArrayOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _ListToArrayOpGenericAdaptor::_ListToArrayOpGenericAdaptor;
  _ListToArrayOpAdaptor(_ListToArrayOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _ListToArrayOp : public ::mlir::Op<_ListToArrayOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::VariadicResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::OpInvariants, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _ListToArrayOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _ListToArrayOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("N"), ::llvm::StringRef("T"), ::llvm::StringRef("Tin")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getNAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getNAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getTinAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getTinAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._ListToArray");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getInput() {
    return getODSOperands(0);
  }

  ::mlir::MutableOperandRange getInputMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOutput() {
    return getODSResults(0);
  }

  mlir::OperandElementTypeRange getTin();
  size_t getN();
  ::mlir::Type getT();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 3 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_ListToArrayOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_RecvOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _RecvOpGenericAdaptorBase {
public:
  struct Properties {
    using client_terminatedTy = ::mlir::BoolAttr;
    client_terminatedTy client_terminated;

    auto getClientTerminated() {
      auto &propStorage = this->client_terminated;
      return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(propStorage);
    }
    void setClientTerminated(const ::mlir::BoolAttr &propValue) {
      this->client_terminated = propValue;
    }
    using recv_deviceTy = ::mlir::StringAttr;
    recv_deviceTy recv_device;

    auto getRecvDevice() {
      auto &propStorage = this->recv_device;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setRecvDevice(const ::mlir::StringAttr &propValue) {
      this->recv_device = propValue;
    }
    using send_deviceTy = ::mlir::StringAttr;
    send_deviceTy send_device;

    auto getSendDevice() {
      auto &propStorage = this->send_device;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setSendDevice(const ::mlir::StringAttr &propValue) {
      this->send_device = propValue;
    }
    using send_device_incarnationTy = ::mlir::IntegerAttr;
    send_device_incarnationTy send_device_incarnation;

    auto getSendDeviceIncarnation() {
      auto &propStorage = this->send_device_incarnation;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setSendDeviceIncarnation(const ::mlir::IntegerAttr &propValue) {
      this->send_device_incarnation = propValue;
    }
    using tensor_nameTy = ::mlir::StringAttr;
    tensor_nameTy tensor_name;

    auto getTensorName() {
      auto &propStorage = this->tensor_name;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setTensorName(const ::mlir::StringAttr &propValue) {
      this->tensor_name = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.client_terminated == this->client_terminated &&
        rhs.recv_device == this->recv_device &&
        rhs.send_device == this->send_device &&
        rhs.send_device_incarnation == this->send_device_incarnation &&
        rhs.tensor_name == this->tensor_name &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _RecvOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._Recv", odsAttrs.getContext());
  }

  _RecvOpGenericAdaptorBase(_RecvOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getTensorNameAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().tensor_name);
    return attr;
  }

  ::llvm::StringRef getTensorName();
  ::mlir::StringAttr getSendDeviceAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().send_device);
    return attr;
  }

  ::llvm::StringRef getSendDevice();
  ::mlir::IntegerAttr getSendDeviceIncarnationAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().send_device_incarnation);
    return attr;
  }

  uint64_t getSendDeviceIncarnation();
  ::mlir::StringAttr getRecvDeviceAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().recv_device);
    return attr;
  }

  ::llvm::StringRef getRecvDevice();
  ::mlir::BoolAttr getClientTerminatedAttr();
  bool getClientTerminated();
};
} // namespace detail
template <typename RangeT>
class _RecvOpGenericAdaptor : public detail::_RecvOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_RecvOpGenericAdaptorBase;
public:
  _RecvOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _RecvOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _RecvOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _RecvOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _RecvOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _RecvOpGenericAdaptor(RangeT values, const _RecvOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _RecvOp, typename = std::enable_if_t<std::is_same_v<LateInst, _RecvOp>>>
  _RecvOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _RecvOpAdaptor : public _RecvOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _RecvOpGenericAdaptor::_RecvOpGenericAdaptor;
  _RecvOpAdaptor(_RecvOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _RecvOp : public ::mlir::Op<_RecvOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::TensorType>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, GetResourceInstanceInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _RecvOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _RecvOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("client_terminated"), ::llvm::StringRef("recv_device"), ::llvm::StringRef("send_device"), ::llvm::StringRef("send_device_incarnation"), ::llvm::StringRef("tensor_name"), ::llvm::StringRef("tensor_type")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getClientTerminatedAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getClientTerminatedAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getRecvDeviceAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getRecvDeviceAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getSendDeviceAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getSendDeviceAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getSendDeviceIncarnationAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getSendDeviceIncarnationAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getTensorNameAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getTensorNameAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getTensorTypeAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getTensorTypeAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._Recv");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getTensor() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getTensorNameAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().tensor_name);
  }

  ::llvm::StringRef getTensorName();
  ::mlir::StringAttr getSendDeviceAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().send_device);
  }

  ::llvm::StringRef getSendDevice();
  ::mlir::IntegerAttr getSendDeviceIncarnationAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().send_device_incarnation);
  }

  uint64_t getSendDeviceIncarnation();
  ::mlir::StringAttr getRecvDeviceAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().recv_device);
  }

  ::llvm::StringRef getRecvDevice();
  ::mlir::BoolAttr getClientTerminatedAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().client_terminated);
  }

  bool getClientTerminated();
  ::mlir::Type getTensorType();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setTensorNameAttr(::mlir::StringAttr attr) {
    getProperties().tensor_name = attr;
  }

  void setTensorName(::llvm::StringRef attrValue);
  void setSendDeviceAttr(::mlir::StringAttr attr) {
    getProperties().send_device = attr;
  }

  void setSendDevice(::llvm::StringRef attrValue);
  void setSendDeviceIncarnationAttr(::mlir::IntegerAttr attr) {
    getProperties().send_device_incarnation = attr;
  }

  void setSendDeviceIncarnation(uint64_t attrValue);
  void setRecvDeviceAttr(::mlir::StringAttr attr) {
    getProperties().recv_device = attr;
  }

  void setRecvDevice(::llvm::StringRef attrValue);
  void setClientTerminatedAttr(::mlir::BoolAttr attr) {
    getProperties().client_terminated = attr;
  }

  void setClientTerminated(::std::optional<bool> attrValue);
  ::mlir::Attribute removeClientTerminatedAttr() {
      auto &attr = getProperties().client_terminated;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type tensor, ::mlir::StringAttr tensor_name, ::mlir::StringAttr send_device, ::mlir::IntegerAttr send_device_incarnation, ::mlir::StringAttr recv_device, /*optional*/::mlir::BoolAttr client_terminated = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::StringAttr tensor_name, ::mlir::StringAttr send_device, ::mlir::IntegerAttr send_device_incarnation, ::mlir::StringAttr recv_device, /*optional*/::mlir::BoolAttr client_terminated = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type tensor, ::llvm::StringRef tensor_name, ::llvm::StringRef send_device, uint64_t send_device_incarnation, ::llvm::StringRef recv_device, /*optional*/bool client_terminated = false);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::llvm::StringRef tensor_name, ::llvm::StringRef send_device, uint64_t send_device_incarnation, ::llvm::StringRef recv_device, /*optional*/bool client_terminated = false);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  std::optional<std::string> GetResourceInstanceStr();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 6 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_RecvOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_SendOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _SendOpGenericAdaptorBase {
public:
  struct Properties {
    using client_terminatedTy = ::mlir::BoolAttr;
    client_terminatedTy client_terminated;

    auto getClientTerminated() {
      auto &propStorage = this->client_terminated;
      return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(propStorage);
    }
    void setClientTerminated(const ::mlir::BoolAttr &propValue) {
      this->client_terminated = propValue;
    }
    using recv_deviceTy = ::mlir::StringAttr;
    recv_deviceTy recv_device;

    auto getRecvDevice() {
      auto &propStorage = this->recv_device;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setRecvDevice(const ::mlir::StringAttr &propValue) {
      this->recv_device = propValue;
    }
    using send_deviceTy = ::mlir::StringAttr;
    send_deviceTy send_device;

    auto getSendDevice() {
      auto &propStorage = this->send_device;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setSendDevice(const ::mlir::StringAttr &propValue) {
      this->send_device = propValue;
    }
    using send_device_incarnationTy = ::mlir::IntegerAttr;
    send_device_incarnationTy send_device_incarnation;

    auto getSendDeviceIncarnation() {
      auto &propStorage = this->send_device_incarnation;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setSendDeviceIncarnation(const ::mlir::IntegerAttr &propValue) {
      this->send_device_incarnation = propValue;
    }
    using tensor_nameTy = ::mlir::StringAttr;
    tensor_nameTy tensor_name;

    auto getTensorName() {
      auto &propStorage = this->tensor_name;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setTensorName(const ::mlir::StringAttr &propValue) {
      this->tensor_name = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.client_terminated == this->client_terminated &&
        rhs.recv_device == this->recv_device &&
        rhs.send_device == this->send_device &&
        rhs.send_device_incarnation == this->send_device_incarnation &&
        rhs.tensor_name == this->tensor_name &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _SendOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._Send", odsAttrs.getContext());
  }

  _SendOpGenericAdaptorBase(_SendOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getTensorNameAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().tensor_name);
    return attr;
  }

  ::llvm::StringRef getTensorName();
  ::mlir::StringAttr getSendDeviceAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().send_device);
    return attr;
  }

  ::llvm::StringRef getSendDevice();
  ::mlir::IntegerAttr getSendDeviceIncarnationAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().send_device_incarnation);
    return attr;
  }

  uint64_t getSendDeviceIncarnation();
  ::mlir::StringAttr getRecvDeviceAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().recv_device);
    return attr;
  }

  ::llvm::StringRef getRecvDevice();
  ::mlir::BoolAttr getClientTerminatedAttr();
  bool getClientTerminated();
};
} // namespace detail
template <typename RangeT>
class _SendOpGenericAdaptor : public detail::_SendOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_SendOpGenericAdaptorBase;
public:
  _SendOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _SendOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _SendOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _SendOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _SendOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _SendOpGenericAdaptor(RangeT values, const _SendOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _SendOp, typename = std::enable_if_t<std::is_same_v<LateInst, _SendOp>>>
  _SendOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getTensor() {
    return (*getODSOperands(0).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _SendOpAdaptor : public _SendOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _SendOpGenericAdaptor::_SendOpGenericAdaptor;
  _SendOpAdaptor(_SendOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _SendOp : public ::mlir::Op<_SendOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::OneOperand, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, GetResourceInstanceInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _SendOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _SendOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T"), ::llvm::StringRef("client_terminated"), ::llvm::StringRef("recv_device"), ::llvm::StringRef("send_device"), ::llvm::StringRef("send_device_incarnation"), ::llvm::StringRef("tensor_name")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getClientTerminatedAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getClientTerminatedAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getRecvDeviceAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getRecvDeviceAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getSendDeviceAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getSendDeviceAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getSendDeviceIncarnationAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getSendDeviceIncarnationAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getTensorNameAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getTensorNameAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._Send");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getTensor() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::OpOperand &getTensorMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getTensorNameAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().tensor_name);
  }

  ::llvm::StringRef getTensorName();
  ::mlir::StringAttr getSendDeviceAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().send_device);
  }

  ::llvm::StringRef getSendDevice();
  ::mlir::IntegerAttr getSendDeviceIncarnationAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().send_device_incarnation);
  }

  uint64_t getSendDeviceIncarnation();
  ::mlir::StringAttr getRecvDeviceAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().recv_device);
  }

  ::llvm::StringRef getRecvDevice();
  ::mlir::BoolAttr getClientTerminatedAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().client_terminated);
  }

  bool getClientTerminated();
  ::mlir::Type getT();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setTensorNameAttr(::mlir::StringAttr attr) {
    getProperties().tensor_name = attr;
  }

  void setTensorName(::llvm::StringRef attrValue);
  void setSendDeviceAttr(::mlir::StringAttr attr) {
    getProperties().send_device = attr;
  }

  void setSendDevice(::llvm::StringRef attrValue);
  void setSendDeviceIncarnationAttr(::mlir::IntegerAttr attr) {
    getProperties().send_device_incarnation = attr;
  }

  void setSendDeviceIncarnation(uint64_t attrValue);
  void setRecvDeviceAttr(::mlir::StringAttr attr) {
    getProperties().recv_device = attr;
  }

  void setRecvDevice(::llvm::StringRef attrValue);
  void setClientTerminatedAttr(::mlir::BoolAttr attr) {
    getProperties().client_terminated = attr;
  }

  void setClientTerminated(::std::optional<bool> attrValue);
  ::mlir::Attribute removeClientTerminatedAttr() {
      auto &attr = getProperties().client_terminated;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value tensor, ::mlir::StringAttr tensor_name, ::mlir::StringAttr send_device, ::mlir::IntegerAttr send_device_incarnation, ::mlir::StringAttr recv_device, /*optional*/::mlir::BoolAttr client_terminated = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value tensor, ::mlir::StringAttr tensor_name, ::mlir::StringAttr send_device, ::mlir::IntegerAttr send_device_incarnation, ::mlir::StringAttr recv_device, /*optional*/::mlir::BoolAttr client_terminated = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value tensor, ::llvm::StringRef tensor_name, ::llvm::StringRef send_device, uint64_t send_device_incarnation, ::llvm::StringRef recv_device, /*optional*/bool client_terminated = false);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value tensor, ::llvm::StringRef tensor_name, ::llvm::StringRef send_device, uint64_t send_device_incarnation, ::llvm::StringRef recv_device, /*optional*/bool client_terminated = false);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  std::optional<std::string> GetResourceInstanceStr();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 6 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_SendOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_TPUCompileMlirOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _TPUCompileMlirOpGenericAdaptorBase {
public:
  struct Properties {
    using metadataTy = ::mlir::StringAttr;
    metadataTy metadata;

    auto getMetadata() {
      auto &propStorage = this->metadata;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setMetadata(const ::mlir::StringAttr &propValue) {
      this->metadata = propValue;
    }
    using mlir_moduleTy = ::mlir::StringAttr;
    mlir_moduleTy mlir_module;

    auto getMlirModule() {
      auto &propStorage = this->mlir_module;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setMlirModule(const ::mlir::StringAttr &propValue) {
      this->mlir_module = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.metadata == this->metadata &&
        rhs.mlir_module == this->mlir_module &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _TPUCompileMlirOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._TPUCompileMlir", odsAttrs.getContext());
  }

  _TPUCompileMlirOpGenericAdaptorBase(_TPUCompileMlirOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getMlirModuleAttr();
  ::llvm::StringRef getMlirModule();
  ::mlir::StringAttr getMetadataAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().metadata);
    return attr;
  }

  ::llvm::StringRef getMetadata();
};
} // namespace detail
template <typename RangeT>
class _TPUCompileMlirOpGenericAdaptor : public detail::_TPUCompileMlirOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_TPUCompileMlirOpGenericAdaptorBase;
public:
  _TPUCompileMlirOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _TPUCompileMlirOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _TPUCompileMlirOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _TPUCompileMlirOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _TPUCompileMlirOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _TPUCompileMlirOpGenericAdaptor(RangeT values, const _TPUCompileMlirOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _TPUCompileMlirOp, typename = std::enable_if_t<std::is_same_v<LateInst, _TPUCompileMlirOp>>>
  _TPUCompileMlirOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getDynamicShapes() {
    return getODSOperands(0);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _TPUCompileMlirOpAdaptor : public _TPUCompileMlirOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _TPUCompileMlirOpGenericAdaptor::_TPUCompileMlirOpGenericAdaptor;
  _TPUCompileMlirOpAdaptor(_TPUCompileMlirOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _TPUCompileMlirOp : public ::mlir::Op<_TPUCompileMlirOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::AtLeastNResults<1>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _TPUCompileMlirOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _TPUCompileMlirOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("NumDynamicShapes"), ::llvm::StringRef("metadata"), ::llvm::StringRef("mlir_module"), ::llvm::StringRef("num_computations")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getNumDynamicShapesAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getNumDynamicShapesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getMetadataAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getMetadataAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getMlirModuleAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getMlirModuleAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getNumComputationsAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getNumComputationsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._TPUCompileMlir");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getDynamicShapes() {
    return getODSOperands(0);
  }

  ::mlir::MutableOperandRange getDynamicShapesMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getCompilationStatus() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  ::mlir::Operation::result_range getProgram() {
    return getODSResults(1);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getMlirModuleAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().mlir_module);
  }

  ::llvm::StringRef getMlirModule();
  ::mlir::StringAttr getMetadataAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().metadata);
  }

  ::llvm::StringRef getMetadata();
  size_t getNumDynamicShapes();
  size_t getNumComputations();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setMlirModuleAttr(::mlir::StringAttr attr) {
    getProperties().mlir_module = attr;
  }

  void setMlirModule(::std::optional<::llvm::StringRef> attrValue);
  void setMetadataAttr(::mlir::StringAttr attr) {
    getProperties().metadata = attr;
  }

  void setMetadata(::llvm::StringRef attrValue);
  ::mlir::Attribute removeMlirModuleAttr() {
      auto &attr = getProperties().mlir_module;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type compilation_status, ::mlir::TypeRange program, ::mlir::ValueRange dynamic_shapes, /*optional*/::mlir::StringAttr mlir_module, ::mlir::StringAttr metadata);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange dynamic_shapes, /*optional*/::mlir::StringAttr mlir_module, ::mlir::StringAttr metadata);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type compilation_status, ::mlir::TypeRange program, ::mlir::ValueRange dynamic_shapes, /*optional*/::llvm::StringRef mlir_module, ::llvm::StringRef metadata);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange dynamic_shapes, /*optional*/::llvm::StringRef mlir_module, ::llvm::StringRef metadata);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 4 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_TPUCompileMlirOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_TPUDeviceOrdinalPlaceholderOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _TPUDeviceOrdinalPlaceholderOpGenericAdaptorBase {
public:
  struct Properties {
    using logical_coreTy = ::mlir::IntegerAttr;
    logical_coreTy logical_core;

    auto getLogicalCore() {
      auto &propStorage = this->logical_core;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setLogicalCore(const ::mlir::IntegerAttr &propValue) {
      this->logical_core = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.logical_core == this->logical_core &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _TPUDeviceOrdinalPlaceholderOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._TPUDeviceOrdinalPlaceholder", odsAttrs.getContext());
  }

  _TPUDeviceOrdinalPlaceholderOpGenericAdaptorBase(_TPUDeviceOrdinalPlaceholderOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::IntegerAttr getLogicalCoreAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().logical_core);
    return attr;
  }

  uint64_t getLogicalCore();
};
} // namespace detail
template <typename RangeT>
class _TPUDeviceOrdinalPlaceholderOpGenericAdaptor : public detail::_TPUDeviceOrdinalPlaceholderOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_TPUDeviceOrdinalPlaceholderOpGenericAdaptorBase;
public:
  _TPUDeviceOrdinalPlaceholderOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _TPUDeviceOrdinalPlaceholderOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _TPUDeviceOrdinalPlaceholderOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _TPUDeviceOrdinalPlaceholderOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _TPUDeviceOrdinalPlaceholderOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _TPUDeviceOrdinalPlaceholderOpGenericAdaptor(RangeT values, const _TPUDeviceOrdinalPlaceholderOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _TPUDeviceOrdinalPlaceholderOp, typename = std::enable_if_t<std::is_same_v<LateInst, _TPUDeviceOrdinalPlaceholderOp>>>
  _TPUDeviceOrdinalPlaceholderOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _TPUDeviceOrdinalPlaceholderOpAdaptor : public _TPUDeviceOrdinalPlaceholderOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _TPUDeviceOrdinalPlaceholderOpGenericAdaptor::_TPUDeviceOrdinalPlaceholderOpGenericAdaptor;
  _TPUDeviceOrdinalPlaceholderOpAdaptor(_TPUDeviceOrdinalPlaceholderOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _TPUDeviceOrdinalPlaceholderOp : public ::mlir::Op<_TPUDeviceOrdinalPlaceholderOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::TensorType>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _TPUDeviceOrdinalPlaceholderOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _TPUDeviceOrdinalPlaceholderOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("logical_core")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getLogicalCoreAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getLogicalCoreAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._TPUDeviceOrdinalPlaceholder");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getDeviceOrdinal() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::IntegerAttr getLogicalCoreAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().logical_core);
  }

  uint64_t getLogicalCore();
  void setLogicalCoreAttr(::mlir::IntegerAttr attr) {
    getProperties().logical_core = attr;
  }

  void setLogicalCore(uint64_t attrValue);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type device_ordinal, ::mlir::IntegerAttr logical_core);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::IntegerAttr logical_core);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type device_ordinal, uint64_t logical_core);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, uint64_t logical_core);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 1 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_TPUDeviceOrdinalPlaceholderOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_UnaryOpsCompositionOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _UnaryOpsCompositionOpGenericAdaptorBase {
public:
  struct Properties {
    using op_namesTy = ::mlir::ArrayAttr;
    op_namesTy op_names;

    auto getOpNames() {
      auto &propStorage = this->op_names;
      return ::llvm::cast<::mlir::ArrayAttr>(propStorage);
    }
    void setOpNames(const ::mlir::ArrayAttr &propValue) {
      this->op_names = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.op_names == this->op_names &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _UnaryOpsCompositionOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._UnaryOpsComposition", odsAttrs.getContext());
  }

  _UnaryOpsCompositionOpGenericAdaptorBase(_UnaryOpsCompositionOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::ArrayAttr getOpNamesAttr() {
    auto attr = ::llvm::cast<::mlir::ArrayAttr>(getProperties().op_names);
    return attr;
  }

  ::mlir::ArrayAttr getOpNames();
};
} // namespace detail
template <typename RangeT>
class _UnaryOpsCompositionOpGenericAdaptor : public detail::_UnaryOpsCompositionOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_UnaryOpsCompositionOpGenericAdaptorBase;
public:
  _UnaryOpsCompositionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _UnaryOpsCompositionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _UnaryOpsCompositionOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _UnaryOpsCompositionOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _UnaryOpsCompositionOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _UnaryOpsCompositionOpGenericAdaptor(RangeT values, const _UnaryOpsCompositionOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _UnaryOpsCompositionOp, typename = std::enable_if_t<std::is_same_v<LateInst, _UnaryOpsCompositionOp>>>
  _UnaryOpsCompositionOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getX() {
    return (*getODSOperands(0).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _UnaryOpsCompositionOpAdaptor : public _UnaryOpsCompositionOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _UnaryOpsCompositionOpGenericAdaptor::_UnaryOpsCompositionOpGenericAdaptor;
  _UnaryOpsCompositionOpAdaptor(_UnaryOpsCompositionOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _UnaryOpsCompositionOp : public ::mlir::Op<_UnaryOpsCompositionOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::TensorType>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::OneOperand, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::ConditionallySpeculatable::Trait, ::mlir::OpTrait::AlwaysSpeculatableImplTrait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::InferTypeOpInterface::Trait, ::mlir::InferShapedTypeOpInterface::Trait, ::mlir::OpTrait::InferTensorType, ::mlir::OpTrait::TF::SameOperandsAndResultTypeResolveRef, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _UnaryOpsCompositionOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _UnaryOpsCompositionOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("T"), ::llvm::StringRef("op_names")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getOpNamesAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getOpNamesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._UnaryOpsComposition");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getX() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::OpOperand &getXMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getY() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::ArrayAttr getOpNamesAttr() {
    return ::llvm::cast<::mlir::ArrayAttr>(getProperties().op_names);
  }

  ::mlir::ArrayAttr getOpNames();
  ::mlir::Type getT();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setOpNamesAttr(::mlir::ArrayAttr attr) {
    getProperties().op_names = attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type y, ::mlir::Value x, ::mlir::ArrayAttr op_names);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Value x, ::mlir::ArrayAttr op_names);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::Value x, ::mlir::ArrayAttr op_names);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  static ::llvm::LogicalResult inferReturnTypes(::mlir::MLIRContext *context, ::std::optional<::mlir::Location> location, ::mlir::ValueRange operands, ::mlir::DictionaryAttr attributes, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions, ::llvm::SmallVectorImpl<::mlir::Type>&inferredReturnTypes);
  static ::llvm::LogicalResult inferReturnTypeComponents(::mlir::MLIRContext*context, ::std::optional<::mlir::Location> location, ::mlir::ValueShapeRange operands, ::mlir::DictionaryAttr attributes, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions, ::llvm::SmallVectorImpl<::mlir::ShapedTypeComponents>&inferredReturnShapes);
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 2 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  static bool isCompatibleReturnTypes(TypeRange inferred, TypeRange actual) {
    return ArraysAreCastCompatible(inferred, actual);
  }
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_UnaryOpsCompositionOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_XlaCompileMlirPlaceholderProgramKeyOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptorBase(::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._XlaCompileMlirPlaceholderProgramKey", odsAttrs.getContext());
  }

  _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptorBase(::mlir::Operation *op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), odsRegions(op->getRegions()) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

};
} // namespace detail
template <typename RangeT>
class _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptor : public detail::_XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptorBase;
public:
  _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptor(RangeT values, const _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _XlaCompileMlirPlaceholderProgramKeyOp, typename = std::enable_if_t<std::is_same_v<LateInst, _XlaCompileMlirPlaceholderProgramKeyOp>>>
  _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _XlaCompileMlirPlaceholderProgramKeyOpAdaptor : public _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptor::_XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptor;
  _XlaCompileMlirPlaceholderProgramKeyOpAdaptor(_XlaCompileMlirPlaceholderProgramKeyOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _XlaCompileMlirPlaceholderProgramKeyOp : public ::mlir::Op<_XlaCompileMlirPlaceholderProgramKeyOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::OneResult, ::mlir::OpTrait::OneTypedResult<::mlir::TensorType>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::ZeroOperands, ::mlir::OpTrait::OpInvariants, ::mlir::MemoryEffectOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _XlaCompileMlirPlaceholderProgramKeyOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _XlaCompileMlirPlaceholderProgramKeyOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    return {};
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._XlaCompileMlirPlaceholderProgramKey");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getProgram() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type program);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_XlaCompileMlirPlaceholderProgramKeyOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_XlaCompileOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _XlaCompileOpGenericAdaptorBase {
public:
  struct Properties {
    using functionTy = ::mlir::SymbolRefAttr;
    functionTy function;

    auto getFunction() {
      auto &propStorage = this->function;
      return ::llvm::cast<::mlir::SymbolRefAttr>(propStorage);
    }
    void setFunction(const ::mlir::SymbolRefAttr &propValue) {
      this->function = propValue;
    }
    using must_compileTy = ::mlir::BoolAttr;
    must_compileTy must_compile;

    auto getMustCompile() {
      auto &propStorage = this->must_compile;
      return ::llvm::cast<::mlir::BoolAttr>(propStorage);
    }
    void setMustCompile(const ::mlir::BoolAttr &propValue) {
      this->must_compile = propValue;
    }
    using operandSegmentSizesTy = std::array<int32_t, 3>;
    operandSegmentSizesTy operandSegmentSizes;
    ::llvm::ArrayRef<int32_t> getOperandSegmentSizes() const {
      auto &propStorage = this->operandSegmentSizes;
      return propStorage;
    }
    void setOperandSegmentSizes(::llvm::ArrayRef<int32_t> propValue) {
      auto &propStorage = this->operandSegmentSizes;
      ::llvm::copy(propValue, propStorage.begin());
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.function == this->function &&
        rhs.must_compile == this->must_compile &&
        rhs.operandSegmentSizes == this->operandSegmentSizes &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _XlaCompileOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._XlaCompile", odsAttrs.getContext());
  }

  _XlaCompileOpGenericAdaptorBase(_XlaCompileOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::BoolAttr getMustCompileAttr() {
    auto attr = ::llvm::cast<::mlir::BoolAttr>(getProperties().must_compile);
    return attr;
  }

  bool getMustCompile();
  ::mlir::SymbolRefAttr getFunctionAttr() {
    auto attr = ::llvm::cast<::mlir::SymbolRefAttr>(getProperties().function);
    return attr;
  }

  ::mlir::SymbolRefAttr getFunction();
};
} // namespace detail
template <typename RangeT>
class _XlaCompileOpGenericAdaptor : public detail::_XlaCompileOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_XlaCompileOpGenericAdaptorBase;
public:
  _XlaCompileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _XlaCompileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _XlaCompileOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _XlaCompileOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs) : _XlaCompileOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _XlaCompileOpGenericAdaptor(RangeT values, const _XlaCompileOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _XlaCompileOp, typename = std::enable_if_t<std::is_same_v<LateInst, _XlaCompileOp>>>
  _XlaCompileOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getConstants() {
    return getODSOperands(0);
  }

  RangeT getArgs() {
    return getODSOperands(1);
  }

  RangeT getResources() {
    return getODSOperands(2);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _XlaCompileOpAdaptor : public _XlaCompileOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _XlaCompileOpGenericAdaptor::_XlaCompileOpGenericAdaptor;
  _XlaCompileOpAdaptor(_XlaCompileOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _XlaCompileOp : public ::mlir::Op<_XlaCompileOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::NResults<2>::Impl, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::AttrSizedOperandSegments, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::OpAsmOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _XlaCompileOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _XlaCompileOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Nresources"), ::llvm::StringRef("Targs"), ::llvm::StringRef("Tconstants"), ::llvm::StringRef("function"), ::llvm::StringRef("must_compile"), ::llvm::StringRef("operandSegmentSizes")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getNresourcesAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getNresourcesAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getTargsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getTargsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getTconstantsAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getTconstantsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getFunctionAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getFunctionAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getMustCompileAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getMustCompileAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getOperandSegmentSizesAttrName() {
   return (*this)->getName().getAttributeNames().back();
  }

  static ::mlir::StringAttr getOperandSegmentSizesAttrName(::mlir::OperationName name) {
   return name.getAttributeNames().back();
  }

  void getAsmResultNames(::mlir::OpAsmSetValueNameFn setNameFn);
  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._XlaCompile");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getConstants() {
    return getODSOperands(0);
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(1);
  }

  ::mlir::Operation::operand_range getResources() {
    return getODSOperands(2);
  }

  ::mlir::MutableOperandRange getConstantsMutable();
  ::mlir::MutableOperandRange getArgsMutable();
  ::mlir::MutableOperandRange getResourcesMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getKey() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(0).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getCompilationSuccessful() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSResults(1).begin());
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::BoolAttr getMustCompileAttr() {
    return ::llvm::cast<::mlir::BoolAttr>(getProperties().must_compile);
  }

  bool getMustCompile();
  ::mlir::SymbolRefAttr getFunctionAttr() {
    return ::llvm::cast<::mlir::SymbolRefAttr>(getProperties().function);
  }

  ::mlir::SymbolRefAttr getFunction();
  size_t getNresources();
  mlir::OperandElementTypeRange getTargs();
  mlir::OperandElementTypeRange getTconstants();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setMustCompileAttr(::mlir::BoolAttr attr) {
    getProperties().must_compile = attr;
  }

  void setMustCompile(bool attrValue);
  void setFunctionAttr(::mlir::SymbolRefAttr attr) {
    getProperties().function = attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type key, ::mlir::Type compilation_successful, ::mlir::ValueRange constants, ::mlir::ValueRange args, ::mlir::ValueRange resources, ::mlir::BoolAttr must_compile, ::mlir::SymbolRefAttr function);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange constants, ::mlir::ValueRange args, ::mlir::ValueRange resources, ::mlir::BoolAttr must_compile, ::mlir::SymbolRefAttr function);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::Type key, ::mlir::Type compilation_successful, ::mlir::ValueRange constants, ::mlir::ValueRange args, ::mlir::ValueRange resources, bool must_compile, ::mlir::SymbolRefAttr function);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange constants, ::mlir::ValueRange args, ::mlir::ValueRange resources, bool must_compile, ::mlir::SymbolRefAttr function);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 5 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_XlaCompileOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_XlaHostComputeMlirOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _XlaHostComputeMlirOpGenericAdaptorBase {
public:
  struct Properties {
    using host_mlir_moduleTy = ::mlir::StringAttr;
    host_mlir_moduleTy host_mlir_module;

    auto getHostMlirModule() {
      auto &propStorage = this->host_mlir_module;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setHostMlirModule(const ::mlir::StringAttr &propValue) {
      this->host_mlir_module = propValue;
    }
    using manual_shardingTy = ::mlir::BoolAttr;
    manual_shardingTy manual_sharding;

    auto getManualSharding() {
      auto &propStorage = this->manual_sharding;
      return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(propStorage);
    }
    void setManualSharding(const ::mlir::BoolAttr &propValue) {
      this->manual_sharding = propValue;
    }
    using recv_keyTy = ::mlir::StringAttr;
    recv_keyTy recv_key;

    auto getRecvKey() {
      auto &propStorage = this->recv_key;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setRecvKey(const ::mlir::StringAttr &propValue) {
      this->recv_key = propValue;
    }
    using send_keyTy = ::mlir::StringAttr;
    send_keyTy send_key;

    auto getSendKey() {
      auto &propStorage = this->send_key;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setSendKey(const ::mlir::StringAttr &propValue) {
      this->send_key = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.host_mlir_module == this->host_mlir_module &&
        rhs.manual_sharding == this->manual_sharding &&
        rhs.recv_key == this->recv_key &&
        rhs.send_key == this->send_key &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _XlaHostComputeMlirOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._XlaHostComputeMlir", odsAttrs.getContext());
  }

  _XlaHostComputeMlirOpGenericAdaptorBase(_XlaHostComputeMlirOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getSendKeyAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().send_key);
    return attr;
  }

  ::llvm::StringRef getSendKey();
  ::mlir::StringAttr getRecvKeyAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().recv_key);
    return attr;
  }

  ::llvm::StringRef getRecvKey();
  ::mlir::StringAttr getHostMlirModuleAttr();
  ::llvm::StringRef getHostMlirModule();
  ::mlir::BoolAttr getManualShardingAttr();
  bool getManualSharding();
};
} // namespace detail
template <typename RangeT>
class _XlaHostComputeMlirOpGenericAdaptor : public detail::_XlaHostComputeMlirOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_XlaHostComputeMlirOpGenericAdaptorBase;
public:
  _XlaHostComputeMlirOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _XlaHostComputeMlirOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _XlaHostComputeMlirOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _XlaHostComputeMlirOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _XlaHostComputeMlirOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _XlaHostComputeMlirOpGenericAdaptor(RangeT values, const _XlaHostComputeMlirOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _XlaHostComputeMlirOp, typename = std::enable_if_t<std::is_same_v<LateInst, _XlaHostComputeMlirOp>>>
  _XlaHostComputeMlirOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getInputs() {
    return getODSOperands(0);
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _XlaHostComputeMlirOpAdaptor : public _XlaHostComputeMlirOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _XlaHostComputeMlirOpGenericAdaptor::_XlaHostComputeMlirOpGenericAdaptor;
  _XlaHostComputeMlirOpAdaptor(_XlaHostComputeMlirOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _XlaHostComputeMlirOp : public ::mlir::Op<_XlaHostComputeMlirOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::VariadicResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::VariadicOperands, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _XlaHostComputeMlirOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _XlaHostComputeMlirOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Tinputs"), ::llvm::StringRef("Toutputs"), ::llvm::StringRef("host_mlir_module"), ::llvm::StringRef("manual_sharding"), ::llvm::StringRef("recv_key"), ::llvm::StringRef("send_key")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTinputsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTinputsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getToutputsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getToutputsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getHostMlirModuleAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getHostMlirModuleAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getManualShardingAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getManualShardingAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  ::mlir::StringAttr getRecvKeyAttrName() {
    return getAttributeNameForIndex(4);
  }

  static ::mlir::StringAttr getRecvKeyAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 4);
  }

  ::mlir::StringAttr getSendKeyAttrName() {
    return getAttributeNameForIndex(5);
  }

  static ::mlir::StringAttr getSendKeyAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 5);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._XlaHostComputeMlir");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getInputs() {
    return getODSOperands(0);
  }

  ::mlir::MutableOperandRange getInputsMutable();
  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOutputs() {
    return getODSResults(0);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getSendKeyAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().send_key);
  }

  ::llvm::StringRef getSendKey();
  ::mlir::StringAttr getRecvKeyAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().recv_key);
  }

  ::llvm::StringRef getRecvKey();
  ::mlir::StringAttr getHostMlirModuleAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().host_mlir_module);
  }

  ::llvm::StringRef getHostMlirModule();
  ::mlir::BoolAttr getManualShardingAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::BoolAttr>(getProperties().manual_sharding);
  }

  bool getManualSharding();
  mlir::OperandElementTypeRange getTinputs();
  mlir::ResultElementTypeRange getToutputs();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setSendKeyAttr(::mlir::StringAttr attr) {
    getProperties().send_key = attr;
  }

  void setSendKey(::llvm::StringRef attrValue);
  void setRecvKeyAttr(::mlir::StringAttr attr) {
    getProperties().recv_key = attr;
  }

  void setRecvKey(::llvm::StringRef attrValue);
  void setHostMlirModuleAttr(::mlir::StringAttr attr) {
    getProperties().host_mlir_module = attr;
  }

  void setHostMlirModule(::std::optional<::llvm::StringRef> attrValue);
  void setManualShardingAttr(::mlir::BoolAttr attr) {
    getProperties().manual_sharding = attr;
  }

  void setManualSharding(::std::optional<bool> attrValue);
  ::mlir::Attribute removeHostMlirModuleAttr() {
      auto &attr = getProperties().host_mlir_module;
      attr = {};
      return attr;
  }

  ::mlir::Attribute removeManualShardingAttr() {
      auto &attr = getProperties().manual_sharding;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outputs, ::mlir::ValueRange inputs, ::mlir::StringAttr send_key, ::mlir::StringAttr recv_key, /*optional*/::mlir::StringAttr host_mlir_module = nullptr, /*optional*/::mlir::BoolAttr manual_sharding = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outputs, ::mlir::ValueRange inputs, ::llvm::StringRef send_key, ::llvm::StringRef recv_key, /*optional*/::llvm::StringRef host_mlir_module = "", /*optional*/bool manual_sharding = false);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  ::llvm::LogicalResult verify();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 6 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
  func::FuncOp GetHostFunc(mlir::OwningOpRef<mlir::ModuleOp>* mlir_module);
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_XlaHostComputeMlirOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_XlaRecvAtHostOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _XlaRecvAtHostOpGenericAdaptorBase {
public:
  struct Properties {
    using device_ordinalTy = ::mlir::IntegerAttr;
    device_ordinalTy device_ordinal;

    auto getDeviceOrdinal() {
      auto &propStorage = this->device_ordinal;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setDeviceOrdinal(const ::mlir::IntegerAttr &propValue) {
      this->device_ordinal = propValue;
    }
    using device_typeTy = ::mlir::StringAttr;
    device_typeTy device_type;

    auto getDeviceType() {
      auto &propStorage = this->device_type;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setDeviceType(const ::mlir::StringAttr &propValue) {
      this->device_type = propValue;
    }
    using keyTy = ::mlir::StringAttr;
    keyTy key;

    auto getKey() {
      auto &propStorage = this->key;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setKey(const ::mlir::StringAttr &propValue) {
      this->key = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.device_ordinal == this->device_ordinal &&
        rhs.device_type == this->device_type &&
        rhs.key == this->key &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _XlaRecvAtHostOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._XlaRecvAtHost", odsAttrs.getContext());
  }

  _XlaRecvAtHostOpGenericAdaptorBase(_XlaRecvAtHostOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getKeyAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().key);
    return attr;
  }

  ::llvm::StringRef getKey();
  ::mlir::IntegerAttr getDeviceOrdinalAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().device_ordinal);
    return attr;
  }

  uint64_t getDeviceOrdinal();
  ::mlir::StringAttr getDeviceTypeAttr();
  ::llvm::StringRef getDeviceType();
};
} // namespace detail
template <typename RangeT>
class _XlaRecvAtHostOpGenericAdaptor : public detail::_XlaRecvAtHostOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_XlaRecvAtHostOpGenericAdaptorBase;
public:
  _XlaRecvAtHostOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _XlaRecvAtHostOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _XlaRecvAtHostOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _XlaRecvAtHostOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _XlaRecvAtHostOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _XlaRecvAtHostOpGenericAdaptor(RangeT values, const _XlaRecvAtHostOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _XlaRecvAtHostOp, typename = std::enable_if_t<std::is_same_v<LateInst, _XlaRecvAtHostOp>>>
  _XlaRecvAtHostOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getDynamicKey() {
    return (*getODSOperands(0).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _XlaRecvAtHostOpAdaptor : public _XlaRecvAtHostOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _XlaRecvAtHostOpGenericAdaptor::_XlaRecvAtHostOpGenericAdaptor;
  _XlaRecvAtHostOpAdaptor(_XlaRecvAtHostOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _XlaRecvAtHostOp : public ::mlir::Op<_XlaRecvAtHostOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::VariadicResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::OneOperand, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, GetResourceInstanceInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _XlaRecvAtHostOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _XlaRecvAtHostOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Toutputs"), ::llvm::StringRef("device_ordinal"), ::llvm::StringRef("device_type"), ::llvm::StringRef("key")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getToutputsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getToutputsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getDeviceOrdinalAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getDeviceOrdinalAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getDeviceTypeAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getDeviceTypeAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getKeyAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getKeyAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._XlaRecvAtHost");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getDynamicKey() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::OpOperand &getDynamicKeyMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOutputs() {
    return getODSResults(0);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getKeyAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().key);
  }

  ::llvm::StringRef getKey();
  ::mlir::IntegerAttr getDeviceOrdinalAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().device_ordinal);
  }

  uint64_t getDeviceOrdinal();
  ::mlir::StringAttr getDeviceTypeAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().device_type);
  }

  ::llvm::StringRef getDeviceType();
  mlir::ResultElementTypeRange getToutputs();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setKeyAttr(::mlir::StringAttr attr) {
    getProperties().key = attr;
  }

  void setKey(::llvm::StringRef attrValue);
  void setDeviceOrdinalAttr(::mlir::IntegerAttr attr) {
    getProperties().device_ordinal = attr;
  }

  void setDeviceOrdinal(uint64_t attrValue);
  void setDeviceTypeAttr(::mlir::StringAttr attr) {
    getProperties().device_type = attr;
  }

  void setDeviceType(::std::optional<::llvm::StringRef> attrValue);
  ::mlir::Attribute removeDeviceTypeAttr() {
      auto &attr = getProperties().device_type;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outputs, ::mlir::Value dynamic_key, ::mlir::StringAttr key, ::mlir::IntegerAttr device_ordinal, /*optional*/::mlir::StringAttr device_type = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outputs, ::mlir::Value dynamic_key, ::llvm::StringRef key, uint64_t device_ordinal, /*optional*/::llvm::StringRef device_type = "TPU");
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  std::optional<std::string> GetResourceInstanceStr();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 4 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_XlaRecvAtHostOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_XlaRecvAtHostV2Op declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _XlaRecvAtHostV2OpGenericAdaptorBase {
public:
  struct Properties {
    using device_typeTy = ::mlir::StringAttr;
    device_typeTy device_type;

    auto getDeviceType() {
      auto &propStorage = this->device_type;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setDeviceType(const ::mlir::StringAttr &propValue) {
      this->device_type = propValue;
    }
    using keyTy = ::mlir::StringAttr;
    keyTy key;

    auto getKey() {
      auto &propStorage = this->key;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setKey(const ::mlir::StringAttr &propValue) {
      this->key = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.device_type == this->device_type &&
        rhs.key == this->key &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _XlaRecvAtHostV2OpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._XlaRecvAtHostV2", odsAttrs.getContext());
  }

  _XlaRecvAtHostV2OpGenericAdaptorBase(_XlaRecvAtHostV2Op op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize) {
    return {index, 1};
  }

  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getKeyAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().key);
    return attr;
  }

  ::llvm::StringRef getKey();
  ::mlir::StringAttr getDeviceTypeAttr();
  ::llvm::StringRef getDeviceType();
};
} // namespace detail
template <typename RangeT>
class _XlaRecvAtHostV2OpGenericAdaptor : public detail::_XlaRecvAtHostV2OpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_XlaRecvAtHostV2OpGenericAdaptorBase;
public:
  _XlaRecvAtHostV2OpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _XlaRecvAtHostV2OpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _XlaRecvAtHostV2OpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _XlaRecvAtHostV2OpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _XlaRecvAtHostV2OpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _XlaRecvAtHostV2OpGenericAdaptor(RangeT values, const _XlaRecvAtHostV2OpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _XlaRecvAtHostV2Op, typename = std::enable_if_t<std::is_same_v<LateInst, _XlaRecvAtHostV2Op>>>
  _XlaRecvAtHostV2OpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  ValueT getDynamicKey() {
    return (*getODSOperands(0).begin());
  }

  ValueT getDeviceOrdinal() {
    return (*getODSOperands(1).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _XlaRecvAtHostV2OpAdaptor : public _XlaRecvAtHostV2OpGenericAdaptor<::mlir::ValueRange> {
public:
  using _XlaRecvAtHostV2OpGenericAdaptor::_XlaRecvAtHostV2OpGenericAdaptor;
  _XlaRecvAtHostV2OpAdaptor(_XlaRecvAtHostV2Op op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _XlaRecvAtHostV2Op : public ::mlir::Op<_XlaRecvAtHostV2Op, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::VariadicResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::NOperands<2>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, GetResourceInstanceInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _XlaRecvAtHostV2OpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _XlaRecvAtHostV2OpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Toutputs"), ::llvm::StringRef("device_type"), ::llvm::StringRef("key")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getToutputsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getToutputsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getDeviceTypeAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getDeviceTypeAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getKeyAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getKeyAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._XlaRecvAtHostV2");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::TypedValue<::mlir::TensorType> getDynamicKey() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(0).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getDeviceOrdinal() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(1).begin());
  }

  ::mlir::OpOperand &getDynamicKeyMutable() {
    auto range = getODSOperandIndexAndLength(0);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getDeviceOrdinalMutable() {
    auto range = getODSOperandIndexAndLength(1);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getOutputs() {
    return getODSResults(0);
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getKeyAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().key);
  }

  ::llvm::StringRef getKey();
  ::mlir::StringAttr getDeviceTypeAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().device_type);
  }

  ::llvm::StringRef getDeviceType();
  mlir::ResultElementTypeRange getToutputs();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setKeyAttr(::mlir::StringAttr attr) {
    getProperties().key = attr;
  }

  void setKey(::llvm::StringRef attrValue);
  void setDeviceTypeAttr(::mlir::StringAttr attr) {
    getProperties().device_type = attr;
  }

  void setDeviceType(::std::optional<::llvm::StringRef> attrValue);
  ::mlir::Attribute removeDeviceTypeAttr() {
      auto &attr = getProperties().device_type;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outputs, ::mlir::Value dynamic_key, ::mlir::Value device_ordinal, ::mlir::StringAttr key, /*optional*/::mlir::StringAttr device_type = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange outputs, ::mlir::Value dynamic_key, ::mlir::Value device_ordinal, ::llvm::StringRef key, /*optional*/::llvm::StringRef device_type = "TPU");
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  std::optional<std::string> GetResourceInstanceStr();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 3 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_XlaRecvAtHostV2Op)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_XlaRunOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _XlaRunOpGenericAdaptorBase {
public:
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  ::mlir::RegionRange odsRegions;
public:
  _XlaRunOpGenericAdaptorBase(::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._XlaRun", odsAttrs.getContext());
  }

  _XlaRunOpGenericAdaptorBase(::mlir::Operation *op) : odsAttrs(op->getRawDictionaryAttrs()), odsOpName(op->getName()), odsRegions(op->getRegions()) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

};
} // namespace detail
template <typename RangeT>
class _XlaRunOpGenericAdaptor : public detail::_XlaRunOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_XlaRunOpGenericAdaptorBase;
public:
  _XlaRunOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = {}, const ::mlir::EmptyProperties &properties = {}, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _XlaRunOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _XlaRunOpGenericAdaptor(values, attrs, (properties ? *properties.as<::mlir::EmptyProperties *>() : ::mlir::EmptyProperties{}), regions) {}

  _XlaRunOpGenericAdaptor(RangeT values, const _XlaRunOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _XlaRunOp, typename = std::enable_if_t<std::is_same_v<LateInst, _XlaRunOp>>>
  _XlaRunOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getArgs() {
    return getODSOperands(0);
  }

  ValueT getKey() {
    return (*getODSOperands(1).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _XlaRunOpAdaptor : public _XlaRunOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _XlaRunOpGenericAdaptor::_XlaRunOpGenericAdaptor;
  _XlaRunOpAdaptor(_XlaRunOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _XlaRunOp : public ::mlir::Op<_XlaRunOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::VariadicResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _XlaRunOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _XlaRunOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Targs"), ::llvm::StringRef("Tresults")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTargsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTargsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getTresultsAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getTresultsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._XlaRun");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getArgs() {
    return getODSOperands(0);
  }

  ::mlir::TypedValue<::mlir::TensorType> getKey() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(1).begin());
  }

  ::mlir::MutableOperandRange getArgsMutable();
  ::mlir::OpOperand &getKeyMutable() {
    auto range = getODSOperandIndexAndLength(1);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index);
  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::result_range getResults() {
    return getODSResults(0);
  }

  mlir::OperandElementTypeRange getTargs();
  mlir::ResultElementTypeRange getTresults();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange results, ::mlir::ValueRange args, ::mlir::Value key);
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 2 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_XlaRunOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_XlaSendFromHostOp declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _XlaSendFromHostOpGenericAdaptorBase {
public:
  struct Properties {
    using device_ordinalTy = ::mlir::IntegerAttr;
    device_ordinalTy device_ordinal;

    auto getDeviceOrdinal() {
      auto &propStorage = this->device_ordinal;
      return ::llvm::cast<::mlir::IntegerAttr>(propStorage);
    }
    void setDeviceOrdinal(const ::mlir::IntegerAttr &propValue) {
      this->device_ordinal = propValue;
    }
    using device_typeTy = ::mlir::StringAttr;
    device_typeTy device_type;

    auto getDeviceType() {
      auto &propStorage = this->device_type;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setDeviceType(const ::mlir::StringAttr &propValue) {
      this->device_type = propValue;
    }
    using keyTy = ::mlir::StringAttr;
    keyTy key;

    auto getKey() {
      auto &propStorage = this->key;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setKey(const ::mlir::StringAttr &propValue) {
      this->key = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.device_ordinal == this->device_ordinal &&
        rhs.device_type == this->device_type &&
        rhs.key == this->key &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _XlaSendFromHostOpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._XlaSendFromHost", odsAttrs.getContext());
  }

  _XlaSendFromHostOpGenericAdaptorBase(_XlaSendFromHostOp op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getKeyAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().key);
    return attr;
  }

  ::llvm::StringRef getKey();
  ::mlir::IntegerAttr getDeviceOrdinalAttr() {
    auto attr = ::llvm::cast<::mlir::IntegerAttr>(getProperties().device_ordinal);
    return attr;
  }

  uint64_t getDeviceOrdinal();
  ::mlir::StringAttr getDeviceTypeAttr();
  ::llvm::StringRef getDeviceType();
};
} // namespace detail
template <typename RangeT>
class _XlaSendFromHostOpGenericAdaptor : public detail::_XlaSendFromHostOpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_XlaSendFromHostOpGenericAdaptorBase;
public:
  _XlaSendFromHostOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _XlaSendFromHostOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _XlaSendFromHostOpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _XlaSendFromHostOpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _XlaSendFromHostOpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _XlaSendFromHostOpGenericAdaptor(RangeT values, const _XlaSendFromHostOpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _XlaSendFromHostOp, typename = std::enable_if_t<std::is_same_v<LateInst, _XlaSendFromHostOp>>>
  _XlaSendFromHostOpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getInputs() {
    return getODSOperands(0);
  }

  ValueT getDynamicKey() {
    return (*getODSOperands(1).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _XlaSendFromHostOpAdaptor : public _XlaSendFromHostOpGenericAdaptor<::mlir::ValueRange> {
public:
  using _XlaSendFromHostOpGenericAdaptor::_XlaSendFromHostOpGenericAdaptor;
  _XlaSendFromHostOpAdaptor(_XlaSendFromHostOp op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _XlaSendFromHostOp : public ::mlir::Op<_XlaSendFromHostOp, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<1>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, GetResourceInstanceInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _XlaSendFromHostOpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _XlaSendFromHostOpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Tinputs"), ::llvm::StringRef("device_ordinal"), ::llvm::StringRef("device_type"), ::llvm::StringRef("key")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTinputsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTinputsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getDeviceOrdinalAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getDeviceOrdinalAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getDeviceTypeAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getDeviceTypeAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  ::mlir::StringAttr getKeyAttrName() {
    return getAttributeNameForIndex(3);
  }

  static ::mlir::StringAttr getKeyAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 3);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._XlaSendFromHost");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getInputs() {
    return getODSOperands(0);
  }

  ::mlir::TypedValue<::mlir::TensorType> getDynamicKey() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(1).begin());
  }

  ::mlir::MutableOperandRange getInputsMutable();
  ::mlir::OpOperand &getDynamicKeyMutable() {
    auto range = getODSOperandIndexAndLength(1);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getKeyAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().key);
  }

  ::llvm::StringRef getKey();
  ::mlir::IntegerAttr getDeviceOrdinalAttr() {
    return ::llvm::cast<::mlir::IntegerAttr>(getProperties().device_ordinal);
  }

  uint64_t getDeviceOrdinal();
  ::mlir::StringAttr getDeviceTypeAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().device_type);
  }

  ::llvm::StringRef getDeviceType();
  mlir::OperandElementTypeRange getTinputs();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setKeyAttr(::mlir::StringAttr attr) {
    getProperties().key = attr;
  }

  void setKey(::llvm::StringRef attrValue);
  void setDeviceOrdinalAttr(::mlir::IntegerAttr attr) {
    getProperties().device_ordinal = attr;
  }

  void setDeviceOrdinal(uint64_t attrValue);
  void setDeviceTypeAttr(::mlir::StringAttr attr) {
    getProperties().device_type = attr;
  }

  void setDeviceType(::std::optional<::llvm::StringRef> attrValue);
  ::mlir::Attribute removeDeviceTypeAttr() {
      auto &attr = getProperties().device_type;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange inputs, ::mlir::Value dynamic_key, ::mlir::StringAttr key, ::mlir::IntegerAttr device_ordinal, /*optional*/::mlir::StringAttr device_type = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange inputs, ::mlir::Value dynamic_key, ::mlir::StringAttr key, ::mlir::IntegerAttr device_ordinal, /*optional*/::mlir::StringAttr device_type = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange inputs, ::mlir::Value dynamic_key, ::llvm::StringRef key, uint64_t device_ordinal, /*optional*/::llvm::StringRef device_type = "TPU");
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange inputs, ::mlir::Value dynamic_key, ::llvm::StringRef key, uint64_t device_ordinal, /*optional*/::llvm::StringRef device_type = "TPU");
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  std::optional<std::string> GetResourceInstanceStr();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 4 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_XlaSendFromHostOp)

namespace mlir {
namespace TF {

//===----------------------------------------------------------------------===//
// ::mlir::TF::_XlaSendFromHostV2Op declarations
//===----------------------------------------------------------------------===//

namespace detail {
class _XlaSendFromHostV2OpGenericAdaptorBase {
public:
  struct Properties {
    using device_typeTy = ::mlir::StringAttr;
    device_typeTy device_type;

    auto getDeviceType() {
      auto &propStorage = this->device_type;
      return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(propStorage);
    }
    void setDeviceType(const ::mlir::StringAttr &propValue) {
      this->device_type = propValue;
    }
    using keyTy = ::mlir::StringAttr;
    keyTy key;

    auto getKey() {
      auto &propStorage = this->key;
      return ::llvm::cast<::mlir::StringAttr>(propStorage);
    }
    void setKey(const ::mlir::StringAttr &propValue) {
      this->key = propValue;
    }
    bool operator==(const Properties &rhs) const {
      return 
        rhs.device_type == this->device_type &&
        rhs.key == this->key &&
        true;
    }
    bool operator!=(const Properties &rhs) const {
      return !(*this == rhs);
    }
  };
protected:
  ::mlir::DictionaryAttr odsAttrs;
  ::std::optional<::mlir::OperationName> odsOpName;
  Properties properties;
  ::mlir::RegionRange odsRegions;
public:
  _XlaSendFromHostV2OpGenericAdaptorBase(::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : odsAttrs(attrs), properties(properties), odsRegions(regions) {  if (odsAttrs)
      odsOpName.emplace("tf._XlaSendFromHostV2", odsAttrs.getContext());
  }

  _XlaSendFromHostV2OpGenericAdaptorBase(_XlaSendFromHostV2Op op);

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index, unsigned odsOperandsSize);
  const Properties &getProperties() {
    return properties;
  }

  ::mlir::DictionaryAttr getAttributes() {
    return odsAttrs;
  }

  ::mlir::StringAttr getKeyAttr() {
    auto attr = ::llvm::cast<::mlir::StringAttr>(getProperties().key);
    return attr;
  }

  ::llvm::StringRef getKey();
  ::mlir::StringAttr getDeviceTypeAttr();
  ::llvm::StringRef getDeviceType();
};
} // namespace detail
template <typename RangeT>
class _XlaSendFromHostV2OpGenericAdaptor : public detail::_XlaSendFromHostV2OpGenericAdaptorBase {
  using ValueT = ::llvm::detail::ValueOfRange<RangeT>;
  using Base = detail::_XlaSendFromHostV2OpGenericAdaptorBase;
public:
  _XlaSendFromHostV2OpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, const Properties &properties, ::mlir::RegionRange regions = {}) : Base(attrs, properties, regions), odsOperands(values) {}

  _XlaSendFromHostV2OpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs, ::mlir::OpaqueProperties properties, ::mlir::RegionRange regions = {}) : _XlaSendFromHostV2OpGenericAdaptor(values, attrs, (properties ? *properties.as<Properties *>() : Properties{}), regions) {}

  _XlaSendFromHostV2OpGenericAdaptor(RangeT values, ::mlir::DictionaryAttr attrs = nullptr) : _XlaSendFromHostV2OpGenericAdaptor(values, attrs, Properties{}, {}) {}

  _XlaSendFromHostV2OpGenericAdaptor(RangeT values, const _XlaSendFromHostV2OpGenericAdaptorBase &base) : Base(base), odsOperands(values) {}

  template <typename LateInst = _XlaSendFromHostV2Op, typename = std::enable_if_t<std::is_same_v<LateInst, _XlaSendFromHostV2Op>>>
  _XlaSendFromHostV2OpGenericAdaptor(RangeT values, LateInst op) : Base(op), odsOperands(values) {}

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index) {
    return Base::getODSOperandIndexAndLength(index, odsOperands.size());
  }

  RangeT getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(odsOperands.begin(), valueRange.first),
             std::next(odsOperands.begin(), valueRange.first + valueRange.second)};
  }

  RangeT getInputs() {
    return getODSOperands(0);
  }

  ValueT getDynamicKey() {
    return (*getODSOperands(1).begin());
  }

  ValueT getDeviceOrdinal() {
    return (*getODSOperands(2).begin());
  }

  RangeT getOperands() {
    return odsOperands;
  }

private:
  RangeT odsOperands;
};
class _XlaSendFromHostV2OpAdaptor : public _XlaSendFromHostV2OpGenericAdaptor<::mlir::ValueRange> {
public:
  using _XlaSendFromHostV2OpGenericAdaptor::_XlaSendFromHostV2OpGenericAdaptor;
  _XlaSendFromHostV2OpAdaptor(_XlaSendFromHostV2Op op);

  ::llvm::LogicalResult verify(::mlir::Location loc);
};
class _XlaSendFromHostV2Op : public ::mlir::Op<_XlaSendFromHostV2Op, ::mlir::OpTrait::ZeroRegions, ::mlir::OpTrait::ZeroResults, ::mlir::OpTrait::ZeroSuccessors, ::mlir::OpTrait::AtLeastNOperands<2>::Impl, ::mlir::OpTrait::OpInvariants, ::mlir::BytecodeOpInterface::Trait, GetResourceInstanceInterface::Trait, ::mlir::MemoryEffectOpInterface::Trait, ::mlir::DerivedAttributeOpInterface::Trait> {
public:
  using Op::Op;
  using Op::print;
  using Adaptor = _XlaSendFromHostV2OpAdaptor;
  template <typename RangeT>
  using GenericAdaptor = _XlaSendFromHostV2OpGenericAdaptor<RangeT>;
  using FoldAdaptor = GenericAdaptor<::llvm::ArrayRef<::mlir::Attribute>>;
  using Properties = FoldAdaptor::Properties;
  static ::llvm::ArrayRef<::llvm::StringRef> getAttributeNames() {
    static ::llvm::StringRef attrNames[] = {::llvm::StringRef("Tinputs"), ::llvm::StringRef("device_type"), ::llvm::StringRef("key")};
    return ::llvm::ArrayRef(attrNames);
  }

  ::mlir::StringAttr getTinputsAttrName() {
    return getAttributeNameForIndex(0);
  }

  static ::mlir::StringAttr getTinputsAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 0);
  }

  ::mlir::StringAttr getDeviceTypeAttrName() {
    return getAttributeNameForIndex(1);
  }

  static ::mlir::StringAttr getDeviceTypeAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 1);
  }

  ::mlir::StringAttr getKeyAttrName() {
    return getAttributeNameForIndex(2);
  }

  static ::mlir::StringAttr getKeyAttrName(::mlir::OperationName name) {
    return getAttributeNameForIndex(name, 2);
  }

  static constexpr ::llvm::StringLiteral getOperationName() {
    return ::llvm::StringLiteral("tf._XlaSendFromHostV2");
  }

  std::pair<unsigned, unsigned> getODSOperandIndexAndLength(unsigned index);
  ::mlir::Operation::operand_range getODSOperands(unsigned index) {
    auto valueRange = getODSOperandIndexAndLength(index);
    return {std::next(getOperation()->operand_begin(), valueRange.first),
             std::next(getOperation()->operand_begin(), valueRange.first + valueRange.second)};
  }

  ::mlir::Operation::operand_range getInputs() {
    return getODSOperands(0);
  }

  ::mlir::TypedValue<::mlir::TensorType> getDynamicKey() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(1).begin());
  }

  ::mlir::TypedValue<::mlir::TensorType> getDeviceOrdinal() {
    return ::llvm::cast<::mlir::TypedValue<::mlir::TensorType>>(*getODSOperands(2).begin());
  }

  ::mlir::MutableOperandRange getInputsMutable();
  ::mlir::OpOperand &getDynamicKeyMutable() {
    auto range = getODSOperandIndexAndLength(1);
    return getOperation()->getOpOperand(range.first);
  }

  ::mlir::OpOperand &getDeviceOrdinalMutable() {
    auto range = getODSOperandIndexAndLength(2);
    return getOperation()->getOpOperand(range.first);
  }

  std::pair<unsigned, unsigned> getODSResultIndexAndLength(unsigned index) {
    return {index, 1};
  }

  ::mlir::Operation::result_range getODSResults(unsigned index) {
    auto valueRange = getODSResultIndexAndLength(index);
    return {std::next(getOperation()->result_begin(), valueRange.first),
             std::next(getOperation()->result_begin(), valueRange.first + valueRange.second)};
  }

  static ::llvm::LogicalResult setPropertiesFromAttr(Properties &prop, ::mlir::Attribute attr, ::llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::mlir::Attribute getPropertiesAsAttr(::mlir::MLIRContext *ctx, const Properties &prop);
  static llvm::hash_code computePropertiesHash(const Properties &prop);
  static std::optional<mlir::Attribute> getInherentAttr(::mlir::MLIRContext *ctx, const Properties &prop, llvm::StringRef name);
  static void setInherentAttr(Properties &prop, llvm::StringRef name, mlir::Attribute value);
  static void populateInherentAttrs(::mlir::MLIRContext *ctx, const Properties &prop, ::mlir::NamedAttrList &attrs);
  static ::llvm::LogicalResult verifyInherentAttrs(::mlir::OperationName opName, ::mlir::NamedAttrList &attrs, llvm::function_ref<::mlir::InFlightDiagnostic()> emitError);
  static ::llvm::LogicalResult readProperties(::mlir::DialectBytecodeReader &reader, ::mlir::OperationState &state);
  void writeProperties(::mlir::DialectBytecodeWriter &writer);
  ::mlir::StringAttr getKeyAttr() {
    return ::llvm::cast<::mlir::StringAttr>(getProperties().key);
  }

  ::llvm::StringRef getKey();
  ::mlir::StringAttr getDeviceTypeAttr() {
    return ::llvm::dyn_cast_or_null<::mlir::StringAttr>(getProperties().device_type);
  }

  ::llvm::StringRef getDeviceType();
  mlir::OperandElementTypeRange getTinputs();
  static bool isDerivedAttribute(::llvm::StringRef name);
  ::mlir::DictionaryAttr materializeDerivedAttributes();
  void setKeyAttr(::mlir::StringAttr attr) {
    getProperties().key = attr;
  }

  void setKey(::llvm::StringRef attrValue);
  void setDeviceTypeAttr(::mlir::StringAttr attr) {
    getProperties().device_type = attr;
  }

  void setDeviceType(::std::optional<::llvm::StringRef> attrValue);
  ::mlir::Attribute removeDeviceTypeAttr() {
      auto &attr = getProperties().device_type;
      attr = {};
      return attr;
  }

  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange inputs, ::mlir::Value dynamic_key, ::mlir::Value device_ordinal, ::mlir::StringAttr key, /*optional*/::mlir::StringAttr device_type = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange inputs, ::mlir::Value dynamic_key, ::mlir::Value device_ordinal, ::mlir::StringAttr key, /*optional*/::mlir::StringAttr device_type = nullptr);
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::ValueRange inputs, ::mlir::Value dynamic_key, ::mlir::Value device_ordinal, ::llvm::StringRef key, /*optional*/::llvm::StringRef device_type = "TPU");
  static void build(::mlir::OpBuilder &odsBuilder, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange inputs, ::mlir::Value dynamic_key, ::mlir::Value device_ordinal, ::llvm::StringRef key, /*optional*/::llvm::StringRef device_type = "TPU");
  static void build(::mlir::OpBuilder &, ::mlir::OperationState &odsState, ::mlir::TypeRange resultTypes, ::mlir::ValueRange operands, ::llvm::ArrayRef<::mlir::NamedAttribute> attributes = {});
  ::llvm::LogicalResult verifyInvariantsImpl();
  ::llvm::LogicalResult verifyInvariants();
  std::optional<std::string> GetResourceInstanceStr();
  void getEffects(::llvm::SmallVectorImpl<::mlir::SideEffects::EffectInstance<::mlir::MemoryEffects::Effect>> &effects);
private:
  ::mlir::StringAttr getAttributeNameForIndex(unsigned index) {
    return getAttributeNameForIndex((*this)->getName(), index);
  }

  static ::mlir::StringAttr getAttributeNameForIndex(::mlir::OperationName name, unsigned index) {
    assert(index < 3 && "invalid attribute index");
    assert(name.getStringRef() == getOperationName() && "invalid operation name");
    assert(name.isRegistered() && "Operation isn't registered, missing a "
          "dependent dialect loading?");
    return name.getAttributeNames()[index];
  }

public:
};
} // namespace TF
} // namespace mlir
MLIR_DECLARE_EXPLICIT_TYPE_ID(::mlir::TF::_XlaSendFromHostV2Op)


#endif  // GET_OP_CLASSES

