// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/tf2xla/tf2xla.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
namespace tensorflow {
namespace tf2xla {
class Config;
struct ConfigDefaultTypeInternal;
extern ConfigDefaultTypeInternal _Config_default_instance_;
class Feed;
struct FeedDefaultTypeInternal;
extern FeedDefaultTypeInternal _Feed_default_instance_;
class Fetch;
struct FetchDefaultTypeInternal;
extern FetchDefaultTypeInternal _Fetch_default_instance_;
class TensorId;
struct TensorIdDefaultTypeInternal;
extern TensorIdDefaultTypeInternal _TensorId_default_instance_;
class Variable;
struct VariableDefaultTypeInternal;
extern VariableDefaultTypeInternal _Variable_default_instance_;
}  // namespace tf2xla
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tf2xla::Config* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Config>(Arena*);
template<> ::tensorflow::tf2xla::Feed* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Feed>(Arena*);
template<> ::tensorflow::tf2xla::Fetch* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Fetch>(Arena*);
template<> ::tensorflow::tf2xla::TensorId* Arena::CreateMaybeMessage<::tensorflow::tf2xla::TensorId>(Arena*);
template<> ::tensorflow::tf2xla::Variable* Arena::CreateMaybeMessage<::tensorflow::tf2xla::Variable>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tf2xla {

// ===================================================================

class TensorId final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.TensorId) */ {
 public:
  inline TensorId() : TensorId(nullptr) {}
  ~TensorId() override;
  explicit PROTOBUF_CONSTEXPR TensorId(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorId(const TensorId& from);
  TensorId(TensorId&& from) noexcept
    : TensorId() {
    *this = ::std::move(from);
  }

  inline TensorId& operator=(const TensorId& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorId& operator=(TensorId&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorId& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorId* internal_default_instance() {
    return reinterpret_cast<const TensorId*>(
               &_TensorId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TensorId& a, TensorId& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorId* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorId* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorId* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorId>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorId& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorId& from) {
    TensorId::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorId* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.TensorId";
  }
  protected:
  explicit TensorId(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeNameFieldNumber = 1,
    kOutputIndexFieldNumber = 2,
  };
  // string node_name = 1;
  void clear_node_name();
  const std::string& node_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_node_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_node_name();
  PROTOBUF_NODISCARD std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  private:
  const std::string& _internal_node_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_node_name(const std::string& value);
  std::string* _internal_mutable_node_name();
  public:

  // int64 output_index = 2;
  void clear_output_index();
  int64_t output_index() const;
  void set_output_index(int64_t value);
  private:
  int64_t _internal_output_index() const;
  void _internal_set_output_index(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.TensorId)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
    int64_t output_index_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// -------------------------------------------------------------------

class Feed final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Feed) */ {
 public:
  inline Feed() : Feed(nullptr) {}
  ~Feed() override;
  explicit PROTOBUF_CONSTEXPR Feed(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Feed(const Feed& from);
  Feed(Feed&& from) noexcept
    : Feed() {
    *this = ::std::move(from);
  }

  inline Feed& operator=(const Feed& from) {
    CopyFrom(from);
    return *this;
  }
  inline Feed& operator=(Feed&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Feed& default_instance() {
    return *internal_default_instance();
  }
  static inline const Feed* internal_default_instance() {
    return reinterpret_cast<const Feed*>(
               &_Feed_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Feed& a, Feed& b) {
    a.Swap(&b);
  }
  inline void Swap(Feed* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Feed* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Feed* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Feed>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Feed& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Feed& from) {
    Feed::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Feed* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.Feed";
  }
  protected:
  explicit Feed(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 3,
    kIdFieldNumber = 1,
    kShapeFieldNumber = 2,
    kTypeFieldNumber = 4,
  };
  // string name = 3;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.tf2xla.TensorId id = 1;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  const ::tensorflow::tf2xla::TensorId& id() const;
  PROTOBUF_NODISCARD ::tensorflow::tf2xla::TensorId* release_id();
  ::tensorflow::tf2xla::TensorId* mutable_id();
  void set_allocated_id(::tensorflow::tf2xla::TensorId* id);
  private:
  const ::tensorflow::tf2xla::TensorId& _internal_id() const;
  ::tensorflow::tf2xla::TensorId* _internal_mutable_id();
  public:
  void unsafe_arena_set_allocated_id(
      ::tensorflow::tf2xla::TensorId* id);
  ::tensorflow::tf2xla::TensorId* unsafe_arena_release_id();

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 4;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_type() const;
  void _internal_set_type(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Feed)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::tf2xla::TensorId* id_;
    ::tensorflow::TensorShapeProto* shape_;
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// -------------------------------------------------------------------

class Fetch final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Fetch) */ {
 public:
  inline Fetch() : Fetch(nullptr) {}
  ~Fetch() override;
  explicit PROTOBUF_CONSTEXPR Fetch(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Fetch(const Fetch& from);
  Fetch(Fetch&& from) noexcept
    : Fetch() {
    *this = ::std::move(from);
  }

  inline Fetch& operator=(const Fetch& from) {
    CopyFrom(from);
    return *this;
  }
  inline Fetch& operator=(Fetch&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Fetch& default_instance() {
    return *internal_default_instance();
  }
  static inline const Fetch* internal_default_instance() {
    return reinterpret_cast<const Fetch*>(
               &_Fetch_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Fetch& a, Fetch& b) {
    a.Swap(&b);
  }
  inline void Swap(Fetch* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Fetch* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Fetch* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Fetch>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Fetch& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Fetch& from) {
    Fetch::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Fetch* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.Fetch";
  }
  protected:
  explicit Fetch(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 2,
    kIdFieldNumber = 1,
    kShapeFieldNumber = 3,
    kTypeFieldNumber = 4,
  };
  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.tf2xla.TensorId id = 1;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  const ::tensorflow::tf2xla::TensorId& id() const;
  PROTOBUF_NODISCARD ::tensorflow::tf2xla::TensorId* release_id();
  ::tensorflow::tf2xla::TensorId* mutable_id();
  void set_allocated_id(::tensorflow::tf2xla::TensorId* id);
  private:
  const ::tensorflow::tf2xla::TensorId& _internal_id() const;
  ::tensorflow::tf2xla::TensorId* _internal_mutable_id();
  public:
  void unsafe_arena_set_allocated_id(
      ::tensorflow::tf2xla::TensorId* id);
  ::tensorflow::tf2xla::TensorId* unsafe_arena_release_id();

  // .tensorflow.TensorShapeProto shape = 3;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 4;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_type() const;
  void _internal_set_type(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Fetch)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::tf2xla::TensorId* id_;
    ::tensorflow::TensorShapeProto* shape_;
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// -------------------------------------------------------------------

class Variable final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Variable) */ {
 public:
  inline Variable() : Variable(nullptr) {}
  ~Variable() override;
  explicit PROTOBUF_CONSTEXPR Variable(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Variable(const Variable& from);
  Variable(Variable&& from) noexcept
    : Variable() {
    *this = ::std::move(from);
  }

  inline Variable& operator=(const Variable& from) {
    CopyFrom(from);
    return *this;
  }
  inline Variable& operator=(Variable&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Variable& default_instance() {
    return *internal_default_instance();
  }
  static inline const Variable* internal_default_instance() {
    return reinterpret_cast<const Variable*>(
               &_Variable_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Variable& a, Variable& b) {
    a.Swap(&b);
  }
  inline void Swap(Variable* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Variable* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Variable* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Variable>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Variable& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Variable& from) {
    Variable::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Variable* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.Variable";
  }
  protected:
  explicit Variable(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeNameFieldNumber = 1,
    kNameFieldNumber = 2,
    kShapeFieldNumber = 3,
    kTypeFieldNumber = 4,
    kReadonlyFieldNumber = 5,
  };
  // string node_name = 1;
  void clear_node_name();
  const std::string& node_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_node_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_node_name();
  PROTOBUF_NODISCARD std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  private:
  const std::string& _internal_node_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_node_name(const std::string& value);
  std::string* _internal_mutable_node_name();
  public:

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.TensorShapeProto shape = 3;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType type = 4;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_type() const;
  void _internal_set_type(::tensorflow::DataType value);
  public:

  // bool readonly = 5;
  void clear_readonly();
  bool readonly() const;
  void set_readonly(bool value);
  private:
  bool _internal_readonly() const;
  void _internal_set_readonly(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Variable)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::TensorShapeProto* shape_;
    int type_;
    bool readonly_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// -------------------------------------------------------------------

class Config final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.Config) */ {
 public:
  inline Config() : Config(nullptr) {}
  ~Config() override;
  explicit PROTOBUF_CONSTEXPR Config(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Config(const Config& from);
  Config(Config&& from) noexcept
    : Config() {
    *this = ::std::move(from);
  }

  inline Config& operator=(const Config& from) {
    CopyFrom(from);
    return *this;
  }
  inline Config& operator=(Config&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Config& default_instance() {
    return *internal_default_instance();
  }
  static inline const Config* internal_default_instance() {
    return reinterpret_cast<const Config*>(
               &_Config_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Config& a, Config& b) {
    a.Swap(&b);
  }
  inline void Swap(Config* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Config* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Config* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Config>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Config& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Config& from) {
    Config::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Config* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.Config";
  }
  protected:
  explicit Config(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeedFieldNumber = 1,
    kFetchFieldNumber = 2,
    kVariableFieldNumber = 3,
  };
  // repeated .tensorflow.tf2xla.Feed feed = 1;
  int feed_size() const;
  private:
  int _internal_feed_size() const;
  public:
  void clear_feed();
  ::tensorflow::tf2xla::Feed* mutable_feed(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Feed >*
      mutable_feed();
  private:
  const ::tensorflow::tf2xla::Feed& _internal_feed(int index) const;
  ::tensorflow::tf2xla::Feed* _internal_add_feed();
  public:
  const ::tensorflow::tf2xla::Feed& feed(int index) const;
  ::tensorflow::tf2xla::Feed* add_feed();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Feed >&
      feed() const;

  // repeated .tensorflow.tf2xla.Fetch fetch = 2;
  int fetch_size() const;
  private:
  int _internal_fetch_size() const;
  public:
  void clear_fetch();
  ::tensorflow::tf2xla::Fetch* mutable_fetch(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >*
      mutable_fetch();
  private:
  const ::tensorflow::tf2xla::Fetch& _internal_fetch(int index) const;
  ::tensorflow::tf2xla::Fetch* _internal_add_fetch();
  public:
  const ::tensorflow::tf2xla::Fetch& fetch(int index) const;
  ::tensorflow::tf2xla::Fetch* add_fetch();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >&
      fetch() const;

  // repeated .tensorflow.tf2xla.Variable variable = 3;
  int variable_size() const;
  private:
  int _internal_variable_size() const;
  public:
  void clear_variable();
  ::tensorflow::tf2xla::Variable* mutable_variable(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Variable >*
      mutable_variable();
  private:
  const ::tensorflow::tf2xla::Variable& _internal_variable(int index) const;
  ::tensorflow::tf2xla::Variable* _internal_add_variable();
  public:
  const ::tensorflow::tf2xla::Variable& variable(int index) const;
  ::tensorflow::tf2xla::Variable* add_variable();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Variable >&
      variable() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.Config)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Feed > feed_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Fetch > fetch_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Variable > variable_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorId

// string node_name = 1;
inline void TensorId::clear_node_name() {
  _impl_.node_name_.ClearToEmpty();
}
inline const std::string& TensorId::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorId.node_name)
  return _internal_node_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorId::set_node_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.node_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.TensorId.node_name)
}
inline std::string* TensorId::mutable_node_name() {
  std::string* _s = _internal_mutable_node_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.TensorId.node_name)
  return _s;
}
inline const std::string& TensorId::_internal_node_name() const {
  return _impl_.node_name_.Get();
}
inline void TensorId::_internal_set_node_name(const std::string& value) {
  
  _impl_.node_name_.Set(value, GetArenaForAllocation());
}
inline std::string* TensorId::_internal_mutable_node_name() {
  
  return _impl_.node_name_.Mutable(GetArenaForAllocation());
}
inline std::string* TensorId::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.TensorId.node_name)
  return _impl_.node_name_.Release();
}
inline void TensorId::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  _impl_.node_name_.SetAllocated(node_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.node_name_.IsDefault()) {
    _impl_.node_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.TensorId.node_name)
}

// int64 output_index = 2;
inline void TensorId::clear_output_index() {
  _impl_.output_index_ = int64_t{0};
}
inline int64_t TensorId::_internal_output_index() const {
  return _impl_.output_index_;
}
inline int64_t TensorId::output_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorId.output_index)
  return _internal_output_index();
}
inline void TensorId::_internal_set_output_index(int64_t value) {
  
  _impl_.output_index_ = value;
}
inline void TensorId::set_output_index(int64_t value) {
  _internal_set_output_index(value);
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.TensorId.output_index)
}

// -------------------------------------------------------------------

// Feed

// .tensorflow.tf2xla.TensorId id = 1;
inline bool Feed::_internal_has_id() const {
  return this != internal_default_instance() && _impl_.id_ != nullptr;
}
inline bool Feed::has_id() const {
  return _internal_has_id();
}
inline void Feed::clear_id() {
  if (GetArenaForAllocation() == nullptr && _impl_.id_ != nullptr) {
    delete _impl_.id_;
  }
  _impl_.id_ = nullptr;
}
inline const ::tensorflow::tf2xla::TensorId& Feed::_internal_id() const {
  const ::tensorflow::tf2xla::TensorId* p = _impl_.id_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tf2xla::TensorId&>(
      ::tensorflow::tf2xla::_TensorId_default_instance_);
}
inline const ::tensorflow::tf2xla::TensorId& Feed::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.id)
  return _internal_id();
}
inline void Feed::unsafe_arena_set_allocated_id(
    ::tensorflow::tf2xla::TensorId* id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.id_);
  }
  _impl_.id_ = id;
  if (id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Feed.id)
}
inline ::tensorflow::tf2xla::TensorId* Feed::release_id() {
  
  ::tensorflow::tf2xla::TensorId* temp = _impl_.id_;
  _impl_.id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Feed::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Feed.id)
  
  ::tensorflow::tf2xla::TensorId* temp = _impl_.id_;
  _impl_.id_ = nullptr;
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Feed::_internal_mutable_id() {
  
  if (_impl_.id_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tf2xla::TensorId>(GetArenaForAllocation());
    _impl_.id_ = p;
  }
  return _impl_.id_;
}
inline ::tensorflow::tf2xla::TensorId* Feed::mutable_id() {
  ::tensorflow::tf2xla::TensorId* _msg = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Feed.id)
  return _msg;
}
inline void Feed::set_allocated_id(::tensorflow::tf2xla::TensorId* id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.id_;
  }
  if (id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(id);
    if (message_arena != submessage_arena) {
      id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.id_ = id;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Feed.id)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool Feed::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool Feed::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& Feed::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& Feed::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.shape)
  return _internal_shape();
}
inline void Feed::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Feed.shape)
}
inline ::tensorflow::TensorShapeProto* Feed::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* Feed::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Feed.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Feed::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* Feed::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Feed.shape)
  return _msg;
}
inline void Feed::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Feed.shape)
}

// string name = 3;
inline void Feed::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Feed::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Feed::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Feed.name)
}
inline std::string* Feed::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Feed.name)
  return _s;
}
inline const std::string& Feed::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Feed::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Feed::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Feed::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Feed.name)
  return _impl_.name_.Release();
}
inline void Feed::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Feed.name)
}

// .tensorflow.DataType type = 4;
inline void Feed::clear_type() {
  _impl_.type_ = 0;
}
inline ::tensorflow::DataType Feed::_internal_type() const {
  return static_cast< ::tensorflow::DataType >(_impl_.type_);
}
inline ::tensorflow::DataType Feed::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Feed.type)
  return _internal_type();
}
inline void Feed::_internal_set_type(::tensorflow::DataType value) {
  
  _impl_.type_ = value;
}
inline void Feed::set_type(::tensorflow::DataType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Feed.type)
}

// -------------------------------------------------------------------

// Fetch

// .tensorflow.tf2xla.TensorId id = 1;
inline bool Fetch::_internal_has_id() const {
  return this != internal_default_instance() && _impl_.id_ != nullptr;
}
inline bool Fetch::has_id() const {
  return _internal_has_id();
}
inline void Fetch::clear_id() {
  if (GetArenaForAllocation() == nullptr && _impl_.id_ != nullptr) {
    delete _impl_.id_;
  }
  _impl_.id_ = nullptr;
}
inline const ::tensorflow::tf2xla::TensorId& Fetch::_internal_id() const {
  const ::tensorflow::tf2xla::TensorId* p = _impl_.id_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tf2xla::TensorId&>(
      ::tensorflow::tf2xla::_TensorId_default_instance_);
}
inline const ::tensorflow::tf2xla::TensorId& Fetch::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Fetch.id)
  return _internal_id();
}
inline void Fetch::unsafe_arena_set_allocated_id(
    ::tensorflow::tf2xla::TensorId* id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.id_);
  }
  _impl_.id_ = id;
  if (id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Fetch.id)
}
inline ::tensorflow::tf2xla::TensorId* Fetch::release_id() {
  
  ::tensorflow::tf2xla::TensorId* temp = _impl_.id_;
  _impl_.id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Fetch::unsafe_arena_release_id() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Fetch.id)
  
  ::tensorflow::tf2xla::TensorId* temp = _impl_.id_;
  _impl_.id_ = nullptr;
  return temp;
}
inline ::tensorflow::tf2xla::TensorId* Fetch::_internal_mutable_id() {
  
  if (_impl_.id_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tf2xla::TensorId>(GetArenaForAllocation());
    _impl_.id_ = p;
  }
  return _impl_.id_;
}
inline ::tensorflow::tf2xla::TensorId* Fetch::mutable_id() {
  ::tensorflow::tf2xla::TensorId* _msg = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Fetch.id)
  return _msg;
}
inline void Fetch::set_allocated_id(::tensorflow::tf2xla::TensorId* id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.id_;
  }
  if (id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(id);
    if (message_arena != submessage_arena) {
      id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, id, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.id_ = id;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Fetch.id)
}

// string name = 2;
inline void Fetch::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Fetch::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Fetch.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Fetch::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Fetch.name)
}
inline std::string* Fetch::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Fetch.name)
  return _s;
}
inline const std::string& Fetch::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Fetch::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Fetch::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Fetch::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Fetch.name)
  return _impl_.name_.Release();
}
inline void Fetch::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Fetch.name)
}

// .tensorflow.TensorShapeProto shape = 3;
inline bool Fetch::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool Fetch::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& Fetch::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& Fetch::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Fetch.shape)
  return _internal_shape();
}
inline void Fetch::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Fetch.shape)
}
inline ::tensorflow::TensorShapeProto* Fetch::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* Fetch::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Fetch.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Fetch::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* Fetch::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Fetch.shape)
  return _msg;
}
inline void Fetch::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Fetch.shape)
}

// .tensorflow.DataType type = 4;
inline void Fetch::clear_type() {
  _impl_.type_ = 0;
}
inline ::tensorflow::DataType Fetch::_internal_type() const {
  return static_cast< ::tensorflow::DataType >(_impl_.type_);
}
inline ::tensorflow::DataType Fetch::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Fetch.type)
  return _internal_type();
}
inline void Fetch::_internal_set_type(::tensorflow::DataType value) {
  
  _impl_.type_ = value;
}
inline void Fetch::set_type(::tensorflow::DataType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Fetch.type)
}

// -------------------------------------------------------------------

// Variable

// string node_name = 1;
inline void Variable::clear_node_name() {
  _impl_.node_name_.ClearToEmpty();
}
inline const std::string& Variable::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.node_name)
  return _internal_node_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Variable::set_node_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.node_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.node_name)
}
inline std::string* Variable::mutable_node_name() {
  std::string* _s = _internal_mutable_node_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Variable.node_name)
  return _s;
}
inline const std::string& Variable::_internal_node_name() const {
  return _impl_.node_name_.Get();
}
inline void Variable::_internal_set_node_name(const std::string& value) {
  
  _impl_.node_name_.Set(value, GetArenaForAllocation());
}
inline std::string* Variable::_internal_mutable_node_name() {
  
  return _impl_.node_name_.Mutable(GetArenaForAllocation());
}
inline std::string* Variable::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Variable.node_name)
  return _impl_.node_name_.Release();
}
inline void Variable::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  _impl_.node_name_.SetAllocated(node_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.node_name_.IsDefault()) {
    _impl_.node_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Variable.node_name)
}

// string name = 2;
inline void Variable::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Variable::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Variable::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.name)
}
inline std::string* Variable::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Variable.name)
  return _s;
}
inline const std::string& Variable::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Variable::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Variable::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Variable::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Variable.name)
  return _impl_.name_.Release();
}
inline void Variable::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Variable.name)
}

// .tensorflow.TensorShapeProto shape = 3;
inline bool Variable::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool Variable::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& Variable::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& Variable::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.shape)
  return _internal_shape();
}
inline void Variable::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.Variable.shape)
}
inline ::tensorflow::TensorShapeProto* Variable::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* Variable::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.Variable.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* Variable::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* Variable::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Variable.shape)
  return _msg;
}
inline void Variable::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.Variable.shape)
}

// .tensorflow.DataType type = 4;
inline void Variable::clear_type() {
  _impl_.type_ = 0;
}
inline ::tensorflow::DataType Variable::_internal_type() const {
  return static_cast< ::tensorflow::DataType >(_impl_.type_);
}
inline ::tensorflow::DataType Variable::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.type)
  return _internal_type();
}
inline void Variable::_internal_set_type(::tensorflow::DataType value) {
  
  _impl_.type_ = value;
}
inline void Variable::set_type(::tensorflow::DataType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.type)
}

// bool readonly = 5;
inline void Variable::clear_readonly() {
  _impl_.readonly_ = false;
}
inline bool Variable::_internal_readonly() const {
  return _impl_.readonly_;
}
inline bool Variable::readonly() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Variable.readonly)
  return _internal_readonly();
}
inline void Variable::_internal_set_readonly(bool value) {
  
  _impl_.readonly_ = value;
}
inline void Variable::set_readonly(bool value) {
  _internal_set_readonly(value);
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.Variable.readonly)
}

// -------------------------------------------------------------------

// Config

// repeated .tensorflow.tf2xla.Feed feed = 1;
inline int Config::_internal_feed_size() const {
  return _impl_.feed_.size();
}
inline int Config::feed_size() const {
  return _internal_feed_size();
}
inline void Config::clear_feed() {
  _impl_.feed_.Clear();
}
inline ::tensorflow::tf2xla::Feed* Config::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Config.feed)
  return _impl_.feed_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Feed >*
Config::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.Config.feed)
  return &_impl_.feed_;
}
inline const ::tensorflow::tf2xla::Feed& Config::_internal_feed(int index) const {
  return _impl_.feed_.Get(index);
}
inline const ::tensorflow::tf2xla::Feed& Config::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Config.feed)
  return _internal_feed(index);
}
inline ::tensorflow::tf2xla::Feed* Config::_internal_add_feed() {
  return _impl_.feed_.Add();
}
inline ::tensorflow::tf2xla::Feed* Config::add_feed() {
  ::tensorflow::tf2xla::Feed* _add = _internal_add_feed();
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.Config.feed)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Feed >&
Config::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.Config.feed)
  return _impl_.feed_;
}

// repeated .tensorflow.tf2xla.Fetch fetch = 2;
inline int Config::_internal_fetch_size() const {
  return _impl_.fetch_.size();
}
inline int Config::fetch_size() const {
  return _internal_fetch_size();
}
inline void Config::clear_fetch() {
  _impl_.fetch_.Clear();
}
inline ::tensorflow::tf2xla::Fetch* Config::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Config.fetch)
  return _impl_.fetch_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >*
Config::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.Config.fetch)
  return &_impl_.fetch_;
}
inline const ::tensorflow::tf2xla::Fetch& Config::_internal_fetch(int index) const {
  return _impl_.fetch_.Get(index);
}
inline const ::tensorflow::tf2xla::Fetch& Config::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Config.fetch)
  return _internal_fetch(index);
}
inline ::tensorflow::tf2xla::Fetch* Config::_internal_add_fetch() {
  return _impl_.fetch_.Add();
}
inline ::tensorflow::tf2xla::Fetch* Config::add_fetch() {
  ::tensorflow::tf2xla::Fetch* _add = _internal_add_fetch();
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.Config.fetch)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Fetch >&
Config::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.Config.fetch)
  return _impl_.fetch_;
}

// repeated .tensorflow.tf2xla.Variable variable = 3;
inline int Config::_internal_variable_size() const {
  return _impl_.variable_.size();
}
inline int Config::variable_size() const {
  return _internal_variable_size();
}
inline void Config::clear_variable() {
  _impl_.variable_.Clear();
}
inline ::tensorflow::tf2xla::Variable* Config::mutable_variable(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.Config.variable)
  return _impl_.variable_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Variable >*
Config::mutable_variable() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.Config.variable)
  return &_impl_.variable_;
}
inline const ::tensorflow::tf2xla::Variable& Config::_internal_variable(int index) const {
  return _impl_.variable_.Get(index);
}
inline const ::tensorflow::tf2xla::Variable& Config::variable(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.Config.variable)
  return _internal_variable(index);
}
inline ::tensorflow::tf2xla::Variable* Config::_internal_add_variable() {
  return _impl_.variable_.Add();
}
inline ::tensorflow::tf2xla::Variable* Config::add_variable() {
  ::tensorflow::tf2xla::Variable* _add = _internal_add_variable();
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.Config.variable)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::Variable >&
Config::variable() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.Config.variable)
  return _impl_.variable_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tf2xla
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2ftf2xla_2eproto
