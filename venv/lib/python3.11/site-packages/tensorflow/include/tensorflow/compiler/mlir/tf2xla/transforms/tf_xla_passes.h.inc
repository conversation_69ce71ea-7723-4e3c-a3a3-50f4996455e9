/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_LEGALIZETFCOMMUNICATIONPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// LegalizeTFCommunicationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LEGALIZETFCOMMUNICATIONPASS
#undef GEN_PASS_DECL_LEGALIZETFCOMMUNICATIONPASS
#endif // GEN_PA<PERSON>_<PERSON>CL_LEGALIZETFCOMMUNICATIONPASS
#ifdef GEN_PASS_DEF_LEGALIZETFCOMMUNICATIONPASS
namespace impl {

template <typename DerivedT>
class LegalizeTFCommunicationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFCommunicationPassBase;

  LegalizeTFCommunicationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFCommunicationPassBase(const LegalizeTFCommunicationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LegalizeTFCommunicationPassBase& operator=(const LegalizeTFCommunicationPassBase &) = delete;
  LegalizeTFCommunicationPassBase(LegalizeTFCommunicationPassBase &&) = delete;
  LegalizeTFCommunicationPassBase& operator=(LegalizeTFCommunicationPassBase &&) = delete;
  ~LegalizeTFCommunicationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-communication");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-communication"; }

  ::llvm::StringRef getDescription() const override { return "Legalize TF/XLA communication ops (TensorFlow dialect) to the HLO dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFCommunicationPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFCommunicationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<sparse_tensor::SparseTensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFCommunicationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LEGALIZETFCOMMUNICATIONPASS
#endif // GEN_PASS_DEF_LEGALIZETFCOMMUNICATIONPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// LegalizeTFCommunicationPass Registration
//===----------------------------------------------------------------------===//

inline void registerLegalizeTFCommunicationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::mhlo::CreateLegalizeTFCommunicationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLegalizeTFCommunicationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return ::mlir::mhlo::CreateLegalizeTFCommunicationPass();
  });
}

//===----------------------------------------------------------------------===//
// TfXla Registration
//===----------------------------------------------------------------------===//

inline void registerTfXlaPasses() {
  registerLegalizeTFCommunicationPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class LegalizeTFCommunicationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = LegalizeTFCommunicationPassBase;

  LegalizeTFCommunicationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  LegalizeTFCommunicationPassBase(const LegalizeTFCommunicationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  LegalizeTFCommunicationPassBase& operator=(const LegalizeTFCommunicationPassBase &) = delete;
  LegalizeTFCommunicationPassBase(LegalizeTFCommunicationPassBase &&) = delete;
  LegalizeTFCommunicationPassBase& operator=(LegalizeTFCommunicationPassBase &&) = delete;
  ~LegalizeTFCommunicationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("xla-legalize-tf-communication");
  }
  ::llvm::StringRef getArgument() const override { return "xla-legalize-tf-communication"; }

  ::llvm::StringRef getDescription() const override { return "Legalize TF/XLA communication ops (TensorFlow dialect) to the HLO dialect"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LegalizeTFCommunicationPass");
  }
  ::llvm::StringRef getName() const override { return "LegalizeTFCommunicationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mhlo::MhloDialect>();
    registry.insert<sparse_tensor::SparseTensorDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LegalizeTFCommunicationPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
