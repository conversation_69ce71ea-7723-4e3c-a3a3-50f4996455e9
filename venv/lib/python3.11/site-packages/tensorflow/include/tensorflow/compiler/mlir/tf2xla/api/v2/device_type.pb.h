// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/mlir/tf2xla/api/v2/device_type.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2ftf2xla_2fapi_2fv2_2fdevice_5ftype_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2ftf2xla_2fapi_2fv2_2fdevice_5ftype_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fmlir_2ftf2xla_2fapi_2fv2_2fdevice_5ftype_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fmlir_2ftf2xla_2fapi_2fv2_2fdevice_5ftype_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fmlir_2ftf2xla_2fapi_2fv2_2fdevice_5ftype_2eproto;
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tf2xla {
namespace v2 {

enum DeviceType : int {
  DEVICE_TYPE_UNSPECIFIED = 0,
  XLA_TPU_JIT = 1,
  XLA_CPU_JIT = 2,
  XLA_GPU_JIT = 3
};
bool DeviceType_IsValid(int value);
constexpr DeviceType DeviceType_MIN = DEVICE_TYPE_UNSPECIFIED;
constexpr DeviceType DeviceType_MAX = XLA_GPU_JIT;
constexpr int DeviceType_ARRAYSIZE = DeviceType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DeviceType_descriptor();
template<typename T>
inline const std::string& DeviceType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DeviceType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DeviceType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DeviceType_descriptor(), enum_t_value);
}
inline bool DeviceType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DeviceType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DeviceType>(
    DeviceType_descriptor(), name, value);
}
// ===================================================================


// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace v2
}  // namespace tf2xla
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::tf2xla::v2::DeviceType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tf2xla::v2::DeviceType>() {
  return ::tensorflow::tf2xla::v2::DeviceType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2ftf2xla_2fapi_2fv2_2fdevice_5ftype_2eproto
