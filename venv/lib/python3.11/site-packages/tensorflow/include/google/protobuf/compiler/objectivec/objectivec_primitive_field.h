// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

#ifndef GOOGLE_PROTOBUF_COMPILER_OBJECTIVEC_PRIMITIVE_FIELD_H__
#define GOOGLE_PROTOBUF_COMPILER_OBJECTIVEC_PRIMITIVE_FIELD_H__

#include <google/protobuf/compiler/objectivec/objectivec_field.h>

namespace google {
namespace protobuf {
namespace compiler {
namespace objectivec {

class PrimitiveFieldGenerator : public SingleFieldGenerator {
  friend FieldGenerator* FieldGenerator::Make(const FieldDescriptor* field);

 protected:
  explicit PrimitiveFieldGenerator(const FieldDescriptor* descriptor);
  virtual ~PrimitiveFieldGenerator();

  PrimitiveFieldGenerator(const PrimitiveFieldGenerator&) = delete;
  PrimitiveFieldGenerator& operator=(const PrimitiveFieldGenerator&) = delete;

  virtual void GenerateFieldStorageDeclaration(io::Printer* printer) const override;

  virtual int ExtraRuntimeHasBitsNeeded(void) const override;
  virtual void SetExtraRuntimeHasBitsBase(int index_base) override;
};

class PrimitiveObjFieldGenerator : public ObjCObjFieldGenerator {
  friend FieldGenerator* FieldGenerator::Make(const FieldDescriptor* field);

 protected:
  explicit PrimitiveObjFieldGenerator(const FieldDescriptor* descriptor);
  virtual ~PrimitiveObjFieldGenerator();

  PrimitiveObjFieldGenerator(const PrimitiveObjFieldGenerator&) = delete;
  PrimitiveObjFieldGenerator& operator=(const PrimitiveObjFieldGenerator&) =
      delete;
};

class RepeatedPrimitiveFieldGenerator : public RepeatedFieldGenerator {
  friend FieldGenerator* FieldGenerator::Make(const FieldDescriptor* field);

 protected:
  explicit RepeatedPrimitiveFieldGenerator(const FieldDescriptor* descriptor);
  virtual ~RepeatedPrimitiveFieldGenerator();

  RepeatedPrimitiveFieldGenerator(const RepeatedPrimitiveFieldGenerator&) =
      delete;
  RepeatedPrimitiveFieldGenerator& operator=(
      const RepeatedPrimitiveFieldGenerator&) = delete;
};

}  // namespace objectivec
}  // namespace compiler
}  // namespace protobuf
}  // namespace google

#endif  // GOOGLE_PROTOBUF_COMPILER_OBJECTIVEC_PRIMITIVE_FIELD_H__
