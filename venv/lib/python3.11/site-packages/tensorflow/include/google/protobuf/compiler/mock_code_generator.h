// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Kenton Varda)

#ifndef GOOGLE_PROTOBUF_COMPILER_MOCK_CODE_GENERATOR_H__
#define GOOGLE_PROTOBUF_COMPILER_MOCK_CODE_GENERATOR_H__

#include <cstdint>
#include <string>

#include <google/protobuf/compiler/code_generator.h>

namespace google {
namespace protobuf {
class FileDescriptor;
}  // namespace protobuf
}  // namespace google

namespace google {
namespace protobuf {
namespace compiler {

// A mock CodeGenerator, used by command_line_interface_unittest.  This is in
// its own file so that it can be used both directly and as a plugin.
//
// Generate() produces some output which can be checked by ExpectCalled().  The
// generator can run in a different process (e.g. a plugin).
//
// If the parameter is "insert=NAMES", the MockCodeGenerator will insert lines
// into the files generated by other MockCodeGenerators instead of creating
// its own file.  NAMES is a comma-separated list of the names of those other
// MockCodeGenerators.  If the parameter is "insert_endlines=NAMES", the
// MockCodeGenerator will insert data guaranteed to contain more than one
// endline into the files generated by NAMES.
//
// MockCodeGenerator will also modify its behavior slightly if the input file
// contains a message type with one of the following names:
//   MockCodeGenerator_Error:  Causes Generate() to return false and set the
//     error message to "Saw message type MockCodeGenerator_Error."
//   MockCodeGenerator_Exit:  Generate() prints "Saw message type
//     MockCodeGenerator_Exit." to stderr and then calls exit(123).
//   MockCodeGenerator_Abort:  Generate() prints "Saw message type
//     MockCodeGenerator_Abort." to stderr and then calls abort().
//   MockCodeGenerator_HasSourceCodeInfo:  Causes Generate() to abort after
//     printing "Saw message type MockCodeGenerator_HasSourceCodeInfo: FOO." to
//     stderr, where FOO is "1" if the supplied FileDescriptorProto has source
//     code info, and "0" otherwise.
//   MockCodeGenerator_Annotate:  Generate() will add annotations to its output
//     that can later be verified with CheckGeneratedAnnotations.
class MockCodeGenerator : public CodeGenerator {
 public:
  MockCodeGenerator(const std::string& name);
  ~MockCodeGenerator() override;

  // Expect (via gTest) that a MockCodeGenerator with the given name was called
  // with the given parameters by inspecting the output location.
  //
  // |insertions| is a comma-separated list of names of MockCodeGenerators which
  // should have inserted lines into this file.
  // |parsed_file_list| is a comma-separated list of names of the files
  // that are being compiled together in this run.
  static void ExpectGenerated(const std::string& name,
                              const std::string& parameter,
                              const std::string& insertions,
                              const std::string& file,
                              const std::string& first_message_name,
                              const std::string& parsed_file_list,
                              const std::string& output_directory);

  // Checks that the correct text ranges were annotated by the
  // MockCodeGenerator_Annotate directive.
  static void CheckGeneratedAnnotations(const std::string& name,
                                        const std::string& file,
                                        const std::string& output_directory);

  // Get the name of the file which would be written by the given generator.
  static std::string GetOutputFileName(const std::string& generator_name,
                                       const FileDescriptor* file);
  static std::string GetOutputFileName(const std::string& generator_name,
                                       const std::string& file);

  // implements CodeGenerator ----------------------------------------

  bool Generate(const FileDescriptor* file, const std::string& parameter,
                GeneratorContext* context, std::string* error) const override;

  uint64_t GetSupportedFeatures() const override;
  void SuppressFeatures(uint64_t features);

 private:
  std::string name_;
  uint64_t suppressed_features_ = 0;

  static std::string GetOutputFileContent(const std::string& generator_name,
                                          const std::string& parameter,
                                          const FileDescriptor* file,
                                          GeneratorContext* context);
  static std::string GetOutputFileContent(
      const std::string& generator_name, const std::string& parameter,
      const std::string& file, const std::string& parsed_file_list,
      const std::string& first_message_name);
};

}  // namespace compiler
}  // namespace protobuf
}  // namespace google

#endif  // GOOGLE_PROTOBUF_COMPILER_MOCK_CODE_GENERATOR_H__
